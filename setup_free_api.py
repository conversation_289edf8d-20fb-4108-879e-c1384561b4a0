#!/usr/bin/env python3
"""
🚀 FREE Screen Activity Recognition Setup Guide
===============================================

This script helps you set up FREE alternatives to OpenAI for screen activity recognition.

🔥 BEST FREE OPTIONS:

1. 🟢 GOOGLE GEMINI (RECOMMENDED)
   - ✅ 15 requests/minute FREE
   - ✅ Excellent vision capabilities  
   - ✅ Easy setup
   - 🔗 Get free key: https://makersuite.google.com/app/apikey

2. 🟡 OLLAMA + LLAVA (100% FREE & LOCAL)
   - ✅ Completely offline
   - ✅ No API costs ever
   - ⚠️ Requires local installation
   - 🔗 Install: https://ollama.ai/

3. 🟡 HUGGING FACE TRANSFORMERS
   - ✅ Free tier available
   - ✅ Good for basic image understanding
   - ⚠️ Limited compared to others

"""

import webbrowser
import os

def setup_gemini():
    """Setup Google Gemini (RECOMMENDED)"""
    print("🚀 Setting up Google Gemini (FREE)")
    print("=" * 50)
    print("1. 🔗 Opening Gemini API key page...")
    webbrowser.open("https://makersuite.google.com/app/apikey")
    
    print("\n2. 📝 Follow these steps:")
    print("   • Click 'Create API Key'")
    print("   • Copy your free API key")
    print("   • Paste it below")
    
    api_key = input("\n🔑 Enter your Gemini API key: ").strip()
    
    if api_key and api_key != "":
        # Update the OCRscript.py file
        with open("OCRscript.py", "r") as f:
            content = f.read()
        
        content = content.replace(
            'GEMINI_API_KEY = "YOUR_FREE_GEMINI_API_KEY_HERE"',
            f'GEMINI_API_KEY = "{api_key}"'
        )
        
        with open("OCRscript.py", "w") as f:
            f.write(content)
        
        print("✅ Gemini API key configured!")
        print("🎉 You can now run: python OCRscript.py")
        return True
    else:
        print("❌ No API key provided")
        return False

def setup_ollama():
    """Setup Ollama (100% FREE & LOCAL)"""
    print("🚀 Setting up Ollama (100% FREE & LOCAL)")
    print("=" * 50)
    print("1. 🔗 Opening Ollama website...")
    webbrowser.open("https://ollama.ai/")
    
    print("\n2. 📝 Installation steps:")
    print("   • Download Ollama for your OS")
    print("   • Install it")
    print("   • Run: ollama pull llava:7b")
    print("   • This will download the vision model (~4GB)")
    
    print("\n3. 🔧 Update OCRscript.py:")
    print("   • Change USE_GEMINI = False")
    print("   • Change USE_OLLAMA = True")
    
    input("\nPress Enter when you've completed the installation...")
    
    # Test if Ollama is available
    import subprocess
    try:
        result = subprocess.run(["ollama", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Ollama is installed!")
            
            # Update the script
            with open("OCRscript.py", "r") as f:
                content = f.read()
            
            content = content.replace("USE_GEMINI = True", "USE_GEMINI = False")
            content = content.replace("USE_OLLAMA = False", "USE_OLLAMA = True")
            
            with open("OCRscript.py", "w") as f:
                f.write(content)
            
            print("🎉 Ollama configured! Run: python OCRscript.py")
            return True
        else:
            print("❌ Ollama not found. Please install it first.")
            return False
    except FileNotFoundError:
        print("❌ Ollama not found. Please install it first.")
        return False

def setup_huggingface():
    """Setup Hugging Face Transformers"""
    print("🚀 Setting up Hugging Face Transformers")
    print("=" * 50)
    print("1. 📦 Installing required packages...")
    
    import subprocess
    try:
        subprocess.run(["pip", "install", "transformers", "torch", "torchvision"], check=True)
        print("✅ Packages installed!")
        
        # Update the script
        with open("OCRscript.py", "r") as f:
            content = f.read()
        
        content = content.replace("USE_GEMINI = True", "USE_GEMINI = False")
        content = content.replace("USE_HUGGINGFACE = False", "USE_HUGGINGFACE = True")
        
        with open("OCRscript.py", "w") as f:
            f.write(content)
        
        print("🎉 Hugging Face configured! Run: python OCRscript.py")
        return True
        
    except subprocess.CalledProcessError:
        print("❌ Failed to install packages")
        return False

def main():
    print("🎯 FREE Screen Activity Recognition Setup")
    print("🤖 Choose your preferred FREE method:")
    print("\n" + "=" * 60)
    
    print("1. 🟢 Google Gemini (RECOMMENDED)")
    print("   • 15 requests/minute FREE")
    print("   • Best accuracy")
    print("   • Easy setup")
    
    print("\n2. 🟡 Ollama + LLaVA (100% FREE & LOCAL)")
    print("   • Completely offline")
    print("   • No API limits")
    print("   • Requires ~4GB download")
    
    print("\n3. 🟡 Hugging Face Transformers")
    print("   • Free tier")
    print("   • Basic image understanding")
    print("   • Good for simple tasks")
    
    choice = input("\nEnter choice (1, 2, or 3): ").strip()
    
    if choice == "1":
        setup_gemini()
    elif choice == "2":
        setup_ollama()
    elif choice == "3":
        setup_huggingface()
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()

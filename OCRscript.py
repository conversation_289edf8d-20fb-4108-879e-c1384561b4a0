import cv2
import pytesseract
pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
import mss
import numpy as np

from PIL import Image

with mss.mss() as sct:
    monitor = sct.monitors[1]  # 1 is the main monitor
    screenshot = sct.grab(monitor)
    img_bgra = np.array(screenshot)

    # Convert BGRA to BGR for OpenCV processing
    img_bgr = cv2.cvtColor(img_bgra, cv2.COLOR_BGRA2BGR)

    # Display the original screenshot
    cv2.imshow("Screen", img_bgr)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

# Get text detection data for drawing bounding boxes
data = pytesseract.image_to_data(img_bgr, output_type=pytesseract.Output.DICT)

# Create a copy for drawing bounding boxes
img_with_boxes = img_bgr.copy()

n_boxes = len(data['level'])
for i in range(n_boxes):
    # Only draw boxes for detected text (confidence > 0)
    if int(data['conf'][i]) > 0:
        (x, y, w, h) = (data['left'][i], data['top'][i], data['width'][i], data['height'][i])
        cv2.rectangle(img_with_boxes, (x, y), (x + w, y + h), (0, 255, 0), 2)

# Convert to grayscale for better OCR results
gray_image = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2GRAY)

# Apply thresholding for better text detection
_, thresh_image = cv2.threshold(gray_image, 150, 255, cv2.THRESH_BINARY)

# Perform OCR on the processed image
extracted_text = pytesseract.image_to_string(thresh_image)
print("Extracted Text:")
print(extracted_text)

# Display the image with bounding boxes
cv2.imshow("Detected Text", img_with_boxes)
cv2.waitKey(0)
cv2.destroyAllWindows()
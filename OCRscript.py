import cv2
import pytesseract
pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
import mss
import numpy as np
from PIL import Image
import os
import urllib.request

def download_cascade_file():
    """Download the face cascade file if it doesn't exist"""
    cascade_file = 'haarcascade_frontalface_default.xml'
    if not os.path.exists(cascade_file):
        print("Downloading face cascade file...")
        url = 'https://raw.githubusercontent.com/opencv/opencv/master/data/haarcascades/haarcascade_frontalface_default.xml'
        try:
            urllib.request.urlretrieve(url, cascade_file)
            print("Face cascade file downloaded successfully!")
            return cascade_file
        except Exception as e:
            print(f"Failed to download cascade file: {e}")
            return None
    return cascade_file

# Load the face detection classifier

# Try multiple paths to find the cascade file
cascade_paths = [
    'haarcascade_frontalface_default.xml',  # Current directory
    os.path.join(cv2.__path__[0], 'data', 'haarcascade_frontalface_default.xml'),  # OpenCV installation
    r'C:\opencv\data\haarcascades\haarcascade_frontalface_default.xml',  # Common Windows path
    r'C:\Users\<USER>\AppData\Local\Programs\Python\Python*\Lib\site-packages\cv2\data\haarcascade_frontalface_default.xml'.format(os.getenv('USERNAME')),
]

face_cascade = None
for path in cascade_paths:
    try:
        if '*' in path:
            # Skip wildcard paths for now
            continue
        face_cascade = cv2.CascadeClassifier(path)
        if not face_cascade.empty():
            print(f"Face cascade loaded from: {path}")
            break
    except:
        continue

if face_cascade is None or face_cascade.empty():
    print("Warning: Could not load face cascade classifier. Face detection will be skipped.")
    print("Please ensure haarcascade_frontalface_default.xml is available.")
    face_cascade = None

with mss.mss() as sct:
    monitor = sct.monitors[1]  # 1 is the main monitor
    screenshot = sct.grab(monitor)
    img_bgra = np.array(screenshot)

    # Convert BGRA to BGR for OpenCV processing
    img_bgr = cv2.cvtColor(img_bgra, cv2.COLOR_BGRA2BGR)

    # Display the original screenshot
    cv2.imshow("Screen", img_bgr)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

# Face Detection
faces = []
if face_cascade is not None:
    gray_for_faces = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray_for_faces, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30))

    # Create a copy for drawing face rectangles
    img_with_faces = img_bgr.copy()

    # Draw rectangles around detected faces
    for (x, y, w, h) in faces:
        cv2.rectangle(img_with_faces, (x, y), (x + w, y + h), (255, 0, 0), 3)  # Blue rectangles for faces

    print(f"Number of faces detected: {len(faces)}")

    # Display image with detected faces
    if len(faces) > 0:
        cv2.imshow("Detected Faces", img_with_faces)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    else:
        print("No faces detected in the screenshot.")
else:
    print("Face detection skipped - cascade classifier not available.")

# Text Detection and OCR
print("\n" + "="*50)
print("TEXT DETECTION AND OCR")
print("="*50)

# Get text detection data for drawing bounding boxes
data = pytesseract.image_to_data(img_bgr, output_type=pytesseract.Output.DICT)

# Create a copy for drawing both faces and text boxes
img_combined = img_bgr.copy()

# Draw face rectangles (blue)
for (x, y, w, h) in faces:
    cv2.rectangle(img_combined, (x, y), (x + w, y + h), (255, 0, 0), 3)  # Blue for faces
    cv2.putText(img_combined, 'Face', (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 0, 0), 2)

# Draw text bounding boxes (green)
n_boxes = len(data['level'])
text_detected = False
for i in range(n_boxes):
    # Only draw boxes for detected text (confidence > 30)
    if int(data['conf'][i]) > 30:
        (x, y, w, h) = (data['left'][i], data['top'][i], data['width'][i], data['height'][i])
        cv2.rectangle(img_combined, (x, y), (x + w, y + h), (0, 255, 0), 2)  # Green for text
        text_detected = True

# Convert to grayscale for better OCR results
gray_image = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2GRAY)

# Apply thresholding for better text detection
_, thresh_image = cv2.threshold(gray_image, 150, 255, cv2.THRESH_BINARY)

# Perform OCR on the processed image
extracted_text = pytesseract.image_to_string(thresh_image)
if extracted_text.strip():
    print("Extracted Text:")
    print(extracted_text)
else:
    print("No text detected in the screenshot.")

# Display the combined image with both faces and text boxes
cv2.imshow("Faces (Blue) and Text (Green) Detection", img_combined)
cv2.waitKey(0)
cv2.destroyAllWindows()

# Summary
print("\n" + "="*50)
print("DETECTION SUMMARY")
print("="*50)
print(f"Faces detected: {len(faces)}")
print(f"Text detected: {'Yes' if text_detected else 'No'}")
print(f"Text content: {'Yes' if extracted_text.strip() else 'No'}")
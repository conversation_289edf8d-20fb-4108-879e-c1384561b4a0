import cv2
import pytesseract
pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
import mss
import numpy as np
from PIL import Image
import os
import urllib.request

def download_cascade_file():
    """Download the face cascade file if it doesn't exist"""
    cascade_file = 'haarcascade_frontalface_default.xml'
    if not os.path.exists(cascade_file):
        print("Downloading face cascade file...")
        url = 'https://raw.githubusercontent.com/opencv/opencv/master/data/haarcascades/haarcascade_frontalface_default.xml'
        try:
            urllib.request.urlretrieve(url, cascade_file)
            print("Face cascade file downloaded successfully!")
            return cascade_file
        except Exception as e:
            print(f"Failed to download cascade file: {e}")
            return None
    return cascade_file

# Load the face detection classifier

# Try to load or download the cascade file
cascade_file = download_cascade_file()
face_cascade = None

if cascade_file and os.path.exists(cascade_file):
    face_cascade = cv2.CascadeClassifier(cascade_file)
    if face_cascade.empty():
        print("Warning: Face cascade file is empty or corrupted.")
        face_cascade = None
    else:
        print("Face cascade loaded successfully!")
else:
    print("Warning: Could not load face cascade classifier. Face detection will be skipped.")

with mss.mss() as sct:
    monitor = sct.monitors[1]  # 1 is the main monitor
    screenshot = sct.grab(monitor)
    img_bgra = np.array(screenshot)

    # Convert BGRA to BGR for OpenCV processing
    img_bgr = cv2.cvtColor(img_bgra, cv2.COLOR_BGRA2BGR)

print("Screenshot captured. Processing...")

# Face Detection
faces = []
if face_cascade is not None:
    print("Detecting faces...")
    gray_for_faces = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray_for_faces, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30))
    print(f"Found {len(faces)} faces")
else:
    print("Face detection skipped - cascade classifier not available.")

# Enhanced Text Detection with Multiple Passes
print("Detecting text...")

# Convert to grayscale - keep it simple
gray = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2GRAY)

# Try multiple PSM modes to catch different types of text
psm_modes = [
    ('--psm 6', 'Uniform block of text'),
    ('--psm 8', 'Single word'),
    ('--psm 7', 'Single text line'),
    ('--psm 11', 'Sparse text'),
    ('--psm 13', 'Raw line (no word detection)')
]

all_detections = []
all_text_parts = []

print("Running multiple detection passes...")
for config, description in psm_modes:
    try:
        # Get detections for this mode
        mode_data = pytesseract.image_to_data(gray, config=config, output_type=pytesseract.Output.DICT)
        mode_text = pytesseract.image_to_string(gray, config=config).strip()

        # Store detections
        all_detections.append((mode_data, description))
        if mode_text:
            all_text_parts.append(mode_text)

    except Exception as e:
        continue

# Combine all detections (use the first one as primary for bounding boxes)
data = all_detections[0][0] if all_detections else {'level': [], 'conf': [], 'left': [], 'top': [], 'width': [], 'height': [], 'text': []}

# Combine all extracted text
extracted_text = '\n'.join(set(all_text_parts))  # Remove duplicates

print(f"Text detection completed with {len(psm_modes)} passes")

# Create final combined image
img_combined = img_bgr.copy()

# Draw face rectangles (blue)
for (x, y, w, h) in faces:
    cv2.rectangle(img_combined, (x, y), (x + w, y + h), (255, 0, 0), 3)  # Blue for faces
    cv2.putText(img_combined, 'FACE', (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)

# Merge detections from all PSM modes for comprehensive text regions
all_text_regions = []
text_detected = False
detected_words = []
total_confidence = 0
confident_words = 0

# Process detections from all modes
for mode_data, mode_description in all_detections:
    n_boxes = len(mode_data['level'])

    for i in range(n_boxes):
        confidence = int(mode_data['conf'][i])
        if confidence > 25:  # Even lower threshold to catch more text
            x, y, w, h = mode_data['left'][i], mode_data['top'][i], mode_data['width'][i], mode_data['height'][i]
            text = mode_data['text'][i].strip()

            # More inclusive text filtering
            if text and len(text) > 0 and not text.isspace():
                # Avoid duplicate regions (simple overlap check)
                is_duplicate = False
                for existing_x, existing_y, existing_w, existing_h, _, _ in all_text_regions:
                    if (abs(x - existing_x) < 10 and abs(y - existing_y) < 10 and
                        abs(w - existing_w) < 20 and abs(h - existing_h) < 20):
                        is_duplicate = True
                        break

                if not is_duplicate:
                    all_text_regions.append((x, y, w, h, confidence, text))

# Draw all unique text regions
for x, y, w, h, confidence, text in all_text_regions:
    # Color code by confidence: Green (high), Yellow (medium), Orange (low)
    if confidence >= 80:
        color = (0, 255, 0)  # Green for high confidence
    elif confidence >= 60:
        color = (0, 255, 255)  # Yellow for medium confidence
    elif confidence >= 40:
        color = (0, 165, 255)  # Orange for medium-low confidence
    else:
        color = (0, 100, 255)  # Red-orange for low confidence

    cv2.rectangle(img_combined, (x, y), (x + w, y + h), color, 2)
    cv2.putText(img_combined, f'{confidence}%', (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
    text_detected = True
    detected_words.append(f"'{text}' ({confidence}%)")
    total_confidence += confidence
    confident_words += 1

# Calculate average confidence
avg_confidence = total_confidence / confident_words if confident_words > 0 else 0

# Display results
print("\n" + "="*50)
print("DETECTION RESULTS")
print("="*50)

if extracted_text:
    print("EXTRACTED TEXT:")
    print("-" * 30)
    print(extracted_text)
    print("-" * 30)
    print(f"Average confidence: {avg_confidence:.1f}%")
else:
    print("No text detected.")

if detected_words:
    print(f"\nDETECTED WORDS ({len(detected_words)} total):")
    for word in detected_words[:15]:  # Show first 15 words
        print(f"  {word}")
    if len(detected_words) > 15:
        print(f"  ... and {len(detected_words) - 15} more")

# Final summary
print(f"\nSUMMARY:")
print(f"• Faces detected: {len(faces)}")
print(f"• Text regions detected: {len(detected_words)}")
print(f"• Average text confidence: {avg_confidence:.1f}%")

# Show single final result
cv2.imshow("Final Detection Results - Faces (Blue) & Text (Green)", img_combined)
print(f"\nShowing final results. Press any key to close...")
cv2.waitKey(0)
cv2.destroyAllWindows()
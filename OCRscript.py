import cv2
import pytesseract
pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
import mss
import numpy as np
from PIL import Image
import os
import urllib.request

def download_cascade_file():
    """Download the face cascade file if it doesn't exist"""
    cascade_file = 'haarcascade_frontalface_default.xml'
    if not os.path.exists(cascade_file):
        print("Downloading face cascade file...")
        url = 'https://raw.githubusercontent.com/opencv/opencv/master/data/haarcascades/haarcascade_frontalface_default.xml'
        try:
            urllib.request.urlretrieve(url, cascade_file)
            print("Face cascade file downloaded successfully!")
            return cascade_file
        except Exception as e:
            print(f"Failed to download cascade file: {e}")
            return None
    return cascade_file

# Load the face detection classifier

# Try to load or download the cascade file
cascade_file = download_cascade_file()
face_cascade = None

if cascade_file and os.path.exists(cascade_file):
    face_cascade = cv2.CascadeClassifier(cascade_file)
    if face_cascade.empty():
        print("Warning: Face cascade file is empty or corrupted.")
        face_cascade = None
    else:
        print("Face cascade loaded successfully!")
else:
    print("Warning: Could not load face cascade classifier. Face detection will be skipped.")

with mss.mss() as sct:
    monitor = sct.monitors[1]  # 1 is the main monitor
    screenshot = sct.grab(monitor)
    img_bgra = np.array(screenshot)

    # Convert BGRA to BGR for OpenCV processing
    img_bgr = cv2.cvtColor(img_bgra, cv2.COLOR_BGRA2BGR)

print("Screenshot captured. Processing...")

# Face Detection
faces = []
if face_cascade is not None:
    print("Detecting faces...")
    gray_for_faces = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray_for_faces, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30))
    print(f"Found {len(faces)} faces")
else:
    print("Face detection skipped - cascade classifier not available.")

# Simple and Effective Text Detection
print("Detecting text...")

# Convert to grayscale - keep it simple
gray = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2GRAY)

# Use default Tesseract settings - they work best for most cases
data = pytesseract.image_to_data(gray, output_type=pytesseract.Output.DICT)

# Extract text using default settings
extracted_text = pytesseract.image_to_string(gray).strip()

print("Text detection completed")

# Create final combined image
img_combined = img_bgr.copy()

# Draw face rectangles (blue)
for (x, y, w, h) in faces:
    cv2.rectangle(img_combined, (x, y), (x + w, y + h), (255, 0, 0), 3)  # Blue for faces
    cv2.putText(img_combined, 'FACE', (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)

# Draw text bounding boxes (green) with better filtering
n_boxes = len(data['level'])
text_detected = False
detected_words = []
total_confidence = 0
confident_words = 0

for i in range(n_boxes):
    confidence = int(data['conf'][i])
    if confidence > 60:  # Higher threshold for reliable text
        x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
        text = data['text'][i].strip()

        if text and len(text.replace(' ', '')) > 0:  # Only actual text content
            cv2.rectangle(img_combined, (x, y), (x + w, y + h), (0, 255, 0), 2)  # Green for text
            cv2.putText(img_combined, f'{confidence}%', (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
            text_detected = True
            detected_words.append(f"'{text}' ({confidence}%)")
            total_confidence += confidence
            confident_words += 1

# Calculate average confidence
avg_confidence = total_confidence / confident_words if confident_words > 0 else 0

# Display results
print("\n" + "="*50)
print("DETECTION RESULTS")
print("="*50)

if extracted_text:
    print("EXTRACTED TEXT:")
    print("-" * 30)
    print(extracted_text)
    print("-" * 30)
    print(f"Average confidence: {avg_confidence:.1f}%")
else:
    print("No text detected.")

if detected_words:
    print(f"\nDETECTED WORDS ({len(detected_words)} total):")
    for word in detected_words[:15]:  # Show first 15 words
        print(f"  {word}")
    if len(detected_words) > 15:
        print(f"  ... and {len(detected_words) - 15} more")

# Final summary
print(f"\nSUMMARY:")
print(f"• Faces detected: {len(faces)}")
print(f"• Text regions detected: {len(detected_words)}")
print(f"• Average text confidence: {avg_confidence:.1f}%")

# Show single final result
cv2.imshow("Final Detection Results - Faces (Blue) & Text (Green)", img_combined)
print(f"\nShowing final results. Press any key to close...")
cv2.waitKey(0)
cv2.destroyAllWindows()
import cv2
import pytesseract
pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
import mss
import numpy as np

from PIL import Image

with mss.mss() as sct:
    monitor = sct.monitors[1]  # 1 is the main monitor
    screenshot = sct.grab(monitor)
    img = np.array(screenshot)

    # Convert BGRA to BGR
    img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)

    cv2.imshow("Screen", img)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

data = pytesseract.image_to_data(img, output_type=pytesseract.Output.DICT)

n_boxes = len(data['level'])
for i in range(n_boxes):
    (x, y, w, h) = (data['left'][i], data['top'][i], data['width'][i], data['height'][i])
    cv2.rectangle(img, (x, y), (x + w, y + h), (0, 255, 0), 2)

cv2.imshow("Text Detection", img)
cv2.waitKey(0)
cv2.destroyAllWindows()


# Convert to grayscale
gray_image = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

# Optional: Apply thresholding for better text detection
_, thresh_image = cv2.threshold(img, 150, 255, cv2.THRESH_BINARY)

# Perform OCR
extracted_text = pytesseract.image_to_string(thresh_image)
print("Extracted Text:")
print(extracted_text)

data = pytesseract.image_to_data(img, output_type=pytesseract.Output.DICT)
n_boxes = len(data['level'])

for i in range(n_boxes):
    (x, y, w, h) = (data['left'][i], data['top'][i], data['width'][i], data['height'][i])
    cv2.rectangle(img, (x, y), (x + w, y + h), (0, 255, 0), 2)

# Display the image with bounding boxes
cv2.imshow("Detected Text", image)
cv2.waitKey(0)
cv2.destroyAllWindows()
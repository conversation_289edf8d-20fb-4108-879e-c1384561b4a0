import mss
import base64
import io
from PIL import Image
import time
from datetime import datetime
import requests
import json
import pyaudio
import wave
import threading
import speech_recognition as sr
import numpy as np
import subprocess
import os
import tempfile
from datetime import datetime

# Free API Configuration - Choose your preferred method
USE_GEMINI = True  # Google Gemini (FREE - 15 requests/minute)
USE_OLLAMA = False  # Ollama + LLaVA (100% FREE & LOCAL)
USE_HUGGINGFACE = False  # Hugging Face (FREE with rate limits)

# Get your free Gemini API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY = "AIzaSyAn4hFVT12sGKNTlwRjhZ2JA_6lzszuqgE"  # Your free Gemini key

class ScreenActivityMonitor:
    def __init__(self):
        self.activity_history = []
        self.audio_data = ""
        self.is_recording = False
        self.audio_thread = None
        self.continuous_audio_data = []
        self.audio_buffer_size = 10  # Keep last 10 seconds of audio analysis

        # Initialize the selected API
        if USE_GEMINI:
            try:
                import google.generativeai as genai
                if GEMINI_API_KEY != "YOUR_FREE_GEMINI_API_KEY_HERE":
                    genai.configure(api_key=GEMINI_API_KEY)
                    self.model = genai.GenerativeModel('gemini-1.5-flash')
                    self.api_type = "gemini"
                    print("🔑 Using Google Gemini (FREE - 15 requests/minute)")
                else:
                    print("❌ Please set your Gemini API key!")
                    print("🔗 Get free key: https://makersuite.google.com/app/apikey")
                    exit(1)
            except ImportError:
                print("❌ Google Generative AI not installed. Run: pip install google-generativeai")
                exit(1)
        elif USE_OLLAMA:
            self.api_type = "ollama"
            print("🔑 Using Ollama + LLaVA (100% FREE & LOCAL)")
        elif USE_HUGGINGFACE:
            self.api_type = "huggingface"
            print("🔑 Using Hugging Face Transformers (FREE)")
        else:
            print("❌ Please select an API method!")
            exit(1)

    def capture_screen(self):
        """Capture the current screen"""
        with mss.mss() as sct:
            monitor = sct.monitors[1]  # Primary monitor
            screenshot = sct.grab(monitor)

            # Convert to PIL Image
            img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
            return img

    def image_to_base64(self, image):
        """Convert PIL image to base64 string"""
        buffer = io.BytesIO()
        image.save(buffer, format="PNG")
        img_str = base64.b64encode(buffer.getvalue()).decode()
        return img_str

    def start_continuous_audio_monitoring(self):
        """Start continuous system audio monitoring in background"""
        self.is_recording = True
        self.audio_thread = threading.Thread(target=self._continuous_audio_monitor)
        self.audio_thread.daemon = True
        self.audio_thread.start()
        print("🎵 Started continuous audio monitoring...")

    def stop_audio_recording(self):
        """Stop recording system audio"""
        self.is_recording = False
        if self.audio_thread:
            self.audio_thread.join(timeout=2)
        print("🔇 Stopped audio monitoring")

    def _continuous_audio_monitor(self):
        """Continuously monitor system audio using Intel Smart Microphone Array"""
        try:
            import sounddevice as sd

            # Use Intel Smart Microphone Array (Device 2) - PROVEN TO WORK
            AUDIO_DEVICE = 2
            SAMPLE_RATE = 44100
            DURATION = 2  # Analyze 2-second chunks

            print("🎵 Listening to system audio via Intel Smart Microphone...")

            while self.is_recording:
                try:
                    # Record system audio using working device
                    audio_data = sd.rec(int(DURATION * SAMPLE_RATE),
                                      samplerate=SAMPLE_RATE,
                                      channels=2,
                                      device=AUDIO_DEVICE,
                                      dtype='float32')
                    sd.wait()  # Wait for recording to complete

                    # Analyze audio characteristics
                    audio_analysis = self._analyze_audio_content(audio_data, SAMPLE_RATE)

                    # Store in buffer (keep last 10 analyses)
                    self.continuous_audio_data.append({
                        'timestamp': datetime.now().strftime("%H:%M:%S"),
                        'analysis': audio_analysis
                    })

                    # Keep only recent data
                    if len(self.continuous_audio_data) > self.audio_buffer_size:
                        self.continuous_audio_data.pop(0)

                    # Update current audio data
                    self.audio_data = audio_analysis

                except Exception as e:
                    self.audio_data = f"Audio monitoring error: {str(e)}"
                    time.sleep(1)

        except ImportError:
            # Fallback to basic audio detection
            self._basic_audio_monitor()

    def _analyze_audio_content(self, audio_data, sample_rate):
        """Analyze audio content characteristics"""
        try:
            import librosa

            # Calculate audio features
            rms_energy = np.sqrt(np.mean(audio_data**2))

            if rms_energy < 0.001:
                return "🔇 Silence detected"
            elif rms_energy < 0.01:
                return "🔉 Low volume audio (background noise/quiet music)"
            elif rms_energy < 0.1:
                return "🔊 Moderate volume audio (speech/music)"
            else:
                return "📢 High volume audio (loud music/speech)"

        except ImportError:
            # Basic volume analysis without librosa
            rms_energy = np.sqrt(np.mean(audio_data**2))

            if rms_energy < 0.001:
                return "🔇 Silence detected"
            elif rms_energy < 0.01:
                return "🔉 Audio detected (low volume)"
            elif rms_energy < 0.1:
                return "🔊 Audio detected (moderate volume)"
            else:
                return "📢 Audio detected (high volume)"
        except Exception as e:
            return f"Audio analysis error: {str(e)}"

    def _basic_audio_monitor(self):
        """Basic audio monitoring fallback"""
        while self.is_recording:
            try:
                # Simple audio presence detection
                import pyaudio

                CHUNK = 1024
                FORMAT = pyaudio.paInt16
                CHANNELS = 1
                RATE = 44100

                p = pyaudio.PyAudio()

                # Try to use default input device
                stream = p.open(format=FORMAT,
                              channels=CHANNELS,
                              rate=RATE,
                              input=True,
                              frames_per_buffer=CHUNK)

                # Read audio chunk
                data = stream.read(CHUNK, exception_on_overflow=False)
                audio_array = np.frombuffer(data, dtype=np.int16)

                # Calculate volume
                volume = np.sqrt(np.mean(audio_array**2))

                if volume > 1000:
                    self.audio_data = f"🎵 Audio detected (volume: {int(volume)})"
                else:
                    self.audio_data = "🔇 No significant audio"

                stream.stop_stream()
                stream.close()
                p.terminate()

                time.sleep(2)  # Check every 2 seconds

            except Exception as e:
                self.audio_data = f"Basic audio monitor error: {str(e)}"
                time.sleep(2)

    def get_recent_audio_summary(self):
        """Get summary of recent audio activity"""
        if not self.continuous_audio_data:
            return "No recent audio data"

        recent_analyses = [item['analysis'] for item in self.continuous_audio_data[-3:]]
        return " | ".join(recent_analyses)

    def analyze_screen_activity(self, image):
        """Analyze screen activity using selected API"""
        try:
            if self.api_type == "gemini":
                return self._analyze_with_gemini(image)
            elif self.api_type == "ollama":
                return self._analyze_with_ollama(image)
            elif self.api_type == "huggingface":
                return self._analyze_with_huggingface(image)
            else:
                return "No API configured"

        except Exception as e:
            return f"Error analyzing screen: {str(e)}"

    def _analyze_with_gemini(self, image):
        """Analyze using Google Gemini"""
        prompt = """Analyze this screenshot and tell me:
1. What application/website is currently active?
2. What specific activity is the user doing?
3. What type of content is visible (video, text, images, etc.)?
4. If it's a game, what game is it?
5. If it's a website, what website and what are they doing?
6. If it's a video, what type of video content?
7. Overall context and user intent

Be specific and detailed like a human observer would be."""

        response = self.model.generate_content([prompt, image])
        return response.text

    def _analyze_with_ollama(self, image):
        """Analyze using Ollama + LLaVA (Local)"""
        # Save image temporarily for Ollama
        temp_path = "temp_screenshot.png"
        image.save(temp_path)

        # Call Ollama API
        import subprocess
        result = subprocess.run([
            "ollama", "run", "llava:7b",
            "Analyze this screenshot and describe what the user is doing in detail.",
            temp_path
        ], capture_output=True, text=True)

        import os
        os.remove(temp_path)
        return result.stdout if result.returncode == 0 else f"Ollama error: {result.stderr}"

    def _analyze_with_huggingface(self, image):
        """Analyze using Hugging Face Transformers"""
        try:
            from transformers import BlipProcessor, BlipForConditionalGeneration

            # Load model (first time will download)
            processor = BlipProcessor.from_pretrained("Salesforce/blip-image-captioning-base")
            model = BlipForConditionalGeneration.from_pretrained("Salesforce/blip-image-captioning-base")

            # Process image
            inputs = processor(image, return_tensors="pt")
            out = model.generate(**inputs, max_length=100)
            caption = processor.decode(out[0], skip_special_tokens=True)

            return f"Screen Activity: {caption}"

        except ImportError:
            return "Please install transformers: pip install transformers torch"
        except Exception as e:
            return f"Hugging Face error: {str(e)}"

    def categorize_activity(self, analysis):
        """Categorize the activity based on analysis"""
        analysis_lower = analysis.lower()

        categories = {
            'ai_research': ['lightning ai', 'ai hub', 'api documentation', 'machine learning', 'ai models', 'benchmark', 'data scientist', 'vision api', 'llm', 'generative ai'],
            'video_streaming': ['youtube website', 'youtube', 'netflix', 'watching video', 'video episode', 'anime episode', 'streaming video', 'twitch stream', 'video player'],
            'gaming': ['chess.com', 'playing chess', 'playing minecraft', 'playing fortnite', 'steam game', 'xbox game', 'playstation game', 'game controller'],
            'social_media': ['instagram stories', 'facebook feed', 'twitter timeline', 'tiktok videos', 'snapchat', 'linkedin feed'],
            'web_browsing': ['web browser', 'browsing website', 'google search', 'chrome browser', 'firefox browser', 'website'],
            'work': ['microsoft word', 'excel spreadsheet', 'powerpoint presentation', 'email client', 'teams meeting', 'zoom call'],
            'coding': ['visual studio code', 'programming', 'python script', 'javascript code', 'github repository', 'code editor'],
            'shopping': ['amazon cart', 'shopping cart', 'buy now', 'checkout page', 'purchase', 'online store']
        }

        for category, keywords in categories.items():
            if any(keyword in analysis_lower for keyword in keywords):
                return category

        return 'other'

    def display_results(self, analysis, category, timestamp):
        """Display the analysis results"""
        print("\n" + "="*80)
        print(f"🔍 SCREEN ACTIVITY ANALYSIS - {timestamp}")
        print("="*80)
        print(f"📱 Category: {category.upper().replace('_', ' ')}")
        print("\n📋 Detailed Analysis:")
        print("-" * 50)
        print(analysis)
        print("-" * 50)

    def run_continuous_monitoring(self, interval=10, include_audio=True):
        """Run continuous screen monitoring with real-time audio"""
        print("🚀 Starting Real-Time Screen + Audio Activity Monitor")
        print(f"🔑 Using {self.api_type.title()}")
        print(f"⏱️  Analyzing screen every {interval} seconds")
        print("⏹️  Press Ctrl+C to stop")
        print("\n" + "="*80)

        # Start continuous audio monitoring if requested
        if include_audio:
            self.start_continuous_audio_monitoring()

        try:
            while True:
                # Capture screen
                print("📸 Capturing screen...")
                screenshot = self.capture_screen()

                # Analyze activity
                print("🧠 Analyzing with AI...")
                analysis = self.analyze_screen_activity(screenshot)

                # Add recent audio analysis if available
                if include_audio and self.audio_data:
                    recent_audio = self.get_recent_audio_summary()
                    analysis += f"\n\n🎵 AUDIO ANALYSIS:\n{recent_audio}"

                category = self.categorize_activity(analysis)
                timestamp = datetime.now().strftime("%H:%M:%S")

                # Store in history
                self.activity_history.append({
                    'timestamp': timestamp,
                    'category': category,
                    'analysis': analysis
                })

                # Display results
                self.display_results(analysis, category, timestamp)

                # Wait for next analysis
                print(f"\n⏳ Waiting {interval} seconds for next analysis...")
                time.sleep(interval)

        except KeyboardInterrupt:
            print("\n\n🛑 Monitoring stopped by user")
            self.show_session_summary()

    def run_single_analysis(self, include_audio=True):
        """Run a single screen analysis with optional audio"""
        print("🚀 Single Screen Analysis")
        print(f"🔑 Using {self.api_type.title()}")
        print("\n" + "="*50)

        # Start continuous audio monitoring if requested
        if include_audio:
            self.start_continuous_audio_monitoring()
            time.sleep(3)  # Let audio monitoring start

        # Capture and analyze
        print("📸 Capturing screen...")
        screenshot = self.capture_screen()

        print("🧠 Analyzing with AI...")
        analysis = self.analyze_screen_activity(screenshot)

        # Add audio analysis if available
        if include_audio and self.audio_data:
            analysis += f"\n\n🎵 AUDIO ANALYSIS:\n{self.audio_data}"

        # Stop audio monitoring
        if include_audio:
            self.stop_audio_recording()

        category = self.categorize_activity(analysis)
        timestamp = datetime.now().strftime("%H:%M:%S")

        # Display results
        self.display_results(analysis, category, timestamp)

        # Save screenshot for reference
        screenshot.save("last_analysis_screenshot.png")
        print(f"\n💾 Screenshot saved as 'last_analysis_screenshot.png'")

    def show_session_summary(self):
        """Show summary of the monitoring session"""
        if not self.activity_history:
            return

        print("\n" + "="*80)
        print("📊 SESSION SUMMARY")
        print("="*80)

        # Count categories
        categories = {}
        for entry in self.activity_history:
            cat = entry['category']
            categories[cat] = categories.get(cat, 0) + 1

        print("🏷️  Activity Categories:")
        for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
            print(f"   • {category.replace('_', ' ').title()}: {count} times")

        print(f"\n📈 Total Analyses: {len(self.activity_history)}")
        print(f"⏱️  Session Duration: {len(self.activity_history) * 10} seconds")

# Main execution
if __name__ == "__main__":
    monitor = ScreenActivityMonitor()

    print("🎯 Advanced Screen Activity Recognition System")
    print("🤖 Powered by AI Vision + Audio Analysis")
    print("\nChoose mode:")
    print("1. Single Analysis (analyze current screen + audio once)")
    print("2. Continuous Monitoring (analyze screen + audio every 10 seconds)")
    print("3. Visual Only Analysis (no audio)")
    print("4. Continuous Visual Only (no audio)")

    choice = input("\nEnter choice (1-4): ").strip()

    if choice == "1":
        monitor.run_single_analysis(include_audio=True)
    elif choice == "2":
        monitor.run_continuous_monitoring(include_audio=True)
    elif choice == "3":
        monitor.run_single_analysis(include_audio=False)
    elif choice == "4":
        monitor.run_continuous_monitoring(include_audio=False)
    else:
        print("Invalid choice. Running single analysis with audio...")
        monitor.run_single_analysis(include_audio=True)
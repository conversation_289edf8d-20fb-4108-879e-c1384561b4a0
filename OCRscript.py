import mss
import base64
import io
from PIL import Image
import time
from datetime import datetime
import requests
import json

# Free API Configuration - Choose your preferred method
USE_GEMINI = True  # Google Gemini (FREE - 15 requests/minute)
USE_OLLAMA = False  # Ollama + LLaVA (100% FREE & LOCAL)
USE_HUGGINGFACE = False  # Hugging Face (FREE with rate limits)

# Get your free Gemini API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY = "AIzaSyAn4hFVT12sGKNTlwRjhZ2JA_6lzszuqgE"  # Your free Gemini key

class ScreenActivityMonitor:
    def __init__(self):
        self.activity_history = []

        # Initialize the selected API
        if USE_GEMINI:
            try:
                import google.generativeai as genai
                if GEMINI_API_KEY != "YOUR_FREE_GEMINI_API_KEY_HERE":
                    genai.configure(api_key=GEMINI_API_KEY)
                    self.model = genai.GenerativeModel('gemini-1.5-flash')
                    self.api_type = "gemini"
                    print("🔑 Using Google Gemini (FREE - 15 requests/minute)")
                else:
                    print("❌ Please set your Gemini API key!")
                    print("🔗 Get free key: https://makersuite.google.com/app/apikey")
                    exit(1)
            except ImportError:
                print("❌ Google Generative AI not installed. Run: pip install google-generativeai")
                exit(1)
        elif USE_OLLAMA:
            self.api_type = "ollama"
            print("🔑 Using Ollama + LLaVA (100% FREE & LOCAL)")
        elif USE_HUGGINGFACE:
            self.api_type = "huggingface"
            print("🔑 Using Hugging Face Transformers (FREE)")
        else:
            print("❌ Please select an API method!")
            exit(1)

    def capture_screen(self):
        """Capture the current screen"""
        with mss.mss() as sct:
            monitor = sct.monitors[1]  # Primary monitor
            screenshot = sct.grab(monitor)

            # Convert to PIL Image
            img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
            return img

    def image_to_base64(self, image):
        """Convert PIL image to base64 string"""
        buffer = io.BytesIO()
        image.save(buffer, format="PNG")
        img_str = base64.b64encode(buffer.getvalue()).decode()
        return img_str

    def analyze_screen_activity(self, image):
        """Analyze screen activity using selected API"""
        try:
            if self.api_type == "gemini":
                return self._analyze_with_gemini(image)
            elif self.api_type == "ollama":
                return self._analyze_with_ollama(image)
            elif self.api_type == "huggingface":
                return self._analyze_with_huggingface(image)
            else:
                return "No API configured"

        except Exception as e:
            return f"Error analyzing screen: {str(e)}"

    def _analyze_with_gemini(self, image):
        """Analyze using Google Gemini"""
        prompt = """Analyze this screenshot and tell me:
1. What application/website is currently active?
2. What specific activity is the user doing?
3. What type of content is visible (video, text, images, etc.)?
4. If it's a game, what game is it?
5. If it's a website, what website and what are they doing?
6. If it's a video, what type of video content?
7. Overall context and user intent

Be specific and detailed like a human observer would be."""

        response = self.model.generate_content([prompt, image])
        return response.text

    def _analyze_with_ollama(self, image):
        """Analyze using Ollama + LLaVA (Local)"""
        # Save image temporarily for Ollama
        temp_path = "temp_screenshot.png"
        image.save(temp_path)

        # Call Ollama API
        import subprocess
        result = subprocess.run([
            "ollama", "run", "llava:7b",
            "Analyze this screenshot and describe what the user is doing in detail.",
            temp_path
        ], capture_output=True, text=True)

        import os
        os.remove(temp_path)
        return result.stdout if result.returncode == 0 else f"Ollama error: {result.stderr}"

    def _analyze_with_huggingface(self, image):
        """Analyze using Hugging Face Transformers"""
        try:
            from transformers import BlipProcessor, BlipForConditionalGeneration

            # Load model (first time will download)
            processor = BlipProcessor.from_pretrained("Salesforce/blip-image-captioning-base")
            model = BlipForConditionalGeneration.from_pretrained("Salesforce/blip-image-captioning-base")

            # Process image
            inputs = processor(image, return_tensors="pt")
            out = model.generate(**inputs, max_length=100)
            caption = processor.decode(out[0], skip_special_tokens=True)

            return f"Screen Activity: {caption}"

        except ImportError:
            return "Please install transformers: pip install transformers torch"
        except Exception as e:
            return f"Hugging Face error: {str(e)}"

    def categorize_activity(self, analysis):
        """Categorize the activity based on analysis"""
        analysis_lower = analysis.lower()

        categories = {
            'gaming': ['game', 'gaming', 'chess', 'minecraft', 'fortnite', 'valorant', 'league of legends'],
            'social_media': ['instagram', 'facebook', 'twitter', 'tiktok', 'snapchat', 'linkedin'],
            'video_streaming': ['youtube', 'netflix', 'twitch', 'video', 'streaming'],
            'web_browsing': ['google', 'browser', 'website', 'search', 'chrome', 'firefox'],
            'work': ['document', 'excel', 'word', 'powerpoint', 'email', 'teams', 'zoom'],
            'coding': ['code', 'programming', 'python', 'javascript', 'github', 'vscode'],
            'shopping': ['amazon', 'shopping', 'cart', 'buy', 'purchase', 'store']
        }

        for category, keywords in categories.items():
            if any(keyword in analysis_lower for keyword in keywords):
                return category

        return 'other'

    def display_results(self, analysis, category, timestamp):
        """Display the analysis results"""
        print("\n" + "="*80)
        print(f"🔍 SCREEN ACTIVITY ANALYSIS - {timestamp}")
        print("="*80)
        print(f"📱 Category: {category.upper().replace('_', ' ')}")
        print("\n📋 Detailed Analysis:")
        print("-" * 50)
        print(analysis)
        print("-" * 50)

    def run_continuous_monitoring(self, interval=10):
        """Run continuous screen monitoring"""
        print("🚀 Starting Real-Time Screen Activity Monitor")
        print("🔑 Using OpenAI GPT-4 Vision for intelligent analysis")
        print(f"⏱️  Analyzing screen every {interval} seconds")
        print("⏹️  Press Ctrl+C to stop")
        print("\n" + "="*80)

        try:
            while True:
                # Capture screen
                print("📸 Capturing screen...")
                screenshot = self.capture_screen()

                # Analyze activity
                print("🧠 Analyzing with AI...")
                analysis = self.analyze_screen_activity(screenshot)
                category = self.categorize_activity(analysis)
                timestamp = datetime.now().strftime("%H:%M:%S")

                # Store in history
                self.activity_history.append({
                    'timestamp': timestamp,
                    'category': category,
                    'analysis': analysis
                })

                # Display results
                self.display_results(analysis, category, timestamp)

                # Wait for next analysis
                print(f"\n⏳ Waiting {interval} seconds for next analysis...")
                time.sleep(interval)

        except KeyboardInterrupt:
            print("\n\n🛑 Monitoring stopped by user")
            self.show_session_summary()

    def run_single_analysis(self):
        """Run a single screen analysis"""
        print("🚀 Single Screen Analysis")
        print("🔑 Using OpenAI GPT-4 Vision")
        print("\n" + "="*50)

        # Capture and analyze
        print("📸 Capturing screen...")
        screenshot = self.capture_screen()

        print("🧠 Analyzing with AI...")
        analysis = self.analyze_screen_activity(screenshot)
        category = self.categorize_activity(analysis)
        timestamp = datetime.now().strftime("%H:%M:%S")

        # Display results
        self.display_results(analysis, category, timestamp)

        # Save screenshot for reference
        screenshot.save("last_analysis_screenshot.png")
        print(f"\n💾 Screenshot saved as 'last_analysis_screenshot.png'")

    def show_session_summary(self):
        """Show summary of the monitoring session"""
        if not self.activity_history:
            return

        print("\n" + "="*80)
        print("📊 SESSION SUMMARY")
        print("="*80)

        # Count categories
        categories = {}
        for entry in self.activity_history:
            cat = entry['category']
            categories[cat] = categories.get(cat, 0) + 1

        print("🏷️  Activity Categories:")
        for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
            print(f"   • {category.replace('_', ' ').title()}: {count} times")

        print(f"\n📈 Total Analyses: {len(self.activity_history)}")
        print(f"⏱️  Session Duration: {len(self.activity_history) * 10} seconds")

# Main execution
if __name__ == "__main__":
    monitor = ScreenActivityMonitor()

    print("🎯 Advanced Screen Activity Recognition System")
    print("🤖 Powered by OpenAI GPT-4 Vision")
    print("\nChoose mode:")
    print("1. Single Analysis (analyze current screen once)")
    print("2. Continuous Monitoring (analyze every 10 seconds)")

    choice = input("\nEnter choice (1 or 2): ").strip()

    if choice == "1":
        monitor.run_single_analysis()
    elif choice == "2":
        monitor.run_continuous_monitoring()
    else:
        print("Invalid choice. Running single analysis...")
        monitor.run_single_analysis()
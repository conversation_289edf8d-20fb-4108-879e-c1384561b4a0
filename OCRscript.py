import mss
import base64
import io
from PIL import Image
import openai
import time
from datetime import datetime

# Configure OpenAI API
openai.api_key = "*****************************************************************"

class ScreenActivityMonitor:
    def __init__(self):
        self.client = openai.OpenAI(api_key=openai.api_key)
        self.activity_history = []

    def capture_screen(self):
        """Capture the current screen"""
        with mss.mss() as sct:
            monitor = sct.monitors[1]  # Primary monitor
            screenshot = sct.grab(monitor)

            # Convert to PIL Image
            img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
            return img

    def image_to_base64(self, image):
        """Convert PIL image to base64 string"""
        buffer = io.BytesIO()
        image.save(buffer, format="PNG")
        img_str = base64.b64encode(buffer.getvalue()).decode()
        return img_str

    def analyze_screen_activity(self, image):
        """Analyze screen activity using GPT-4 Vision"""
        try:
            base64_image = self.image_to_base64(image)

            response = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": """Analyze this screenshot and tell me:
1. What application/website is currently active?
2. What specific activity is the user doing?
3. What type of content is visible (video, text, images, etc.)?
4. If it's a game, what game is it?
5. If it's a website, what website and what are they doing?
6. If it's a video, what type of video content?
7. Overall context and user intent

Be specific and detailed like a human observer would be."""
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=500
            )

            return response.choices[0].message.content

        except Exception as e:
            return f"Error analyzing screen: {str(e)}"

    def categorize_activity(self, analysis):
        """Categorize the activity based on analysis"""
        analysis_lower = analysis.lower()

        categories = {
            'gaming': ['game', 'gaming', 'chess', 'minecraft', 'fortnite', 'valorant', 'league of legends'],
            'social_media': ['instagram', 'facebook', 'twitter', 'tiktok', 'snapchat', 'linkedin'],
            'video_streaming': ['youtube', 'netflix', 'twitch', 'video', 'streaming'],
            'web_browsing': ['google', 'browser', 'website', 'search', 'chrome', 'firefox'],
            'work': ['document', 'excel', 'word', 'powerpoint', 'email', 'teams', 'zoom'],
            'coding': ['code', 'programming', 'python', 'javascript', 'github', 'vscode'],
            'shopping': ['amazon', 'shopping', 'cart', 'buy', 'purchase', 'store']
        }

        for category, keywords in categories.items():
            if any(keyword in analysis_lower for keyword in keywords):
                return category

        return 'other'

    def display_results(self, analysis, category, timestamp):
        """Display the analysis results"""
        print("\n" + "="*80)
        print(f"🔍 SCREEN ACTIVITY ANALYSIS - {timestamp}")
        print("="*80)
        print(f"📱 Category: {category.upper().replace('_', ' ')}")
        print("\n📋 Detailed Analysis:")
        print("-" * 50)
        print(analysis)
        print("-" * 50)

    def run_continuous_monitoring(self, interval=10):
        """Run continuous screen monitoring"""
        print("🚀 Starting Real-Time Screen Activity Monitor")
        print("🔑 Using OpenAI GPT-4 Vision for intelligent analysis")
        print(f"⏱️  Analyzing screen every {interval} seconds")
        print("⏹️  Press Ctrl+C to stop")
        print("\n" + "="*80)

        try:
            while True:
                # Capture screen
                print("📸 Capturing screen...")
                screenshot = self.capture_screen()

                # Analyze activity
                print("🧠 Analyzing with AI...")
                analysis = self.analyze_screen_activity(screenshot)
                category = self.categorize_activity(analysis)
                timestamp = datetime.now().strftime("%H:%M:%S")

                # Store in history
                self.activity_history.append({
                    'timestamp': timestamp,
                    'category': category,
                    'analysis': analysis
                })

                # Display results
                self.display_results(analysis, category, timestamp)

                # Wait for next analysis
                print(f"\n⏳ Waiting {interval} seconds for next analysis...")
                time.sleep(interval)

        except KeyboardInterrupt:
            print("\n\n🛑 Monitoring stopped by user")
            self.show_session_summary()

    def run_single_analysis(self):
        """Run a single screen analysis"""
        print("🚀 Single Screen Analysis")
        print("🔑 Using OpenAI GPT-4 Vision")
        print("\n" + "="*50)

        # Capture and analyze
        print("📸 Capturing screen...")
        screenshot = self.capture_screen()

        print("🧠 Analyzing with AI...")
        analysis = self.analyze_screen_activity(screenshot)
        category = self.categorize_activity(analysis)
        timestamp = datetime.now().strftime("%H:%M:%S")

        # Display results
        self.display_results(analysis, category, timestamp)

        # Save screenshot for reference
        screenshot.save("last_analysis_screenshot.png")
        print(f"\n💾 Screenshot saved as 'last_analysis_screenshot.png'")

    def show_session_summary(self):
        """Show summary of the monitoring session"""
        if not self.activity_history:
            return

        print("\n" + "="*80)
        print("📊 SESSION SUMMARY")
        print("="*80)

        # Count categories
        categories = {}
        for entry in self.activity_history:
            cat = entry['category']
            categories[cat] = categories.get(cat, 0) + 1

        print("🏷️  Activity Categories:")
        for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
            print(f"   • {category.replace('_', ' ').title()}: {count} times")

        print(f"\n📈 Total Analyses: {len(self.activity_history)}")
        print(f"⏱️  Session Duration: {len(self.activity_history) * 10} seconds")

# Main execution
if __name__ == "__main__":
    monitor = ScreenActivityMonitor()

    print("🎯 Advanced Screen Activity Recognition System")
    print("🤖 Powered by OpenAI GPT-4 Vision")
    print("\nChoose mode:")
    print("1. Single Analysis (analyze current screen once)")
    print("2. Continuous Monitoring (analyze every 10 seconds)")

    choice = input("\nEnter choice (1 or 2): ").strip()

    if choice == "1":
        monitor.run_single_analysis()
    elif choice == "2":
        monitor.run_continuous_monitoring()
    else:
        print("Invalid choice. Running single analysis...")
        monitor.run_single_analysis()
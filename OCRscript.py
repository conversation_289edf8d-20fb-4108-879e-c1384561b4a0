import cv2
import pytesseract
pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
import mss
import numpy as np
from PIL import Image
import os
import urllib.request

def download_cascade_file():
    """Download the face cascade file if it doesn't exist"""
    cascade_file = 'haarcascade_frontalface_default.xml'
    if not os.path.exists(cascade_file):
        print("Downloading face cascade file...")
        url = 'https://raw.githubusercontent.com/opencv/opencv/master/data/haarcascades/haarcascade_frontalface_default.xml'
        try:
            urllib.request.urlretrieve(url, cascade_file)
            print("Face cascade file downloaded successfully!")
            return cascade_file
        except Exception as e:
            print(f"Failed to download cascade file: {e}")
            return None
    return cascade_file

# Load the face detection classifier

# Try to load or download the cascade file
cascade_file = download_cascade_file()
face_cascade = None

if cascade_file and os.path.exists(cascade_file):
    face_cascade = cv2.CascadeClassifier(cascade_file)
    if face_cascade.empty():
        print("Warning: Face cascade file is empty or corrupted.")
        face_cascade = None
    else:
        print("Face cascade loaded successfully!")
else:
    print("Warning: Could not load face cascade classifier. Face detection will be skipped.")

with mss.mss() as sct:
    monitor = sct.monitors[1]  # 1 is the main monitor
    screenshot = sct.grab(monitor)
    img_bgra = np.array(screenshot)

    # Convert BGRA to BGR for OpenCV processing
    img_bgr = cv2.cvtColor(img_bgra, cv2.COLOR_BGRA2BGR)

    # Display the original screenshot
    cv2.imshow("Screen", img_bgr)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

# Face Detection
faces = []
if face_cascade is not None:
    gray_for_faces = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray_for_faces, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30))

    # Create a copy for drawing face rectangles
    img_with_faces = img_bgr.copy()

    # Draw rectangles around detected faces
    for (x, y, w, h) in faces:
        cv2.rectangle(img_with_faces, (x, y), (x + w, y + h), (255, 0, 0), 3)  # Blue rectangles for faces

    print(f"Number of faces detected: {len(faces)}")

    # Display image with detected faces
    if len(faces) > 0:
        cv2.imshow("Detected Faces", img_with_faces)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    else:
        print("No faces detected in the screenshot.")
else:
    print("Face detection skipped - cascade classifier not available.")

# Text Detection and OCR with Enhanced Processing
print("\n" + "="*50)
print("ENHANCED TEXT DETECTION AND OCR")
print("="*50)

def preprocess_image_for_ocr(image):
    """Apply multiple preprocessing techniques for better OCR"""
    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Apply different preprocessing techniques
    processed_images = {}

    # 1. Original grayscale
    processed_images['grayscale'] = gray

    # 2. Gaussian blur to reduce noise
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    processed_images['blurred'] = blurred

    # 3. Adaptive thresholding
    adaptive_thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
    processed_images['adaptive_thresh'] = adaptive_thresh

    # 4. OTSU thresholding
    _, otsu_thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    processed_images['otsu_thresh'] = otsu_thresh

    # 5. Morphological operations to clean up
    kernel = np.ones((2,2), np.uint8)
    morph_close = cv2.morphologyEx(otsu_thresh, cv2.MORPH_CLOSE, kernel)
    processed_images['morph_close'] = morph_close

    # 6. Contrast enhancement
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    enhanced = clahe.apply(gray)
    processed_images['enhanced'] = enhanced

    return processed_images

# Preprocess the image with multiple techniques
processed_images = preprocess_image_for_ocr(img_bgr)

# Try OCR with different preprocessing methods and configurations
ocr_configs = [
    '--oem 3 --psm 6',  # Default
    '--oem 3 --psm 8',  # Single word
    '--oem 3 --psm 7',  # Single text line
    '--oem 3 --psm 11', # Sparse text
    '--oem 3 --psm 12', # Sparse text with OSD
    '--oem 3 --psm 3',  # Fully automatic page segmentation
]

best_text = ""
best_confidence = 0
best_method = ""

print("Trying different OCR preprocessing methods...")

for method_name, processed_img in processed_images.items():
    for config in ocr_configs:
        try:
            # Get detailed data for confidence scoring
            data = pytesseract.image_to_data(processed_img, config=config, output_type=pytesseract.Output.DICT)

            # Calculate average confidence for detected text
            confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
            if confidences:
                avg_confidence = sum(confidences) / len(confidences)

                # Extract text
                text = pytesseract.image_to_string(processed_img, config=config).strip()

                if text and avg_confidence > best_confidence:
                    best_confidence = avg_confidence
                    best_text = text
                    best_method = f"{method_name} + {config}"

        except Exception as e:
            continue

# Get the best preprocessing method for bounding boxes
best_processed_img = processed_images['otsu_thresh']  # Usually works well for bounding boxes
data = pytesseract.image_to_data(best_processed_img, output_type=pytesseract.Output.DICT)

# Create a copy for drawing both faces and text boxes
img_combined = img_bgr.copy()

# Draw face rectangles (blue)
for (x, y, w, h) in faces:
    cv2.rectangle(img_combined, (x, y), (x + w, y + h), (255, 0, 0), 3)  # Blue for faces
    cv2.putText(img_combined, 'Face', (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 0, 0), 2)

# Draw text bounding boxes (green) with confidence filtering
n_boxes = len(data['level'])
text_detected = False
detected_words = []

for i in range(n_boxes):
    confidence = int(data['conf'][i])
    if confidence > 30:  # Lower threshold for better detection
        (x, y, w, h) = (data['left'][i], data['top'][i], data['width'][i], data['height'][i])
        text = data['text'][i].strip()

        if text:  # Only draw boxes around actual text
            cv2.rectangle(img_combined, (x, y), (x + w, y + h), (0, 255, 0), 2)  # Green for text
            # Add confidence score
            cv2.putText(img_combined, f'{confidence}%', (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
            text_detected = True
            detected_words.append(f"'{text}' (conf: {confidence}%)")

# Display results
if best_text:
    print(f"\nBest OCR Result (Confidence: {best_confidence:.1f}%):")
    print(f"Method: {best_method}")
    print("Extracted Text:")
    print("-" * 30)
    print(best_text)
    print("-" * 30)
else:
    print("No text detected with sufficient confidence.")

if detected_words:
    print(f"\nDetected Words ({len(detected_words)} total):")
    for word in detected_words[:10]:  # Show first 10 words
        print(f"  {word}")
    if len(detected_words) > 10:
        print(f"  ... and {len(detected_words) - 10} more words")

# Display the combined image with both faces and text boxes
cv2.imshow("Faces (Blue) and Text (Green) Detection", img_combined)
cv2.waitKey(0)
cv2.destroyAllWindows()

# Summary
print("\n" + "="*50)
print("DETECTION SUMMARY")
print("="*50)
print(f"Faces detected: {len(faces)}")
print(f"Text detected: {'Yes' if text_detected else 'No'}")
print(f"Text content: {'Yes' if extracted_text.strip() else 'No'}")
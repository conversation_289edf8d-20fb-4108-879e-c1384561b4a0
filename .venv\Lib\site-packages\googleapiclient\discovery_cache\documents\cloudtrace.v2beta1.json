{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}, "https://www.googleapis.com/auth/trace.append": {"description": "Write Trace data for a project or application"}, "https://www.googleapis.com/auth/trace.readonly": {"description": "Read Trace data for a project or application"}}}}, "basePath": "", "baseUrl": "https://cloudtrace.googleapis.com/", "batchPath": "batch", "canonicalName": "Cloud Trace", "description": "Sends application trace data to Cloud Trace for viewing. Trace data is collected for all App Engine applications by default. Trace data from other applications can be provided using this API. This library is used to interact with the Cloud Trace API directly. If you are looking to instrument your application for Cloud Trace, we recommend using OpenTelemetry. ", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/trace", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "cloudtrace:v2beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://cloudtrace.mtls.googleapis.com/", "name": "cloudtrace", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"traceSinks": {"methods": {"create": {"description": "Creates a sink that exports trace spans to a destination. The export of newly-ingested traces begins immediately, unless the sink's `writer_identity` is not permitted to write to the destination. A sink can export traces only from the resource owning the sink (the 'parent').", "flatPath": "v2beta1/projects/{projectsId}/traceSinks", "httpMethod": "POST", "id": "cloudtrace.projects.traceSinks.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource in which to create the sink (currently only project sinks are supported): \"projects/[PROJECT_ID]\" Examples: `\"projects/my-trace-project\"`, `\"projects/123456789\"`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta1/{+parent}/traceSinks", "request": {"$ref": "TraceSink"}, "response": {"$ref": "TraceSink"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/trace.append"]}, "delete": {"description": "Deletes a sink.", "flatPath": "v2beta1/projects/{projectsId}/traceSinks/{traceSinksId}", "httpMethod": "DELETE", "id": "cloudtrace.projects.traceSinks.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The full resource name of the sink to delete, including the parent resource and the sink identifier: \"projects/[PROJECT_NUMBER]/traceSinks/[SINK_ID]\" Example: `\"projects/12345/traceSinks/my-sink-id\"`.", "location": "path", "pattern": "^projects/[^/]+/traceSinks/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/trace.append"]}, "get": {"description": "Get a trace sink by name under the parent resource (GCP project).", "flatPath": "v2beta1/projects/{projectsId}/traceSinks/{traceSinksId}", "httpMethod": "GET", "id": "cloudtrace.projects.traceSinks.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the sink: \"projects/[PROJECT_NUMBER]/traceSinks/[SINK_ID]\" Example: `\"projects/12345/traceSinks/my-sink-id\"`.", "location": "path", "pattern": "^projects/[^/]+/traceSinks/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta1/{+name}", "response": {"$ref": "TraceSink"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/trace.readonly"]}, "list": {"description": "List all sinks for the parent resource (GCP project).", "flatPath": "v2beta1/projects/{projectsId}/traceSinks", "httpMethod": "GET", "id": "cloudtrace.projects.traceSinks.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of results to return from this request. Non-positive values are ignored. The presence of `next_page_token` in the response indicates that more results might be available.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. If present, then retrieve the next batch of results from the preceding call to this method. `page_token` must be the value of `next_page_token` from the previous response. The values of other method parameters should be identical to those in the previous call.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource whose sinks are to be listed (currently only project parent resources are supported): \"projects/[PROJECT_ID]\"", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta1/{+parent}/traceSinks", "response": {"$ref": "ListTraceSinksResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/trace.readonly"]}, "patch": {"description": "Updates a sink. This method updates fields in the existing sink according to the provided update mask. The sink's name cannot be changed nor any output-only fields (e.g. the writer_identity).", "flatPath": "v2beta1/projects/{projectsId}/traceSinks/{traceSinksId}", "httpMethod": "PATCH", "id": "cloudtrace.projects.traceSinks.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The full resource name of the sink to update, including the parent resource and the sink identifier: \"projects/[PROJECT_NUMBER]/traceSinks/[SINK_ID]\" Example: `\"projects/12345/traceSinks/my-sink-id\"`.", "location": "path", "pattern": "^projects/[^/]+/traceSinks/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Field mask that specifies the fields in `trace_sink` that are to be updated. A sink field is overwritten if, and only if, it is in the update mask. `name` and `writer_identity` fields cannot be updated. An empty `update_mask` is considered an error. For a detailed `FieldMask` definition, see https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask Example: `updateMask=output_config`.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v2beta1/{+name}", "request": {"$ref": "TraceSink"}, "response": {"$ref": "TraceSink"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/trace.append"]}}}}}}, "revision": "20240712", "rootUrl": "https://cloudtrace.googleapis.com/", "schemas": {"Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "ListTraceSinksResponse": {"description": "<PERSON><PERSON>t returned from `ListTraceSinks`.", "id": "ListTraceSinksResponse", "properties": {"nextPageToken": {"description": "A paginated response where more pages might be available has `next_page_token` set. To get the next set of results, call the same method again using the value of `next_page_token` as `page_token`.", "type": "string"}, "sinks": {"description": "A list of sinks.", "items": {"$ref": "TraceSink"}, "type": "array"}}, "type": "object"}, "OutputConfig": {"description": "OutputConfig contains a destination for writing trace data.", "id": "OutputConfig", "properties": {"destination": {"description": "Required. The destination for writing trace data. Supported formats include: \"bigquery.googleapis.com/projects/[PROJECT_ID]/datasets/[DATASET]\"", "type": "string"}}, "type": "object"}, "TraceSink": {"description": "Describes a sink used to export traces to a BigQuery dataset. The sink must be created within a project.", "id": "TraceSink", "properties": {"name": {"description": "Identifier. The canonical sink resource name, unique within the project. Must be of the form: projects/[PROJECT_NUMBER]/traceSinks/[SINK_ID]. E.g.: `\"projects/12345/traceSinks/my-project-trace-sink\"`. Sink identifiers are limited to 256 characters and can include only the following characters: upper and lower-case alphanumeric characters, underscores, hyphens, and periods.", "type": "string"}, "outputConfig": {"$ref": "OutputConfig", "description": "Required. The export destination."}, "writerIdentity": {"description": "Output only. A service account name for exporting the data. This field is set by sinks.create and sinks.update. The service account will need to be granted write access to the destination specified in the output configuration, see [Granting access for a resource](/iam/docs/granting-roles-to-service-accounts#granting_access_to_a_service_account_for_a_resource). To create tables and to write data, this account needs the `dataEditor` role. Read more about roles in the [BigQuery documentation](https://cloud.google.com/bigquery/docs/access-control). E.g.: \"<EMAIL>\"", "readOnly": true, "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Cloud Trace API", "version": "v2beta1", "version_module": true}
{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://apihub.googleapis.com/", "batchPath": "batch", "canonicalName": "API hub", "description": "", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/apigee/docs/api-hub/what-is-api-hub", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "apihub:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://apihub.mtls.googleapis.com/", "name": "<PERSON><PERSON><PERSON><PERSON>", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"collectApiData": {"description": "Collect API data from a source and push it to Hub's collect layer.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}:collectApiData", "httpMethod": "POST", "id": "apihub.projects.locations.collectApiData", "parameterOrder": ["location"], "parameters": {"location": {"description": "Required. The regional location of the API hub instance and its resources. Format: `projects/{project}/locations/{location}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+location}:collectApiData", "request": {"$ref": "GoogleCloudApihubV1CollectApiDataRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets information about a location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "apihub.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudLocationLocation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "apihub.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1/{+name}/locations", "response": {"$ref": "GoogleCloudLocationListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "lookupRuntimeProjectAttachment": {"description": "Look up a runtime project attachment. This API can be called in the context of any project.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}:lookupRuntimeProjectAttachment", "httpMethod": "GET", "id": "apihub.projects.locations.lookupRuntimeProjectAttachment", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Runtime project ID to look up runtime project attachment for. Lookup happens across all regions. Expected format: `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:lookupRuntimeProjectAttachment", "response": {"$ref": "GoogleCloudApihubV1LookupRuntimeProjectAttachmentResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "searchResources": {"description": "Search across API-Hub resources.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}:searchResources", "httpMethod": "POST", "id": "apihub.projects.locations.searchResources", "parameterOrder": ["location"], "parameters": {"location": {"description": "Required. The resource name of the location which will be of the type `projects/{project_id}/locations/{location_id}`. This field is used to identify the instance of API-Hub in which resources should be searched.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+location}:searchResources", "request": {"$ref": "GoogleCloudApihubV1SearchResourcesRequest"}, "response": {"$ref": "GoogleCloudApihubV1SearchResourcesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"apiHubInstances": {"methods": {"create": {"description": "Provisions instance resources for the API Hub.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apiHubInstances", "httpMethod": "POST", "id": "apihub.projects.locations.apiHubInstances.create", "parameterOrder": ["parent"], "parameters": {"apiHubInstanceId": {"description": "Optional. Identifier to assign to the Api Hub instance. Must be unique within scope of the parent resource. If the field is not provided, system generated id will be used. This value should be 4-40 characters, and valid characters are `/a-z[0-9]-_/`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource for the Api Hub instance resource. Format: `projects/{project}/locations/{location}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/apiHubInstances", "request": {"$ref": "GoogleCloudApihubV1ApiHubInstance"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the API hub instance.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apiHubInstances/{apiHubInstancesId}", "httpMethod": "DELETE", "id": "apihub.projects.locations.apiHubInstances.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the Api Hub instance to delete. Format: `projects/{project}/locations/{location}/apiHubInstances/{apiHubInstance}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apiHubInstances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single API Hub instance.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apiHubInstances/{apiHubInstancesId}", "httpMethod": "GET", "id": "apihub.projects.locations.apiHubInstances.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the Api Hub instance to retrieve. Format: `projects/{project}/locations/{location}/apiHubInstances/{apiHubInstance}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apiHubInstances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudApihubV1ApiHubInstance"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "lookup": {"description": "Looks up an Api Hub instance in a given GCP project. There will always be only one Api Hub instance for a GCP project across all locations.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apiHubInstances:lookup", "httpMethod": "GET", "id": "apihub.projects.locations.apiHubInstances.lookup", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. There will always be only one Api Hub instance for a GCP project across all locations. The parent resource for the Api Hub instance resource. Format: `projects/{project}/locations/{location}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/apiHubInstances:lookup", "response": {"$ref": "GoogleCloudApihubV1LookupApiHubInstanceResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "apis": {"methods": {"create": {"description": "Create an API resource in the API hub. Once an API resource is created, versions can be added to it.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis", "httpMethod": "POST", "id": "apihub.projects.locations.apis.create", "parameterOrder": ["parent"], "parameters": {"apiId": {"description": "Optional. The ID to use for the API resource, which will become the final component of the API's resource name. This field is optional. * If provided, the same will be used. The service will throw an error if the specified id is already used by another API resource in the API hub. * If not provided, a system generated id will be used. This value should be 4-500 characters, and valid characters are /a-z[0-9]-_/.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource for the API resource. Format: `projects/{project}/locations/{location}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/apis", "request": {"$ref": "GoogleCloudApihubV1Api"}, "response": {"$ref": "GoogleCloudApihubV1Api"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete an API resource in the API hub. API can only be deleted if all underlying versions are deleted.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}", "httpMethod": "DELETE", "id": "apihub.projects.locations.apis.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "Optional. If set to true, any versions from this API will also be deleted. Otherwise, the request will only work if the API has no versions.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The name of the API resource to delete. Format: `projects/{project}/locations/{location}/apis/{api}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get API resource details including the API versions contained in it.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}", "httpMethod": "GET", "id": "apihub.projects.locations.apis.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the API resource to retrieve. Format: `projects/{project}/locations/{location}/apis/{api}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudApihubV1Api"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List API resources in the API hub.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis", "httpMethod": "GET", "id": "apihub.projects.locations.apis.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. An expression that filters the list of ApiResources. A filter expression consists of a field name, a comparison operator, and a value for filtering. The value must be a string. The comparison operator must be one of: `<`, `>`, `:` or `=`. Filters are not case sensitive. The following fields in the `ApiResource` are eligible for filtering: * `owner.email` - The email of the team which owns the ApiResource. Allowed comparison operators: `=`. * `create_time` - The time at which the ApiResource was created. The value should be in the (RFC3339)[https://tools.ietf.org/html/rfc3339] format. Allowed comparison operators: `>` and `<`. * `display_name` - The display name of the ApiResource. Allowed comparison operators: `=`. * `target_user.enum_values.values.id` - The allowed value id of the target users attribute associated with the ApiResource. Allowed comparison operator is `:`. * `target_user.enum_values.values.display_name` - The allowed value display name of the target users attribute associated with the ApiResource. Allowed comparison operator is `:`. * `team.enum_values.values.id` - The allowed value id of the team attribute associated with the ApiResource. Allowed comparison operator is `:`. * `team.enum_values.values.display_name` - The allowed value display name of the team attribute associated with the ApiResource. Allowed comparison operator is `:`. * `business_unit.enum_values.values.id` - The allowed value id of the business unit attribute associated with the ApiResource. Allowed comparison operator is `:`. * `business_unit.enum_values.values.display_name` - The allowed value display name of the business unit attribute associated with the ApiResource. Allowed comparison operator is `:`. * `maturity_level.enum_values.values.id` - The allowed value id of the maturity level attribute associated with the ApiResource. Allowed comparison operator is `:`. * `maturity_level.enum_values.values.display_name` - The allowed value display name of the maturity level attribute associated with the ApiResource. Allowed comparison operator is `:`. * `api_style.enum_values.values.id` - The allowed value id of the api style attribute associated with the ApiResource. Allowed comparison operator is `:`. * `api_style.enum_values.values.display_name` - The allowed value display name of the api style attribute associated with the ApiResource. Allowed comparison operator is `:`. * `attributes.projects/test-project-id/locations/test-location-id/ attributes/user-defined-attribute-id.enum_values.values.id` - The allowed value id of the user defined enum attribute associated with the Resource. Allowed comparison operator is `:`. Here user-defined-attribute-enum-id is a placeholder that can be replaced with any user defined enum attribute name. * `attributes.projects/test-project-id/locations/test-location-id/ attributes/user-defined-attribute-id.enum_values.values.display_name` - The allowed value display name of the user defined enum attribute associated with the Resource. Allowed comparison operator is `:`. Here user-defined-attribute-enum-display-name is a placeholder that can be replaced with any user defined enum attribute enum name. * `attributes.projects/test-project-id/locations/test-location-id/ attributes/user-defined-attribute-id.string_values.values` - The allowed value of the user defined string attribute associated with the Resource. Allowed comparison operator is `:`. Here user-defined-attribute-string is a placeholder that can be replaced with any user defined string attribute name. * `attributes.projects/test-project-id/locations/test-location-id/ attributes/user-defined-attribute-id.json_values.values` - The allowed value of the user defined JSON attribute associated with the Resource. Allowed comparison operator is `:`. Here user-defined-attribute-json is a placeholder that can be replaced with any user defined JSON attribute name. A filter function is also supported in the filter string. The filter function is `id(name)`. The `id(name)` function returns the id of the resource name. For example, `id(name) = \\\"api-1\\\"` is equivalent to `name = \\\"projects/test-project-id/locations/test-location-id/apis/api-1\\\"` provided the parent is `projects/test-project-id/locations/test-location-id`. Expressions are combined with either `AND` logic operator or `OR` logical operator but not both of them together i.e. only one of the `AND` or `OR` operator can be used throughout the filter string and both the operators cannot be used together. No other logical operators are supported. At most three filter fields are allowed in the filter string and if provided more than that then `INVALID_ARGUMENT` error is returned by the API. Here are a few examples: * `owner.email = \\\"<EMAIL>\\\"` - - The owner team <NAME_EMAIL>_. * `owner.email = \\\"<EMAIL>\\\" AND create_time < \\\"2021-08-15T14:50:00Z\\\" AND create_time > \\\"2021-08-10T12:00:00Z\\\"` - The owner team <NAME_EMAIL>_ and the api was created before _2021-08-15 14:50:00 UTC_ and after _2021-08-10 12:00:00 UTC_. * `owner.email = \\\"<EMAIL>\\\" OR team.enum_values.values.id: apihub-team-id` - The filter string specifies the APIs where the owner team <NAME_EMAIL>_ or the id of the allowed value associated with the team attribute is _apihub-team-id_. * `owner.email = \\\"<EMAIL>\\\" OR team.enum_values.values.display_name: ApiHub Team` - The filter string specifies the APIs where the owner team <NAME_EMAIL>_ or the display name of the allowed value associated with the team attribute is `ApiHub Team`. * `owner.email = \\\"<EMAIL>\\\" AND attributes.projects/test-project-id/locations/test-location-id/ attributes/17650f90-4a29-4971-b3c0-d5532da3764b.enum_values.values.id: test_enum_id AND attributes.projects/test-project-id/locations/test-location-id/ attributes/1765\\0f90-4a29-5431-b3d0-d5532da3764c.string_values.values: test_string_value` - The filter string specifies the APIs where the owner team <NAME_EMAIL>_ and the id of the allowed value associated with the user defined attribute of type enum is _test_enum_id_ and the value of the user defined attribute of type string is _test_..", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of API resources to return. The service may return fewer than this value. If unspecified, at most 50 Apis will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListApis` call. Provide this to retrieve the subsequent page. When paginating, all other parameters (except page_size) provided to `ListApis` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of API resources. Format: `projects/{project}/locations/{location}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/apis", "response": {"$ref": "GoogleCloudApihubV1ListApisResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update an API resource in the API hub. The following fields in the API can be updated: * display_name * description * owner * documentation * target_user * team * business_unit * maturity_level * api_style * attributes The update_mask should be used to specify the fields being updated. Updating the owner field requires complete owner message and updates both owner and email fields.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}", "httpMethod": "PATCH", "id": "apihub.projects.locations.apis.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The name of the API resource in the API Hub. Format: `projects/{project}/locations/{location}/apis/{api}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudApihubV1Api"}, "response": {"$ref": "GoogleCloudApihubV1Api"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"versions": {"methods": {"create": {"description": "Create an API version for an API resource in the API hub.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions", "httpMethod": "POST", "id": "apihub.projects.locations.apis.versions.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource for API version. Format: `projects/{project}/locations/{location}/apis/{api}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+$", "required": true, "type": "string"}, "versionId": {"description": "Optional. The ID to use for the API version, which will become the final component of the version's resource name. This field is optional. * If provided, the same will be used. The service will throw an error if the specified id is already used by another version in the API resource. * If not provided, a system generated id will be used. This value should be 4-500 characters, overall resource name which will be of format `projects/{project}/locations/{location}/apis/{api}/versions/{version}`, its length is limited to 700 characters and valid characters are /a-z[0-9]-_/.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/versions", "request": {"$ref": "GoogleCloudApihubV1Version"}, "response": {"$ref": "GoogleCloudApihubV1Version"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete an API version. Version can only be deleted if all underlying specs, operations, definitions and linked deployments are deleted.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}", "httpMethod": "DELETE", "id": "apihub.projects.locations.apis.versions.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "Optional. If set to true, any specs from this version will also be deleted. Otherwise, the request will only work if the version has no specs.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The name of the version to delete. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/versions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get details about the API version of an API resource. This will include information about the specs and operations present in the API version as well as the deployments linked to it.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}", "httpMethod": "GET", "id": "apihub.projects.locations.apis.versions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the API version to retrieve. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/versions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudApihubV1Version"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List API versions of an API resource in the API hub.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions", "httpMethod": "GET", "id": "apihub.projects.locations.apis.versions.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. An expression that filters the list of Versions. A filter expression consists of a field name, a comparison operator, and a value for filtering. The value must be a string, a number, or a boolean. The comparison operator must be one of: `<`, `>` or `=`. Filters are not case sensitive. The following fields in the `Version` are eligible for filtering: * `display_name` - The display name of the Version. Allowed comparison operators: `=`. * `create_time` - The time at which the Version was created. The value should be in the (RFC3339)[https://tools.ietf.org/html/rfc3339] format. Allowed comparison operators: `>` and `<`. * `lifecycle.enum_values.values.id` - The allowed value id of the lifecycle attribute associated with the Version. Allowed comparison operators: `:`. * `lifecycle.enum_values.values.display_name` - The allowed value display name of the lifecycle attribute associated with the Version. Allowed comparison operators: `:`. * `compliance.enum_values.values.id` - The allowed value id of the compliances attribute associated with the Version. Allowed comparison operators: `:`. * `compliance.enum_values.values.display_name` - The allowed value display name of the compliances attribute associated with the Version. Allowed comparison operators: `:`. * `accreditation.enum_values.values.id` - The allowed value id of the accreditations attribute associated with the Version. Allowed comparison operators: `:`. * `accreditation.enum_values.values.display_name` - The allowed value display name of the accreditations attribute associated with the Version. Allowed comparison operators: `:`. * `attributes.projects/test-project-id/locations/test-location-id/ attributes/user-defined-attribute-id.enum_values.values.id` - The allowed value id of the user defined enum attribute associated with the Resource. Allowed comparison operator is `:`. Here user-defined-attribute-enum-id is a placeholder that can be replaced with any user defined enum attribute name. * `attributes.projects/test-project-id/locations/test-location-id/ attributes/user-defined-attribute-id.enum_values.values.display_name` - The allowed value display name of the user defined enum attribute associated with the Resource. Allowed comparison operator is `:`. Here user-defined-attribute-enum-display-name is a placeholder that can be replaced with any user defined enum attribute enum name. * `attributes.projects/test-project-id/locations/test-location-id/ attributes/user-defined-attribute-id.string_values.values` - The allowed value of the user defined string attribute associated with the Resource. Allowed comparison operator is `:`. Here user-defined-attribute-string is a placeholder that can be replaced with any user defined string attribute name. * `attributes.projects/test-project-id/locations/test-location-id/ attributes/user-defined-attribute-id.json_values.values` - The allowed value of the user defined JSON attribute associated with the Resource. Allowed comparison operator is `:`. Here user-defined-attribute-json is a placeholder that can be replaced with any user defined JSON attribute name. Expressions are combined with either `AND` logic operator or `OR` logical operator but not both of them together i.e. only one of the `AND` or `OR` operator can be used throughout the filter string and both the operators cannot be used together. No other logical operators are supported. At most three filter fields are allowed in the filter string and if provided more than that then `INVALID_ARGUMENT` error is returned by the API. Here are a few examples: * `lifecycle.enum_values.values.id: preview-id` - The filter string specifies that the id of the allowed value associated with the lifecycle attribute of the Version is _preview-id_. * `lifecycle.enum_values.values.display_name: \\\"Preview Display Name\\\"` - The filter string specifies that the display name of the allowed value associated with the lifecycle attribute of the Version is `Preview Display Name`. * `lifecycle.enum_values.values.id: preview-id AND create_time < \\\"2021-08-15T14:50:00Z\\\" AND create_time > \\\"2021-08-10T12:00:00Z\\\"` - The id of the allowed value associated with the lifecycle attribute of the Version is _preview-id_ and it was created before _2021-08-15 14:50:00 UTC_ and after _2021-08-10 12:00:00 UTC_. * `compliance.enum_values.values.id: gdpr-id OR compliance.enum_values.values.id: pci-dss-id` - The id of the allowed value associated with the compliance attribute is _gdpr-id_ or _pci-dss-id_. * `lifecycle.enum_values.values.id: preview-id AND attributes.projects/test-project-id/locations/test-location-id/ attributes/17650f90-4a29-4971-b3c0-d5532da3764b.string_values.values: test` - The filter string specifies that the id of the allowed value associated with the lifecycle attribute of the Version is _preview-id_ and the value of the user defined attribute of type string is _test_.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of versions to return. The service may return fewer than this value. If unspecified, at most 50 versions will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListVersions` call. Provide this to retrieve the subsequent page. When paginating, all other parameters (except page_size) provided to `ListVersions` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent which owns this collection of API versions i.e., the API resource Format: `projects/{project}/locations/{location}/apis/{api}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/versions", "response": {"$ref": "GoogleCloudApihubV1ListVersionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update API version. The following fields in the version can be updated currently: * display_name * description * documentation * deployments * lifecycle * compliance * accreditation * attributes The update_mask should be used to specify the fields being updated.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}", "httpMethod": "PATCH", "id": "apihub.projects.locations.apis.versions.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The name of the version. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/versions/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudApihubV1Version"}, "response": {"$ref": "GoogleCloudApihubV1Version"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"definitions": {"methods": {"get": {"description": "Get details about a definition in an API version.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/definitions/{definitionsId}", "httpMethod": "GET", "id": "apihub.projects.locations.apis.versions.definitions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the definition to retrieve. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}/definitions/{definition}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/versions/[^/]+/definitions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudApihubV1Definition"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"create": {"description": "Create an apiOperation in an API version. An apiOperation can be created only if the version has no apiOperations which were created by parsing a spec.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/operations", "httpMethod": "POST", "id": "apihub.projects.locations.apis.versions.operations.create", "parameterOrder": ["parent"], "parameters": {"apiOperationId": {"description": "Optional. The ID to use for the operation resource, which will become the final component of the operation's resource name. This field is optional. * If provided, the same will be used. The service will throw an error if the specified id is already used by another operation resource in the API hub. * If not provided, a system generated id will be used. This value should be 4-500 characters, overall resource name which will be of format `projects/{project}/locations/{location}/apis/{api}/versions/{version}/operations/{operation}`, its length is limited to 700 characters, and valid characters are /a-z[0-9]-_/.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource for the operation resource. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/versions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/operations", "request": {"$ref": "GoogleCloudApihubV1ApiOperation"}, "response": {"$ref": "GoogleCloudApihubV1ApiOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete an operation in an API version and we can delete only the operations created via create API. If the operation was created by parsing the spec, then it can be deleted by editing or deleting the spec.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "apihub.projects.locations.apis.versions.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the operation resource to delete. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}/operations/{operation}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/versions/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get details about a particular operation in API version.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/operations/{operationsId}", "httpMethod": "GET", "id": "apihub.projects.locations.apis.versions.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the operation to retrieve. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}/operations/{operation}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/versions/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudApihubV1ApiOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List operations in an API version.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/operations", "httpMethod": "GET", "id": "apihub.projects.locations.apis.versions.operations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. An expression that filters the list of ApiOperations. A filter expression consists of a field name, a comparison operator, and a value for filtering. The value must be a string or a boolean. The comparison operator must be one of: `<`, `>` or `=`. Filters are not case sensitive. The following fields in the `ApiOperation` are eligible for filtering: * `name` - The ApiOperation resource name. Allowed comparison operators: `=`. * `details.http_operation.path.path` - The http operation's complete path relative to server endpoint. Allowed comparison operators: `=`. * `details.http_operation.method` - The http operation method type. Allowed comparison operators: `=`. * `details.deprecated` - Indicates if the ApiOperation is deprecated. Allowed values are True / False indicating the deprycation status of the ApiOperation. Allowed comparison operators: `=`. * `create_time` - The time at which the ApiOperation was created. The value should be in the (RFC3339)[https://tools.ietf.org/html/rfc3339] format. Allowed comparison operators: `>` and `<`. * `attributes.projects/test-project-id/locations/test-location-id/ attributes/user-defined-attribute-id.enum_values.values.id` - The allowed value id of the user defined enum attribute associated with the Resource. Allowed comparison operator is `:`. Here user-defined-attribute-enum-id is a placeholder that can be replaced with any user defined enum attribute name. * `attributes.projects/test-project-id/locations/test-location-id/ attributes/user-defined-attribute-id.enum_values.values.display_name` - The allowed value display name of the user defined enum attribute associated with the Resource. Allowed comparison operator is `:`. Here user-defined-attribute-enum-display-name is a placeholder that can be replaced with any user defined enum attribute enum name. * `attributes.projects/test-project-id/locations/test-location-id/ attributes/user-defined-attribute-id.string_values.values` - The allowed value of the user defined string attribute associated with the Resource. Allowed comparison operator is `:`. Here user-defined-attribute-string is a placeholder that can be replaced with any user defined string attribute name. * `attributes.projects/test-project-id/locations/test-location-id/ attributes/user-defined-attribute-id.json_values.values` - The allowed value of the user defined JSON attribute associated with the Resource. Allowed comparison operator is `:`. Here user-defined-attribute-json is a placeholder that can be replaced with any user defined JSON attribute name. Expressions are combined with either `AND` logic operator or `OR` logical operator but not both of them together i.e. only one of the `AND` or `OR` operator can be used throughout the filter string and both the operators cannot be used together. No other logical operators are supported. At most three filter fields are allowed in the filter string and if provided more than that then `INVALID_ARGUMENT` error is returned by the API. Here are a few examples: * `details.deprecated = True` - The ApiOperation is deprecated. * `details.http_operation.method = GET AND create_time < \\\"2021-08-15T14:50:00Z\\\" AND create_time > \\\"2021-08-10T12:00:00Z\\\"` - The method of the http operation of the ApiOperation is _GET_ and the spec was created before _2021-08-15 14:50:00 UTC_ and after _2021-08-10 12:00:00 UTC_. * `details.http_operation.method = GET OR details.http_operation.method = POST`. - The http operation of the method of ApiOperation is _GET_ or _POST_. * `details.deprecated = True AND attributes.projects/test-project-id/locations/test-location-id/ attributes/17650f90-4a29-4971-b3c0-d5532da3764b.string_values.values: test` - The filter string specifies that the ApiOperation is deprecated and the value of the user defined attribute of type string is _test_.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of operations to return. The service may return fewer than this value. If unspecified, at most 50 operations will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListApiOperations` call. Provide this to retrieve the subsequent page. When paginating, all other parameters (except page_size) provided to `ListApiOperations` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent which owns this collection of operations i.e., the API version. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/versions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/operations", "response": {"$ref": "GoogleCloudApihubV1ListApiOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update an operation in an API version. The following fields in the ApiOperation resource can be updated: * details.description * details.documentation * details.http_operation.path * details.http_operation.method * details.deprecated * attributes The update_mask should be used to specify the fields being updated. An operation can be updated only if the operation was created via CreateApiOperation API. If the operation was created by parsing the spec, then it can be edited by updating the spec.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/operations/{operationsId}", "httpMethod": "PATCH", "id": "apihub.projects.locations.apis.versions.operations.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The name of the operation. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}/operations/{operation}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/versions/[^/]+/operations/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudApihubV1ApiOperation"}, "response": {"$ref": "GoogleCloudApihubV1ApiOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "specs": {"methods": {"create": {"description": "Add a spec to an API version in the API hub. Multiple specs can be added to an API version. Note, while adding a spec, at least one of `contents` or `source_uri` must be provided. If `contents` is provided, then `spec_type` must also be provided. On adding a spec with contents to the version, the operations present in it will be added to the version.Note that the file contents in the spec should be of the same type as defined in the `projects/{project}/locations/{location}/attributes/system-spec-type` attribute associated with spec resource. Note that specs of various types can be uploaded, however parsing of details is supported for OpenAPI spec currently. In order to access the information parsed from the spec, use the GetSpec method. In order to access the raw contents for a particular spec, use the GetSpecContents method. In order to access the operations parsed from the spec, use the ListAPIOperations method.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/specs", "httpMethod": "POST", "id": "apihub.projects.locations.apis.versions.specs.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource for Spec. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/versions/[^/]+$", "required": true, "type": "string"}, "specId": {"description": "Optional. The ID to use for the spec, which will become the final component of the spec's resource name. This field is optional. * If provided, the same will be used. The service will throw an error if the specified id is already used by another spec in the API resource. * If not provided, a system generated id will be used. This value should be 4-500 characters, overall resource name which will be of format `projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`, its length is limited to 1000 characters and valid characters are /a-z[0-9]-_/.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/specs", "request": {"$ref": "GoogleCloudApihubV1Spec"}, "response": {"$ref": "GoogleCloudApihubV1Spec"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a spec. Deleting a spec will also delete the associated operations from the version.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/specs/{specsId}", "httpMethod": "DELETE", "id": "apihub.projects.locations.apis.versions.specs.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the spec to delete. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/versions/[^/]+/specs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get details about the information parsed from a spec. Note that this method does not return the raw spec contents. Use GetSpecContents method to retrieve the same.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/specs/{specsId}", "httpMethod": "GET", "id": "apihub.projects.locations.apis.versions.specs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the spec to retrieve. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/versions/[^/]+/specs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudApihubV1Spec"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getContents": {"description": "Get spec contents.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/specs/{specsId}:contents", "httpMethod": "GET", "id": "apihub.projects.locations.apis.versions.specs.getContents", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the spec whose contents need to be retrieved. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/versions/[^/]+/specs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:contents", "response": {"$ref": "GoogleCloudApihubV1SpecContents"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "lint": {"description": "Lints the requested spec and updates the corresponding API Spec with the lint response. This lint response will be available in all subsequent Get and List Spec calls to Core service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/specs/{specsId}:lint", "httpMethod": "POST", "id": "apihub.projects.locations.apis.versions.specs.lint", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the spec to be linted. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/versions/[^/]+/specs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:lint", "request": {"$ref": "GoogleCloudApihubV1LintSpecRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List specs corresponding to a particular API resource.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/specs", "httpMethod": "GET", "id": "apihub.projects.locations.apis.versions.specs.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. An expression that filters the list of Specs. A filter expression consists of a field name, a comparison operator, and a value for filtering. The value must be a string. The comparison operator must be one of: `<`, `>`, `:` or `=`. Filters are not case sensitive. The following fields in the `Spec` are eligible for filtering: * `display_name` - The display name of the Spec. Allowed comparison operators: `=`. * `create_time` - The time at which the Spec was created. The value should be in the (RFC3339)[https://tools.ietf.org/html/rfc3339] format. Allowed comparison operators: `>` and `<`. * `spec_type.enum_values.values.id` - The allowed value id of the spec_type attribute associated with the Spec. Allowed comparison operators: `:`. * `spec_type.enum_values.values.display_name` - The allowed value display name of the spec_type attribute associated with the Spec. Allowed comparison operators: `:`. * `lint_response.json_values.values` - The json value of the lint_response attribute associated with the Spec. Allowed comparison operators: `:`. * `mime_type` - The MIME type of the Spec. Allowed comparison operators: `=`. * `attributes.projects/test-project-id/locations/test-location-id/ attributes/user-defined-attribute-id.enum_values.values.id` - The allowed value id of the user defined enum attribute associated with the Resource. Allowed comparison operator is `:`. Here user-defined-attribute-enum-id is a placeholder that can be replaced with any user defined enum attribute name. * `attributes.projects/test-project-id/locations/test-location-id/ attributes/user-defined-attribute-id.enum_values.values.display_name` - The allowed value display name of the user defined enum attribute associated with the Resource. Allowed comparison operator is `:`. Here user-defined-attribute-enum-display-name is a placeholder that can be replaced with any user defined enum attribute enum name. * `attributes.projects/test-project-id/locations/test-location-id/ attributes/user-defined-attribute-id.string_values.values` - The allowed value of the user defined string attribute associated with the Resource. Allowed comparison operator is `:`. Here user-defined-attribute-string is a placeholder that can be replaced with any user defined string attribute name. * `attributes.projects/test-project-id/locations/test-location-id/ attributes/user-defined-attribute-id.json_values.values` - The allowed value of the user defined JSON attribute associated with the Resource. Allowed comparison operator is `:`. Here user-defined-attribute-json is a placeholder that can be replaced with any user defined JSON attribute name. Expressions are combined with either `AND` logic operator or `OR` logical operator but not both of them together i.e. only one of the `AND` or `OR` operator can be used throughout the filter string and both the operators cannot be used together. No other logical operators are supported. At most three filter fields are allowed in the filter string and if provided more than that then `INVALID_ARGUMENT` error is returned by the API. Here are a few examples: * `spec_type.enum_values.values.id: rest-id` - The filter string specifies that the id of the allowed value associated with the spec_type attribute is _rest-id_. * `spec_type.enum_values.values.display_name: \\\"Rest Display Name\\\"` - The filter string specifies that the display name of the allowed value associated with the spec_type attribute is `Rest Display Name`. * `spec_type.enum_values.values.id: grpc-id AND create_time < \\\"2021-08-15T14:50:00Z\\\" AND create_time > \\\"2021-08-10T12:00:00Z\\\"` - The id of the allowed value associated with the spec_type attribute is _grpc-id_ and the spec was created before _2021-08-15 14:50:00 UTC_ and after _2021-08-10 12:00:00 UTC_. * `spec_type.enum_values.values.id: rest-id OR spec_type.enum_values.values.id: grpc-id` - The id of the allowed value associated with the spec_type attribute is _rest-id_ or _grpc-id_. * `spec_type.enum_values.values.id: rest-id AND attributes.projects/test-project-id/locations/test-location-id/ attributes/17650f90-4a29-4971-b3c0-d5532da3764b.enum_values.values.id: test` - The filter string specifies that the id of the allowed value associated with the spec_type attribute is _rest-id_ and the id of the allowed value associated with the user defined attribute of type enum is _test_.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of specs to return. The service may return fewer than this value. If unspecified, at most 50 specs will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListSpecs` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListSpecs` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of specs. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/versions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/specs", "response": {"$ref": "GoogleCloudApihubV1ListSpecsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update spec. The following fields in the spec can be updated: * display_name * source_uri * lint_response * attributes * contents * spec_type In case of an OAS spec, updating spec contents can lead to: 1. Creation, deletion and update of operations. 2. Creation, deletion and update of definitions. 3. Update of other info parsed out from the new spec. In case of contents or source_uri being present in update mask, spec_type must also be present. Also, spec_type can not be present in update mask if contents or source_uri is not present. The update_mask should be used to specify the fields being updated.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/versions/{versionsId}/specs/{specsId}", "httpMethod": "PATCH", "id": "apihub.projects.locations.apis.versions.specs.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The name of the spec. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/versions/[^/]+/specs/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudApihubV1Spec"}, "response": {"$ref": "GoogleCloudApihubV1Spec"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "attributes": {"methods": {"create": {"description": "Create a user defined attribute. Certain pre defined attributes are already created by the API hub. These attributes will have type as `SYSTEM_DEFINED` and can be listed via ListAttributes method. Allowed values for the same can be updated via UpdateAttribute method.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/attributes", "httpMethod": "POST", "id": "apihub.projects.locations.attributes.create", "parameterOrder": ["parent"], "parameters": {"attributeId": {"description": "Optional. The ID to use for the attribute, which will become the final component of the attribute's resource name. This field is optional. * If provided, the same will be used. The service will throw an error if the specified id is already used by another attribute resource in the API hub. * If not provided, a system generated id will be used. This value should be 4-500 characters, and valid characters are /a-z[0-9]-_/.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource for Attribute. Format: `projects/{project}/locations/{location}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/attributes", "request": {"$ref": "GoogleCloudApihubV1Attribute"}, "response": {"$ref": "GoogleCloudApihubV1Attribute"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete an attribute. Note: System defined attributes cannot be deleted. All associations of the attribute being deleted with any API hub resource will also get deleted.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/attributes/{attributesId}", "httpMethod": "DELETE", "id": "apihub.projects.locations.attributes.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the attribute to delete. Format: `projects/{project}/locations/{location}/attributes/{attribute}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/attributes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get details about the attribute.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/attributes/{attributesId}", "httpMethod": "GET", "id": "apihub.projects.locations.attributes.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the attribute to retrieve. Format: `projects/{project}/locations/{location}/attributes/{attribute}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/attributes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudApihubV1Attribute"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List all attributes.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/attributes", "httpMethod": "GET", "id": "apihub.projects.locations.attributes.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. An expression that filters the list of Attributes. A filter expression consists of a field name, a comparison operator, and a value for filtering. The value must be a string or a boolean. The comparison operator must be one of: `<`, `>` or `=`. Filters are not case sensitive. The following fields in the `Attribute` are eligible for filtering: * `display_name` - The display name of the Attribute. Allowed comparison operators: `=`. * `definition_type` - The definition type of the attribute. Allowed comparison operators: `=`. * `scope` - The scope of the attribute. Allowed comparison operators: `=`. * `data_type` - The type of the data of the attribute. Allowed comparison operators: `=`. * `mandatory` - Denotes whether the attribute is mandatory or not. Allowed comparison operators: `=`. * `create_time` - The time at which the Attribute was created. The value should be in the (RFC3339)[https://tools.ietf.org/html/rfc3339] format. Allowed comparison operators: `>` and `<`. Expressions are combined with either `AND` logic operator or `OR` logical operator but not both of them together i.e. only one of the `AND` or `OR` operator can be used throughout the filter string and both the operators cannot be used together. No other logical operators are supported. At most three filter fields are allowed in the filter string and if provided more than that then `INVALID_ARGUMENT` error is returned by the API. Here are a few examples: * `display_name = production` - - The display name of the attribute is _production_. * `(display_name = production) AND (create_time < \\\"2021-08-15T14:50:00Z\\\") AND (create_time > \\\"2021-08-10T12:00:00Z\\\")` - The display name of the attribute is _production_ and the attribute was created before _2021-08-15 14:50:00 UTC_ and after _2021-08-10 12:00:00 UTC_. * `display_name = production OR scope = api` - The attribute where the display name is _production_ or the scope is _api_.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of attribute resources to return. The service may return fewer than this value. If unspecified, at most 50 attributes will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListAttributes` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListAttributes` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource for Attribute. Format: `projects/{project}/locations/{location}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/attributes", "response": {"$ref": "GoogleCloudApihubV1ListAttributesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update the attribute. The following fields in the Attribute resource can be updated: * display_name The display name can be updated for user defined attributes only. * description The description can be updated for user defined attributes only. * allowed_values To update the list of allowed values, clients need to use the fetched list of allowed values and add or remove values to or from the same list. The mutable allowed values can be updated for both user defined and System defined attributes. The immutable allowed values cannot be updated or deleted. The updated list of allowed values cannot be empty. If an allowed value that is already used by some resource's attribute is deleted, then the association between the resource and the attribute value will also be deleted. * cardinality The cardinality can be updated for user defined attributes only. Cardinality can only be increased during an update. The update_mask should be used to specify the fields being updated.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/attributes/{attributesId}", "httpMethod": "PATCH", "id": "apihub.projects.locations.attributes.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The name of the attribute in the API Hub. Format: `projects/{project}/locations/{location}/attributes/{attribute}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/attributes/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudApihubV1Attribute"}, "response": {"$ref": "GoogleCloudApihubV1Attribute"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "curations": {"methods": {"create": {"description": "Create a curation resource in the API hub. Once a curation resource is created, plugin instances can start using it.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/curations", "httpMethod": "POST", "id": "apihub.projects.locations.curations.create", "parameterOrder": ["parent"], "parameters": {"curationId": {"description": "Optional. The ID to use for the curation resource, which will become the final component of the curations's resource name. This field is optional. * If provided, the same will be used. The service will throw an error if the specified ID is already used by another curation resource in the API hub. * If not provided, a system generated ID will be used. This value should be 4-500 characters, and valid characters are /a-z[0-9]-_/.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource for the curation resource. Format: `projects/{project}/locations/{location}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/curations", "request": {"$ref": "GoogleCloudApihubV1Curation"}, "response": {"$ref": "GoogleCloudApihubV1Curation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a curation resource in the API hub. A curation can only be deleted if it's not being used by any plugin instance.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/curations/{curationsId}", "httpMethod": "DELETE", "id": "apihub.projects.locations.curations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the curation resource to delete. Format: `projects/{project}/locations/{location}/curations/{curation}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/curations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get curation resource details.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/curations/{curationsId}", "httpMethod": "GET", "id": "apihub.projects.locations.curations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the curation resource to retrieve. Format: `projects/{project}/locations/{location}/curations/{curation}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/curations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudApihubV1Curation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List curation resources in the API hub.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/curations", "httpMethod": "GET", "id": "apihub.projects.locations.curations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. An expression that filters the list of curation resources. A filter expression consists of a field name, a comparison operator, and a value for filtering. The value must be a string. The comparison operator must be one of: `<`, `>`, `:` or `=`. Filters are case insensitive. The following fields in the `curation resource` are eligible for filtering: * `create_time` - The time at which the curation was created. The value should be in the (RFC3339)[https://tools.ietf.org/html/rfc3339] format. Allowed comparison operators: `>` and `<`. * `display_name` - The display name of the curation. Allowed comparison operators: `=`. * `state` - The state of the curation. Allowed comparison operators: `=`. Expressions are combined with either `AND` logic operator or `OR` logical operator but not both of them together i.e. only one of the `AND` or `OR` operator can be used throughout the filter string and both the operators cannot be used together. No other logical operators are supported. At most three filter fields are allowed in the filter string and if provided more than that then `INVALID_ARGUMENT` error is returned by the API. Here are a few examples: * `create_time < \\\"2021-08-15T14:50:00Z\\\" AND create_time > \\\"2021-08-10T12:00:00Z\\\"` - The curation resource was created before _2021-08-15 14:50:00 UTC_ and after _2021-08-10 12:00:00 UTC_.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of curation resources to return. The service may return fewer than this value. If unspecified, at most 50 curations will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListCurations` call. Provide this to retrieve the subsequent page. When paginating, all other parameters (except page_size) provided to `ListCurations` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of curation resources. Format: `projects/{project}/locations/{location}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/curations", "response": {"$ref": "GoogleCloudApihubV1ListCurationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a curation resource in the API hub. The following fields in the curation can be updated: * display_name * description The update_mask should be used to specify the fields being updated.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/curations/{curationsId}", "httpMethod": "PATCH", "id": "apihub.projects.locations.curations.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The name of the curation. Format: `projects/{project}/locations/{location}/curations/{curation}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/curations/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. The list of fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudApihubV1Curation"}, "response": {"$ref": "GoogleCloudApihubV1Curation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "dependencies": {"methods": {"create": {"description": "Create a dependency between two entities in the API hub.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dependencies", "httpMethod": "POST", "id": "apihub.projects.locations.dependencies.create", "parameterOrder": ["parent"], "parameters": {"dependencyId": {"description": "Optional. The ID to use for the dependency resource, which will become the final component of the dependency's resource name. This field is optional. * If provided, the same will be used. The service will throw an error if duplicate id is provided by the client. * If not provided, a system generated id will be used. This value should be 4-500 characters, and valid characters are `a-z[0-9]-_`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource for the dependency resource. Format: `projects/{project}/locations/{location}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/dependencies", "request": {"$ref": "GoogleCloudApihubV1Dependency"}, "response": {"$ref": "GoogleCloudApihubV1Dependency"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete the dependency resource.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dependencies/{dependenciesId}", "httpMethod": "DELETE", "id": "apihub.projects.locations.dependencies.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the dependency resource to delete. Format: `projects/{project}/locations/{location}/dependencies/{dependency}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dependencies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get details about a dependency resource in the API hub.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dependencies/{dependenciesId}", "httpMethod": "GET", "id": "apihub.projects.locations.dependencies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the dependency resource to retrieve. Format: `projects/{project}/locations/{location}/dependencies/{dependency}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dependencies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudApihubV1Dependency"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List dependencies based on the provided filter and pagination parameters.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dependencies", "httpMethod": "GET", "id": "apihub.projects.locations.dependencies.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. An expression that filters the list of Dependencies. A filter expression consists of a field name, a comparison operator, and a value for filtering. The value must be a string. Allowed comparison operator is `=`. Filters are not case sensitive. The following fields in the `Dependency` are eligible for filtering: * `consumer.operation_resource_name` - The operation resource name for the consumer entity involved in a dependency. Allowed comparison operators: `=`. * `consumer.external_api_resource_name` - The external api resource name for the consumer entity involved in a dependency. Allowed comparison operators: `=`. * `supplier.operation_resource_name` - The operation resource name for the supplier entity involved in a dependency. Allowed comparison operators: `=`. * `supplier.external_api_resource_name` - The external api resource name for the supplier entity involved in a dependency. Allowed comparison operators: `=`. Expressions are combined with either `AND` logic operator or `OR` logical operator but not both of them together i.e. only one of the `AND` or `OR` operator can be used throughout the filter string and both the operators cannot be used together. No other logical operators are supported. At most three filter fields are allowed in the filter string and if provided more than that then `INVALID_ARGUMENT` error is returned by the API. For example, `consumer.operation_resource_name = \\\"projects/p1/locations/global/apis/a1/versions/v1/operations/o1\\\" OR supplier.operation_resource_name = \\\"projects/p1/locations/global/apis/a1/versions/v1/operations/o1\\\"` - The dependencies with either consumer or supplier operation resource name as _projects/p1/locations/global/apis/a1/versions/v1/operations/o1_.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of dependency resources to return. The service may return fewer than this value. If unspecified, at most 50 dependencies will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListDependencies` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListDependencies` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent which owns this collection of dependency resources. Format: `projects/{project}/locations/{location}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/dependencies", "response": {"$ref": "GoogleCloudApihubV1ListDependenciesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a dependency based on the update_mask provided in the request. The following fields in the dependency can be updated: * description", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dependencies/{dependenciesId}", "httpMethod": "PATCH", "id": "apihub.projects.locations.dependencies.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The name of the dependency in the API Hub. Format: `projects/{project}/locations/{location}/dependencies/{dependency}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dependencies/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudApihubV1Dependency"}, "response": {"$ref": "GoogleCloudApihubV1Dependency"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "deployments": {"methods": {"create": {"description": "Create a deployment resource in the API hub. Once a deployment resource is created, it can be associated with API versions.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments", "httpMethod": "POST", "id": "apihub.projects.locations.deployments.create", "parameterOrder": ["parent"], "parameters": {"deploymentId": {"description": "Optional. The ID to use for the deployment resource, which will become the final component of the deployment's resource name. This field is optional. * If provided, the same will be used. The service will throw an error if the specified id is already used by another deployment resource in the API hub. * If not provided, a system generated id will be used. This value should be 4-500 characters, and valid characters are /a-z[0-9]-_/.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource for the deployment resource. Format: `projects/{project}/locations/{location}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/deployments", "request": {"$ref": "GoogleCloudApihubV1Deployment"}, "response": {"$ref": "GoogleCloudApihubV1Deployment"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a deployment resource in the API hub.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}", "httpMethod": "DELETE", "id": "apihub.projects.locations.deployments.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the deployment resource to delete. Format: `projects/{project}/locations/{location}/deployments/{deployment}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get details about a deployment and the API versions linked to it.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}", "httpMethod": "GET", "id": "apihub.projects.locations.deployments.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the deployment resource to retrieve. Format: `projects/{project}/locations/{location}/deployments/{deployment}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudApihubV1Deployment"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List deployment resources in the API hub.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments", "httpMethod": "GET", "id": "apihub.projects.locations.deployments.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. An expression that filters the list of Deployments. A filter expression consists of a field name, a comparison operator, and a value for filtering. The value must be a string. The comparison operator must be one of: `<`, `>` or `=`. Filters are not case sensitive. The following fields in the `Deployments` are eligible for filtering: * `display_name` - The display name of the Deployment. Allowed comparison operators: `=`. * `create_time` - The time at which the Deployment was created. The value should be in the (RFC3339)[https://tools.ietf.org/html/rfc3339] format. Allowed comparison operators: `>` and `<`. * `resource_uri` - A URI to the deployment resource. Allowed comparison operators: `=`. * `api_versions` - The API versions linked to this deployment. Allowed comparison operators: `:`. * `deployment_type.enum_values.values.id` - The allowed value id of the deployment_type attribute associated with the Deployment. Allowed comparison operators: `:`. * `deployment_type.enum_values.values.display_name` - The allowed value display name of the deployment_type attribute associated with the Deployment. Allowed comparison operators: `:`. * `slo.string_values.values` -The allowed string value of the slo attribute associated with the deployment. Allowed comparison operators: `:`. * `environment.enum_values.values.id` - The allowed value id of the environment attribute associated with the deployment. Allowed comparison operators: `:`. * `environment.enum_values.values.display_name` - The allowed value display name of the environment attribute associated with the deployment. Allowed comparison operators: `:`. * `attributes.projects/test-project-id/locations/test-location-id/ attributes/user-defined-attribute-id.enum_values.values.id` - The allowed value id of the user defined enum attribute associated with the Resource. Allowed comparison operator is `:`. Here user-defined-attribute-enum-id is a placeholder that can be replaced with any user defined enum attribute name. * `attributes.projects/test-project-id/locations/test-location-id/ attributes/user-defined-attribute-id.enum_values.values.display_name` - The allowed value display name of the user defined enum attribute associated with the Resource. Allowed comparison operator is `:`. Here user-defined-attribute-enum-display-name is a placeholder that can be replaced with any user defined enum attribute enum name. * `attributes.projects/test-project-id/locations/test-location-id/ attributes/user-defined-attribute-id.string_values.values` - The allowed value of the user defined string attribute associated with the Resource. Allowed comparison operator is `:`. Here user-defined-attribute-string is a placeholder that can be replaced with any user defined string attribute name. * `attributes.projects/test-project-id/locations/test-location-id/ attributes/user-defined-attribute-id.json_values.values` - The allowed value of the user defined JSON attribute associated with the Resource. Allowed comparison operator is `:`. Here user-defined-attribute-json is a placeholder that can be replaced with any user defined JSON attribute name. A filter function is also supported in the filter string. The filter function is `id(name)`. The `id(name)` function returns the id of the resource name. For example, `id(name) = \\\"deployment-1\\\"` is equivalent to `name = \\\"projects/test-project-id/locations/test-location-id/deployments/deployment-1\\\"` provided the parent is `projects/test-project-id/locations/test-location-id`. Expressions are combined with either `AND` logic operator or `OR` logical operator but not both of them together i.e. only one of the `AND` or `OR` operator can be used throughout the filter string and both the operators cannot be used together. No other logical operators are supported. At most three filter fields are allowed in the filter string and if provided more than that then `INVALID_ARGUMENT` error is returned by the API. Here are a few examples: * `environment.enum_values.values.id: staging-id` - The allowed value id of the environment attribute associated with the Deployment is _staging-id_. * `environment.enum_values.values.display_name: \\\"Staging Deployment\\\"` - The allowed value display name of the environment attribute associated with the Deployment is `Staging Deployment`. * `environment.enum_values.values.id: production-id AND create_time < \\\"2021-08-15T14:50:00Z\\\" AND create_time > \\\"2021-08-10T12:00:00Z\\\"` - The allowed value id of the environment attribute associated with the Deployment is _production-id_ and Deployment was created before _2021-08-15 14:50:00 UTC_ and after _2021-08-10 12:00:00 UTC_. * `environment.enum_values.values.id: production-id OR slo.string_values.values: \\\"99.99%\\\"` - The allowed value id of the environment attribute Deployment is _production-id_ or string value of the slo attribute is _99.99%_. * `environment.enum_values.values.id: staging-id AND attributes.projects/test-project-id/locations/test-location-id/ attributes/17650f90-4a29-4971-b3c0-d5532da3764b.string_values.values: test` - The filter string specifies that the allowed value id of the environment attribute associated with the Deployment is _staging-id_ and the value of the user defined attribute of type string is _test_.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of deployment resources to return. The service may return fewer than this value. If unspecified, at most 50 deployments will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListDeployments` call. Provide this to retrieve the subsequent page. When paginating, all other parameters (except page_size) provided to `ListDeployments` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of deployment resources. Format: `projects/{project}/locations/{location}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/deployments", "response": {"$ref": "GoogleCloudApihubV1ListDeploymentsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a deployment resource in the API hub. The following fields in the deployment resource can be updated: * display_name * description * documentation * deployment_type * resource_uri * endpoints * slo * environment * attributes * source_project * source_environment * management_url * source_uri The update_mask should be used to specify the fields being updated.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}", "httpMethod": "PATCH", "id": "apihub.projects.locations.deployments.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The name of the deployment. Format: `projects/{project}/locations/{location}/deployments/{deployment}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployments/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudApihubV1Deployment"}, "response": {"$ref": "GoogleCloudApihubV1Deployment"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "externalApis": {"methods": {"create": {"description": "Create an External API resource in the API hub.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/externalApis", "httpMethod": "POST", "id": "apihub.projects.locations.externalApis.create", "parameterOrder": ["parent"], "parameters": {"externalApiId": {"description": "Optional. The ID to use for the External API resource, which will become the final component of the External API's resource name. This field is optional. * If provided, the same will be used. The service will throw an error if the specified id is already used by another External API resource in the API hub. * If not provided, a system generated id will be used. This value should be 4-500 characters, and valid characters are /a-z[0-9]-_/.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource for the External API resource. Format: `projects/{project}/locations/{location}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/externalApis", "request": {"$ref": "GoogleCloudApihubV1ExternalApi"}, "response": {"$ref": "GoogleCloudApihubV1ExternalApi"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete an External API resource in the API hub.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/externalApis/{externalApisId}", "httpMethod": "DELETE", "id": "apihub.projects.locations.externalApis.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the External API resource to delete. Format: `projects/{project}/locations/{location}/externalApis/{externalApi}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/externalApis/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get details about an External API resource in the API hub.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/externalApis/{externalApisId}", "httpMethod": "GET", "id": "apihub.projects.locations.externalApis.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the External API resource to retrieve. Format: `projects/{project}/locations/{location}/externalApis/{externalApi}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/externalApis/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudApihubV1ExternalApi"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List External API resources in the API hub.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/externalApis", "httpMethod": "GET", "id": "apihub.projects.locations.externalApis.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of External API resources to return. The service may return fewer than this value. If unspecified, at most 50 ExternalApis will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListExternalApis` call. Provide this to retrieve the subsequent page. When paginating, all other parameters (except page_size) provided to `ListExternalApis` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of External API resources. Format: `projects/{project}/locations/{location}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/externalApis", "response": {"$ref": "GoogleCloudApihubV1ListExternalApisResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update an External API resource in the API hub. The following fields can be updated: * display_name * description * documentation * endpoints * paths The update_mask should be used to specify the fields being updated.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/externalApis/{externalApisId}", "httpMethod": "PATCH", "id": "apihub.projects.locations.externalApis.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Format: `projects/{project}/locations/{location}/externalApi/{externalApi}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/externalApis/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudApihubV1ExternalApi"}, "response": {"$ref": "GoogleCloudApihubV1ExternalApi"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "hostProjectRegistrations": {"methods": {"create": {"description": "Create a host project registration. A Google cloud project can be registered as a host project if it is not attached as a runtime project to another host project. A project can be registered as a host project only once. Subsequent register calls for the same project will fail.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/hostProjectRegistrations", "httpMethod": "POST", "id": "apihub.projects.locations.hostProjectRegistrations.create", "parameterOrder": ["parent"], "parameters": {"hostProjectRegistrationId": {"description": "Required. The ID to use for the Host Project Registration, which will become the final component of the host project registration's resource name. The ID must be the same as the Google cloud project specified in the host_project_registration.gcp_project field.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource for the host project. Format: `projects/{project}/locations/{location}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/hostProjectRegistrations", "request": {"$ref": "GoogleCloudApihubV1HostProjectRegistration"}, "response": {"$ref": "GoogleCloudApihubV1HostProjectRegistration"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get a host project registration.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/hostProjectRegistrations/{hostProjectRegistrationsId}", "httpMethod": "GET", "id": "apihub.projects.locations.hostProjectRegistrations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Host project registration resource name. projects/{project}/locations/{location}/hostProjectRegistrations/{host_project_registration_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/hostProjectRegistrations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudApihubV1HostProjectRegistration"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists host project registrations.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/hostProjectRegistrations", "httpMethod": "GET", "id": "apihub.projects.locations.hostProjectRegistrations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. An expression that filters the list of HostProjectRegistrations. A filter expression consists of a field name, a comparison operator, and a value for filtering. The value must be a string. All standard operators as documented at https://google.aip.dev/160 are supported. The following fields in the `HostProjectRegistration` are eligible for filtering: * `name` - The name of the HostProjectRegistration. * `create_time` - The time at which the HostProjectRegistration was created. The value should be in the (RFC3339)[https://tools.ietf.org/html/rfc3339] format. * `gcp_project` - The Google cloud project associated with the HostProjectRegistration.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of host project registrations to return. The service may return fewer than this value. If unspecified, at most 50 host project registrations will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListHostProjectRegistrations` call. Provide this to retrieve the subsequent page. When paginating, all other parameters (except page_size) provided to `ListHostProjectRegistrations` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of host projects. Format: `projects/*/locations/*`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/hostProjectRegistrations", "response": {"$ref": "GoogleCloudApihubV1ListHostProjectRegistrationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "apihub.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "GoogleLongrunningCancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "apihub.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "apihub.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "apihub.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "plugins": {"methods": {"create": {"description": "Create an API Hub plugin resource in the API hub. Once a plugin is created, it can be used to create plugin instances.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/plugins", "httpMethod": "POST", "id": "apihub.projects.locations.plugins.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource where this plugin will be created. Format: `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pluginId": {"description": "Optional. The ID to use for the Plugin resource, which will become the final component of the Plugin's resource name. This field is optional. * If provided, the same will be used. The service will throw an error if the specified id is already used by another Plugin resource in the API hub instance. * If not provided, a system generated id will be used. This value should be 4-63 characters, overall resource name which will be of format `projects/{project}/locations/{location}/plugins/{plugin}`, its length is limited to 1000 characters and valid characters are /a-z[0-9]-_/.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/plugins", "request": {"$ref": "GoogleCloudApihubV1Plugin"}, "response": {"$ref": "GoogleCloudApihubV1Plugin"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a Plugin in API hub. Note, only user owned plugins can be deleted via this method.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}", "httpMethod": "DELETE", "id": "apihub.projects.locations.plugins.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the Plugin resource to delete. Format: `projects/{project}/locations/{location}/plugins/{plugin}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/plugins/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "disable": {"description": "Disables a plugin. The `state` of the plugin after disabling is `DISABLED`", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}:disable", "httpMethod": "POST", "id": "apihub.projects.locations.plugins.disable", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the plugin to disable. Format: `projects/{project}/locations/{location}/plugins/{plugin}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/plugins/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:disable", "request": {"$ref": "GoogleCloudApihubV1DisablePluginRequest"}, "response": {"$ref": "GoogleCloudApihubV1Plugin"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "enable": {"description": "Enables a plugin. The `state` of the plugin after enabling is `ENABLED`", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}:enable", "httpMethod": "POST", "id": "apihub.projects.locations.plugins.enable", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the plugin to enable. Format: `projects/{project}/locations/{location}/plugins/{plugin}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/plugins/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:enable", "request": {"$ref": "GoogleCloudApihubV1EnablePluginRequest"}, "response": {"$ref": "GoogleCloudApihubV1Plugin"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get an API Hub plugin.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}", "httpMethod": "GET", "id": "apihub.projects.locations.plugins.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the plugin to retrieve. Format: `projects/{project}/locations/{location}/plugins/{plugin}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/plugins/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudApihubV1Plugin"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getStyleGuide": {"description": "Get the style guide being used for linting.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}/styleGuide", "httpMethod": "GET", "id": "apihub.projects.locations.plugins.getStyleGuide", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the spec to retrieve. Format: `projects/{project}/locations/{location}/plugins/{plugin}/styleGuide`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/plugins/[^/]+/styleGuide$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudApihubV1StyleGuide"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List all the plugins in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/plugins", "httpMethod": "GET", "id": "apihub.projects.locations.plugins.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. An expression that filters the list of plugins. A filter expression consists of a field name, a comparison operator, and a value for filtering. The value must be a string. The comparison operator must be one of: `<`, `>` or `=`. Filters are not case sensitive. The following fields in the `Plugins` are eligible for filtering: * `plugin_category` - The category of the Plugin. Allowed comparison operators: `=`. Expressions are combined with either `AND` logic operator or `OR` logical operator but not both of them together i.e. only one of the `AND` or `OR` operator can be used throughout the filter string and both the operators cannot be used together. No other logical operators are supported. At most three filter fields are allowed in the filter string and if provided more than that then `INVALID_ARGUMENT` error is returned by the API. Here are a few examples: * `plugin_category = ON_RAMP` - The plugin is of category on ramp.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of hub plugins to return. The service may return fewer than this value. If unspecified, at most 50 hub plugins will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListPlugins` call. Provide this to retrieve the subsequent page. When paginating, all other parameters (except page_size) provided to `ListPlugins` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource where this plugin will be created. Format: `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/plugins", "response": {"$ref": "GoogleCloudApihubV1ListPluginsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateStyleGuide": {"description": "Update the styleGuide to be used for liniting in by API hub.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}/styleGuide", "httpMethod": "PATCH", "id": "apihub.projects.locations.plugins.updateStyleGuide", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The name of the style guide. Format: `projects/{project}/locations/{location}/plugins/{plugin}/styleGuide`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/plugins/[^/]+/styleGuide$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. The list of fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudApihubV1StyleGuide"}, "response": {"$ref": "GoogleCloudApihubV1StyleGuide"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"instances": {"methods": {"create": {"description": "Creates a Plugin instance in the API hub.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}/instances", "httpMethod": "POST", "id": "apihub.projects.locations.plugins.instances.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent of the plugin instance resource. Format: `projects/{project}/locations/{location}/plugins/{plugin}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/plugins/[^/]+$", "required": true, "type": "string"}, "pluginInstanceId": {"description": "Optional. The ID to use for the plugin instance, which will become the final component of the plugin instance's resource name. This field is optional. * If provided, the same will be used. The service will throw an error if the specified id is already used by another plugin instance in the plugin resource. * If not provided, a system generated id will be used. This value should be 4-63 characters, and valid characters are /a-z[0-9]-_/.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/instances", "request": {"$ref": "GoogleCloudApihubV1PluginInstance"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a plugin instance in the API hub.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}/instances/{instancesId}", "httpMethod": "DELETE", "id": "apihub.projects.locations.plugins.instances.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the plugin instance to delete. Format: `projects/{project}/locations/{location}/plugins/{plugin}/instances/{instance}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/plugins/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "disableAction": {"description": "Disables a plugin instance in the API hub.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}/instances/{instancesId}:disableAction", "httpMethod": "POST", "id": "apihub.projects.locations.plugins.instances.disableAction", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the plugin instance to disable. Format: `projects/{project}/locations/{location}/plugins/{plugin}/instances/{instance}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/plugins/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:disableAction", "request": {"$ref": "GoogleCloudApihubV1DisablePluginInstanceActionRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "enableAction": {"description": "Enables a plugin instance in the API hub.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}/instances/{instancesId}:enableAction", "httpMethod": "POST", "id": "apihub.projects.locations.plugins.instances.enableAction", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the plugin instance to enable. Format: `projects/{project}/locations/{location}/plugins/{plugin}/instances/{instance}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/plugins/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:enableAction", "request": {"$ref": "GoogleCloudApihubV1EnablePluginInstanceActionRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "executeAction": {"description": "Executes a plugin instance in the API hub.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}/instances/{instancesId}:executeAction", "httpMethod": "POST", "id": "apihub.projects.locations.plugins.instances.executeAction", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the plugin instance to execute. Format: `projects/{project}/locations/{location}/plugins/{plugin}/instances/{instance}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/plugins/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:executeAction", "request": {"$ref": "GoogleCloudApihubV1ExecutePluginInstanceActionRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get an API Hub plugin instance.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}/instances/{instancesId}", "httpMethod": "GET", "id": "apihub.projects.locations.plugins.instances.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the plugin instance to retrieve. Format: `projects/{project}/locations/{location}/plugins/{plugin}/instances/{instance}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/plugins/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudApihubV1PluginInstance"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List all the plugins in a given project and location. `-` can be used as wildcard value for {plugin_id}", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}/instances", "httpMethod": "GET", "id": "apihub.projects.locations.plugins.instances.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. An expression that filters the list of plugin instances. A filter expression consists of a field name, a comparison operator, and a value for filtering. The value must be a string. The comparison operator must be one of: `<`, `>` or `=`. Filters are not case sensitive. The following fields in the `PluginInstances` are eligible for filtering: * `state` - The state of the Plugin Instance. Allowed comparison operators: `=`. A filter function is also supported in the filter string. The filter function is `id(name)`. The `id(name)` function returns the id of the resource name. For example, `id(name) = \\\"plugin-instance-1\\\"` is equivalent to `name = \\\"projects/test-project-id/locations/test-location-id/plugins/plugin-1/instances/plugin-instance-1\\\"` provided the parent is `projects/test-project-id/locations/test-location-id/plugins/plugin-1`. Expressions are combined with either `AND` logic operator or `OR` logical operator but not both of them together i.e. only one of the `AND` or `OR` operator can be used throughout the filter string and both the operators cannot be used together. No other logical operators are supported. At most three filter fields are allowed in the filter string and if provided more than that then `INVALID_ARGUMENT` error is returned by the API. Here are a few examples: * `state = ENABLED` - The plugin instance is in enabled state.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of hub plugins to return. The service may return fewer than this value. If unspecified, at most 50 hub plugins will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListPluginInstances` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListPluginInstances` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource where this plugin will be created. Format: `projects/{project}/locations/{location}/plugins/{plugin}`. To list plugin instances for multiple plugins, use the - character instead of the plugin ID.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/plugins/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/instances", "response": {"$ref": "GoogleCloudApihubV1ListPluginInstancesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "styleGuide": {"methods": {"getContents": {"description": "Get the contents of the style guide.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/plugins/{pluginsId}/styleGuide:contents", "httpMethod": "GET", "id": "apihub.projects.locations.plugins.styleGuide.getContents", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the StyleGuide whose contents need to be retrieved. There is exactly one style guide resource per project per location. The expected format is `projects/{project}/locations/{location}/plugins/{plugin}/styleGuide`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/plugins/[^/]+/styleGuide$", "required": true, "type": "string"}}, "path": "v1/{+name}:contents", "response": {"$ref": "GoogleCloudApihubV1StyleGuideContents"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "runtimeProjectAttachments": {"methods": {"create": {"description": "Attaches a runtime project to the host project.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/runtimeProjectAttachments", "httpMethod": "POST", "id": "apihub.projects.locations.runtimeProjectAttachments.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource for the Runtime Project Attachment. Format: `projects/{project}/locations/{location}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "runtimeProjectAttachmentId": {"description": "Required. The ID to use for the Runtime Project Attachment, which will become the final component of the Runtime Project Attachment's name. The ID must be the same as the project ID of the Google cloud project specified in the runtime_project_attachment.runtime_project field.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/runtimeProjectAttachments", "request": {"$ref": "GoogleCloudApihubV1RuntimeProjectAttachment"}, "response": {"$ref": "GoogleCloudApihubV1RuntimeProjectAttachment"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a runtime project attachment in the API Hub. This call will detach the runtime project from the host project.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/runtimeProjectAttachments/{runtimeProjectAttachmentsId}", "httpMethod": "DELETE", "id": "apihub.projects.locations.runtimeProjectAttachments.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the Runtime Project Attachment to delete. Format: `projects/{project}/locations/{location}/runtimeProjectAttachments/{runtime_project_attachment}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/runtimeProjectAttachments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a runtime project attachment.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/runtimeProjectAttachments/{runtimeProjectAttachmentsId}", "httpMethod": "GET", "id": "apihub.projects.locations.runtimeProjectAttachments.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the API resource to retrieve. Format: `projects/{project}/locations/{location}/runtimeProjectAttachments/{runtime_project_attachment}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/runtimeProjectAttachments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudApihubV1RuntimeProjectAttachment"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List runtime projects attached to the host project.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/runtimeProjectAttachments", "httpMethod": "GET", "id": "apihub.projects.locations.runtimeProjectAttachments.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. An expression that filters the list of RuntimeProjectAttachments. A filter expression consists of a field name, a comparison operator, and a value for filtering. The value must be a string. All standard operators as documented at https://google.aip.dev/160 are supported. The following fields in the `RuntimeProjectAttachment` are eligible for filtering: * `name` - The name of the RuntimeProjectAttachment. * `create_time` - The time at which the RuntimeProjectAttachment was created. The value should be in the (RFC3339)[https://tools.ietf.org/html/rfc3339] format. * `runtime_project` - The Google cloud project associated with the RuntimeProjectAttachment.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of runtime project attachments to return. The service may return fewer than this value. If unspecified, at most 50 runtime project attachments will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListRuntimeProjectAttachments` call. Provide this to retrieve the subsequent page. When paginating, all other parameters (except page_size) provided to `ListRuntimeProjectAttachments` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of runtime project attachments. Format: `projects/{project}/locations/{location}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/runtimeProjectAttachments", "response": {"$ref": "GoogleCloudApihubV1ListRuntimeProjectAttachmentsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250611", "rootUrl": "https://apihub.googleapis.com/", "schemas": {"Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "GoogleCloudApihubV1APIMetadata": {"description": "The API metadata.", "id": "GoogleCloudApihubV1APIMetadata", "properties": {"api": {"$ref": "GoogleCloudApihubV1Api", "description": "Required. The API resource to be pushed to Hub's collect layer. The ID of the API resource will be generated by Hub to ensure uniqueness across all APIs across systems."}, "originalCreateTime": {"description": "Optional. Timestamp indicating when the API was created at the source.", "format": "google-datetime", "type": "string"}, "originalId": {"description": "Optional. The unique identifier of the API in the system where it was originally created.", "type": "string"}, "originalUpdateTime": {"description": "Required. Timestamp indicating when the API was last updated at the source.", "format": "google-datetime", "type": "string"}, "versions": {"description": "Optional. The list of versions present in an API resource.", "items": {"$ref": "GoogleCloudApihubV1VersionMetadata"}, "type": "array"}}, "type": "object"}, "GoogleCloudApihubV1ActionExecutionDetail": {"description": "The details for the action to execute.", "id": "GoogleCloudApihubV1ActionExecutionDetail", "properties": {"actionId": {"description": "Required. The action id of the plugin to execute.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1AllowedValue": {"description": "The value that can be assigned to the attribute when the data type is enum.", "id": "GoogleCloudApihubV1AllowedValue", "properties": {"description": {"description": "Optional. The detailed description of the allowed value.", "type": "string"}, "displayName": {"description": "Required. The display name of the allowed value.", "type": "string"}, "id": {"description": "Required. The ID of the allowed value. * If provided, the same will be used. The service will throw an error if the specified id is already used by another allowed value in the same attribute resource. * If not provided, a system generated id derived from the display name will be used. In this case, the service will handle conflict resolution by adding a system generated suffix in case of duplicates. This value should be 4-63 characters, and valid characters are /a-z-/.", "type": "string"}, "immutable": {"description": "Optional. When set to true, the allowed value cannot be updated or deleted by the user. It can only be true for System defined attributes.", "type": "boolean"}}, "type": "object"}, "GoogleCloudApihubV1Api": {"description": "An API resource in the API Hub.", "id": "GoogleCloudApihubV1Api", "properties": {"apiFunctionalRequirements": {"$ref": "GoogleCloudApihubV1AttributeValues", "description": "Optional. The api functional requirements associated with the API resource. Carinality is 1 for this attribute. This maps to the following system defined attribute: `projects/{project}/locations/{location}/attributes/system-api-functional-requirements` attribute. The value of the attribute should be a proper URI, and in case of Cloud Storage URI, it should point to a Cloud Storage object, not a directory."}, "apiRequirements": {"$ref": "GoogleCloudApihubV1AttributeValues", "description": "Optional. The api requirement doc associated with the API resource. Carinality is 1 for this attribute. This maps to the following system defined attribute: `projects/{project}/locations/{location}/attributes/system-api-requirements` attribute. The value of the attribute should be a proper URI, and in case of Cloud Storage URI, it should point to a Cloud Storage object, not a directory."}, "apiStyle": {"$ref": "GoogleCloudApihubV1AttributeValues", "description": "Optional. The style of the API. This maps to the following system defined attribute: `projects/{project}/locations/{location}/attributes/system-api-style` attribute. The number of values for this attribute will be based on the cardinality of the attribute. The same can be retrieved via GetAttribute API. All values should be from the list of allowed values defined for the attribute."}, "apiTechnicalRequirements": {"$ref": "GoogleCloudApihubV1AttributeValues", "description": "Optional. The api technical requirements associated with the API resource. Carinality is 1 for this attribute. This maps to the following system defined attribute: `projects/{project}/locations/{location}/attributes/system-api-technical-requirements` attribute. The value of the attribute should be a proper URI, and in case of Cloud Storage URI, it should point to a Cloud Storage object, not a directory."}, "attributes": {"additionalProperties": {"$ref": "GoogleCloudApihubV1AttributeValues"}, "description": "Optional. The list of user defined attributes associated with the API resource. The key is the attribute name. It will be of the format: `projects/{project}/locations/{location}/attributes/{attribute}`. The value is the attribute values associated with the resource.", "type": "object"}, "businessUnit": {"$ref": "GoogleCloudApihubV1AttributeValues", "description": "Optional. The business unit owning the API. This maps to the following system defined attribute: `projects/{project}/locations/{location}/attributes/system-business-unit` attribute. The number of values for this attribute will be based on the cardinality of the attribute. The same can be retrieved via GetAttribute API. All values should be from the list of allowed values defined for the attribute."}, "createTime": {"description": "Output only. The time at which the API resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. The description of the API resource.", "type": "string"}, "displayName": {"description": "Required. The display name of the API resource.", "type": "string"}, "documentation": {"$ref": "GoogleCloudApihubV1Documentation", "description": "Optional. The documentation for the API resource."}, "fingerprint": {"description": "Optional. Fingerprint of the API resource.", "type": "string"}, "maturityLevel": {"$ref": "GoogleCloudApihubV1AttributeValues", "description": "Optional. The maturity level of the API. This maps to the following system defined attribute: `projects/{project}/locations/{location}/attributes/system-maturity-level` attribute. The number of values for this attribute will be based on the cardinality of the attribute. The same can be retrieved via GetAttribute API. All values should be from the list of allowed values defined for the attribute."}, "name": {"description": "Identifier. The name of the API resource in the API Hub. Format: `projects/{project}/locations/{location}/apis/{api}`", "type": "string"}, "owner": {"$ref": "GoogleCloudApihubV1Owner", "description": "Optional. Owner details for the API resource."}, "selectedVersion": {"description": "Optional. The selected version for an API resource. This can be used when special handling is needed on client side for particular version of the API. Format is `projects/{project}/locations/{location}/apis/{api}/versions/{version}`", "type": "string"}, "sourceMetadata": {"description": "Output only. The list of sources and metadata from the sources of the API resource.", "items": {"$ref": "GoogleCloudApihubV1SourceMetadata"}, "readOnly": true, "type": "array"}, "targetUser": {"$ref": "GoogleCloudApihubV1AttributeValues", "description": "Optional. The target users for the API. This maps to the following system defined attribute: `projects/{project}/locations/{location}/attributes/system-target-user` attribute. The number of values for this attribute will be based on the cardinality of the attribute. The same can be retrieved via GetAttribute API. All values should be from the list of allowed values defined for the attribute."}, "team": {"$ref": "GoogleCloudApihubV1AttributeValues", "description": "Optional. The team owning the API. This maps to the following system defined attribute: `projects/{project}/locations/{location}/attributes/system-team` attribute. The number of values for this attribute will be based on the cardinality of the attribute. The same can be retrieved via GetAttribute API. All values should be from the list of allowed values defined for the attribute."}, "updateTime": {"description": "Output only. The time at which the API resource was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "versions": {"description": "Output only. The list of versions present in an API resource. Note: An API resource can be associated with more than 1 version. Format is `projects/{project}/locations/{location}/apis/{api}/versions/{version}`", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleCloudApihubV1ApiData": {"description": "The API data to be collected.", "id": "GoogleCloudApihubV1ApiData", "properties": {"apiMetadataList": {"$ref": "GoogleCloudApihubV1ApiMetadataList", "description": "Optional. The list of API metadata."}}, "type": "object"}, "GoogleCloudApihubV1ApiHubInstance": {"description": "An ApiHubInstance represents the instance resources of the API Hub. Currently, only one ApiHub instance is allowed for each project.", "id": "GoogleCloudApihubV1ApiHubInstance", "properties": {"config": {"$ref": "GoogleCloudApihubV1Config", "description": "Required. Config of the ApiHub instance."}, "createTime": {"description": "Output only. Creation timestamp.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. Description of the ApiHub instance.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Instance labels to represent user-provided metadata. Refer to cloud documentation on labels for more details. https://cloud.google.com/compute/docs/labeling-resources", "type": "object"}, "name": {"description": "Identifier. Format: `projects/{project}/locations/{location}/apiHubInstances/{apiHubInstance}`.", "type": "string"}, "state": {"description": "Output only. The current state of the ApiHub instance.", "enum": ["STATE_UNSPECIFIED", "INACTIVE", "CREATING", "ACTIVE", "UPDATING", "DELETING", "FAILED"], "enumDescriptions": ["The default value. This value is used if the state is omitted.", "The ApiHub instance has not been initialized or has been deleted.", "The ApiHub instance is being created.", "The ApiHub instance has been created and is ready for use.", "The ApiHub instance is being updated.", "The ApiHub instance is being deleted.", "The ApiHub instance encountered an error during a state change."], "readOnly": true, "type": "string"}, "stateMessage": {"description": "Output only. Extra information about ApiHub instance state. Currently the message would be populated when state is `FAILED`.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Last update timestamp.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1ApiHubResource": {"description": "ApiHubResource is one of the resources such as Api, Operation, Deployment, Definition, Spec and Version resources stored in API-Hub.", "id": "GoogleCloudApihubV1ApiHubResource", "properties": {"api": {"$ref": "GoogleCloudApihubV1Api", "description": "This represents Api resource in search results. Only name, display_name, description and owner fields are populated in search results."}, "definition": {"$ref": "GoogleCloudApihubV1Definition", "description": "This represents Definition resource in search results. Only name field is populated in search results."}, "deployment": {"$ref": "GoogleCloudApihubV1Deployment", "description": "This represents Deployment resource in search results. Only name, display_name, description, deployment_type and api_versions fields are populated in search results."}, "operation": {"$ref": "GoogleCloudApihubV1ApiOperation", "description": "This represents ApiOperation resource in search results. Only name, description, spec and details fields are populated in search results."}, "spec": {"$ref": "GoogleCloudApihubV1Spec", "description": "This represents Spec resource in search results. Only name, display_name, description, spec_type and documentation fields are populated in search results."}, "version": {"$ref": "GoogleCloudApihubV1Version", "description": "This represents Version resource in search results. Only name, display_name, description, lifecycle, compliance and accreditation fields are populated in search results."}}, "type": "object"}, "GoogleCloudApihubV1ApiKeyConfig": {"description": "Config for authentication with API key.", "id": "GoogleCloudApihubV1ApiKeyConfig", "properties": {"apiKey": {"$ref": "GoogleCloudApihubV1Secret", "description": "Required. The name of the SecretManager secret version resource storing the API key. Format: `projects/{project}/secrets/{secrete}/versions/{version}`. The `secretmanager.versions.access` permission should be granted to the service account accessing the secret."}, "httpElementLocation": {"description": "Required. The location of the API key. The default value is QUERY.", "enum": ["HTTP_ELEMENT_LOCATION_UNSPECIFIED", "QUERY", "HEADER", "PATH", "BODY", "COOKIE"], "enumDescriptions": ["HTTP element location not specified.", "Element is in the HTTP request query.", "Element is in the HTTP request header.", "Element is in the HTTP request path.", "Element is in the HTTP request body.", "Element is in the HTTP request cookie."], "type": "string"}, "name": {"description": "Required. The parameter name of the API key. E.g. If the API request is \"https://example.com/act?api_key=\", \"api_key\" would be the parameter name.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1ApiMetadataList": {"description": "The message to hold repeated API metadata.", "id": "GoogleCloudApihubV1ApiMetadataList", "properties": {"apiMetadata": {"description": "Required. The list of API metadata.", "items": {"$ref": "GoogleCloudApihubV1APIMetadata"}, "type": "array"}}, "type": "object"}, "GoogleCloudApihubV1ApiOperation": {"description": "Represents an operation contained in an API version in the API Hub. An operation is added/updated/deleted in an API version when a new spec is added or an existing spec is updated/deleted in a version. Currently, an operation will be created only corresponding to OpenAPI spec as parsing is supported for OpenAPI spec. Alternatively operations can be managed via create,update and delete APIs, creation of apiOperation can be possible only for version with no parsed operations and update/delete can be possible only for operations created via create API.", "id": "GoogleCloudApihubV1ApiOperation", "properties": {"attributes": {"additionalProperties": {"$ref": "GoogleCloudApihubV1AttributeValues"}, "description": "Optional. The list of user defined attributes associated with the API operation resource. The key is the attribute name. It will be of the format: `projects/{project}/locations/{location}/attributes/{attribute}`. The value is the attribute values associated with the resource.", "type": "object"}, "createTime": {"description": "Output only. The time at which the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "details": {"$ref": "GoogleCloudApihubV1OperationDetails", "description": "Optional. Operation details. Note: Even though this field is optional, it is required for CreateApiOperation API and we will fail the request if not provided."}, "name": {"description": "Identifier. The name of the operation. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}/operations/{operation}`", "type": "string"}, "sourceMetadata": {"description": "Output only. The list of sources and metadata from the sources of the API operation.", "items": {"$ref": "GoogleCloudApihubV1SourceMetadata"}, "readOnly": true, "type": "array"}, "spec": {"description": "Output only. The name of the spec will be of the format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}` Note:The name of the spec will be empty if the operation is created via CreateApiOperation API.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time at which the operation was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1ApplicationIntegrationEndpointDetails": {"description": "The details of the Application Integration endpoint to be triggered for curation.", "id": "GoogleCloudApihubV1ApplicationIntegrationEndpointDetails", "properties": {"triggerId": {"description": "Required. The API trigger ID of the Application Integration workflow.", "type": "string"}, "uri": {"description": "Required. The endpoint URI should be a valid REST URI for triggering an Application Integration. Format: `https://integrations.googleapis.com/v1/{name=projects/*/locations/*/integrations/*}:execute` or `https://{location}-integrations.googleapis.com/v1/{name=projects/*/locations/*/integrations/*}:execute`", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1Attribute": {"description": "An attribute in the API Hub. An attribute is a name value pair which can be attached to different resources in the API hub based on the scope of the attribute. Attributes can either be pre-defined by the API Hub or created by users.", "id": "GoogleCloudApihubV1Attribute", "properties": {"allowedValues": {"description": "Optional. The list of allowed values when the attribute value is of type enum. This is required when the data_type of the attribute is ENUM. The maximum number of allowed values of an attribute will be 1000.", "items": {"$ref": "GoogleCloudApihubV1AllowedValue"}, "type": "array"}, "cardinality": {"description": "Optional. The maximum number of values that the attribute can have when associated with an API Hub resource. Cardinality 1 would represent a single-valued attribute. It must not be less than 1 or greater than 20. If not specified, the cardinality would be set to 1 by default and represent a single-valued attribute.", "format": "int32", "type": "integer"}, "createTime": {"description": "Output only. The time at which the attribute was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dataType": {"description": "Required. The type of the data of the attribute.", "enum": ["DATA_TYPE_UNSPECIFIED", "ENUM", "JSON", "STRING", "URI"], "enumDescriptions": ["Attribute data type unspecified.", "Attribute's value is of type enum.", "Attribute's value is of type json.", "Attribute's value is of type string.", "Attribute's value is of type uri."], "type": "string"}, "definitionType": {"description": "Output only. The definition type of the attribute.", "enum": ["DEFINITION_TYPE_UNSPECIFIED", "SYSTEM_DEFINED", "USER_DEFINED"], "enumDescriptions": ["Attribute definition type unspecified.", "The attribute is predefined by the API Hub. Note that only the list of allowed values can be updated in this case via UpdateAttribute method.", "The attribute is defined by the user."], "readOnly": true, "type": "string"}, "description": {"description": "Optional. The description of the attribute.", "type": "string"}, "displayName": {"description": "Required. The display name of the attribute.", "type": "string"}, "mandatory": {"description": "Output only. When mandatory is true, the attribute is mandatory for the resource specified in the scope. Only System defined attributes can be mandatory.", "readOnly": true, "type": "boolean"}, "name": {"description": "Identifier. The name of the attribute in the API Hub. Format: `projects/{project}/locations/{location}/attributes/{attribute}`", "type": "string"}, "scope": {"description": "Required. The scope of the attribute. It represents the resource in the API Hub to which the attribute can be linked.", "enum": ["SCOPE_UNSPECIFIED", "API", "VERSION", "SPEC", "API_OPERATION", "DEPLOYMENT", "DEPENDENCY", "DEFINITION", "EXTERNAL_API", "PLUGIN"], "enumDescriptions": ["<PERSON><PERSON> Unspecified.", "Attribute can be linked to an API.", "Attribute can be linked to an API version.", "Attribute can be linked to a Spec.", "Attribute can be linked to an API Operation.", "Attribute can be linked to a Deployment.", "Attribute can be linked to a Dependency.", "Attribute can be linked to a definition.", "Attribute can be linked to a ExternalAPI.", "Attribute can be linked to a Plugin."], "type": "string"}, "updateTime": {"description": "Output only. The time at which the attribute was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1AttributeValues": {"description": "The attribute values associated with resource.", "id": "GoogleCloudApihubV1AttributeValues", "properties": {"attribute": {"description": "Output only. The name of the attribute. Format: projects/{project}/locations/{location}/attributes/{attribute}", "readOnly": true, "type": "string"}, "enumValues": {"$ref": "GoogleCloudApihubV1EnumAttributeValues", "description": "The attribute values associated with a resource in case attribute data type is enum."}, "jsonValues": {"$ref": "GoogleCloudApihubV1StringAttributeValues", "description": "The attribute values associated with a resource in case attribute data type is JSON."}, "stringValues": {"$ref": "GoogleCloudApihubV1StringAttributeValues", "description": "The attribute values associated with a resource in case attribute data type is string."}, "uriValues": {"$ref": "GoogleCloudApihubV1StringAttributeValues", "description": "The attribute values associated with a resource in case attribute data type is URL, URI or IP, like gs://bucket-name/object-name."}}, "type": "object"}, "GoogleCloudApihubV1AuthConfig": {"description": "AuthConfig represents the authentication information.", "id": "GoogleCloudApihubV1AuthConfig", "properties": {"apiKeyConfig": {"$ref": "GoogleCloudApihubV1ApiKeyConfig", "description": "Api Key Config."}, "authType": {"description": "Required. The authentication type.", "enum": ["AUTH_TYPE_UNSPECIFIED", "NO_AUTH", "GOOGLE_SERVICE_ACCOUNT", "USER_PASSWORD", "API_KEY", "OAUTH2_CLIENT_CREDENTIALS"], "enumDescriptions": ["Authentication type not specified.", "No authentication.", "Google service account authentication.", "Username and password authentication.", "API Key authentication.", "Oauth 2.0 client credentials grant authentication."], "type": "string"}, "googleServiceAccountConfig": {"$ref": "GoogleCloudApihubV1GoogleServiceAccountConfig", "description": "Google Service Account."}, "oauth2ClientCredentialsConfig": {"$ref": "GoogleCloudApihubV1Oauth2ClientCredentialsConfig", "description": "Oauth2.0 Client Credentials."}, "userPasswordConfig": {"$ref": "GoogleCloudApihubV1UserPasswordConfig", "description": "User Password."}}, "type": "object"}, "GoogleCloudApihubV1AuthConfigTemplate": {"description": "AuthConfigTemplate represents the authentication template for a plugin.", "id": "GoogleCloudApihubV1AuthConfigTemplate", "properties": {"serviceAccount": {"$ref": "GoogleCloudApihubV1GoogleServiceAccountConfig", "description": "Optional. The service account of the plugin hosting service. This service account should be granted the required permissions on the Auth Config parameters provided while creating the plugin instances corresponding to this plugin. For example, if the plugin instance auth config requires a secret manager secret, the service account should be granted the secretmanager.versions.access permission on the corresponding secret, if the plugin instance auth config contains a service account, the service account should be granted the iam.serviceAccounts.getAccessToken permission on the corresponding service account."}, "supportedAuthTypes": {"description": "Required. The list of authentication types supported by the plugin.", "items": {"enum": ["AUTH_TYPE_UNSPECIFIED", "NO_AUTH", "GOOGLE_SERVICE_ACCOUNT", "USER_PASSWORD", "API_KEY", "OAUTH2_CLIENT_CREDENTIALS"], "enumDescriptions": ["Authentication type not specified.", "No authentication.", "Google service account authentication.", "Username and password authentication.", "API Key authentication.", "Oauth 2.0 client credentials grant authentication."], "type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudApihubV1CollectApiDataRequest": {"description": "The CollectApiData method's request.", "id": "GoogleCloudApihubV1CollectApiDataRequest", "properties": {"actionId": {"description": "Required. The action ID to be used for collecting the API data. This should map to one of the action IDs specified in action configs in the plugin.", "type": "string"}, "apiData": {"$ref": "GoogleCloudApihubV1ApiData", "description": "Required. The API data to be collected."}, "collectionType": {"description": "Required. The type of collection. Applies to all entries in api_data.", "enum": ["COLLECTION_TYPE_UNSPECIFIED", "COLLECTION_TYPE_UPSERT", "COLLECTION_TYPE_DELETE"], "enumDescriptions": ["The default value. This value is used if the collection type is omitted.", "The collection type is upsert. This should be used when an API is created or updated at the source.", "The collection type is delete. This should be used when an API is deleted at the source."], "type": "string"}, "pluginInstance": {"description": "Required. The plugin instance collecting the API data. Format: `projects/{project}/locations/{location}/plugins/{plugin}/instances/{instance}`.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1Config": {"description": "Available configurations to provision an ApiHub Instance.", "id": "GoogleCloudApihubV1Config", "properties": {"cmekKeyName": {"description": "Optional. The Customer Managed Encryption Key (CMEK) used for data encryption. The CMEK name should follow the format of `projects/([^/]+)/locations/([^/]+)/keyRings/([^/]+)/cryptoKeys/([^/]+)`, where the location must match the instance location. If the CMEK is not provided, a GMEK will be created for the instance.", "type": "string"}, "disableSearch": {"description": "Optional. If true, the search will be disabled for the instance. The default value is false.", "type": "boolean"}, "encryptionType": {"description": "Optional. Encryption type for the region. If the encryption type is CMEK, the cmek_key_name must be provided. If no encryption type is provided, GMEK will be used.", "enum": ["ENCRYPTION_TYPE_UNSPECIFIED", "GMEK", "CMEK"], "enumDescriptions": ["Encryption type unspecified.", "Default encryption using Google managed encryption key.", "Encryption using customer managed encryption key."], "type": "string"}, "vertexLocation": {"description": "Optional. The name of the Vertex AI location where the data store is stored.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1ConfigTemplate": {"description": "ConfigTemplate represents the configuration template for a plugin.", "id": "GoogleCloudApihubV1ConfigTemplate", "properties": {"additionalConfigTemplate": {"description": "Optional. The list of additional configuration variables for the plugin's configuration.", "items": {"$ref": "GoogleCloudApihubV1ConfigVariableTemplate"}, "type": "array"}, "authConfigTemplate": {"$ref": "GoogleCloudApihubV1AuthConfigTemplate", "description": "Optional. The authentication template for the plugin."}}, "type": "object"}, "GoogleCloudApihubV1ConfigValueOption": {"description": "ConfigValueOption represents an option for a config variable of type enum or multi select.", "id": "GoogleCloudApihubV1ConfigValueOption", "properties": {"description": {"description": "Optional. Description of the option.", "type": "string"}, "displayName": {"description": "Required. Display name of the option.", "type": "string"}, "id": {"description": "Required. Id of the option.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1ConfigVariable": {"description": "ConfigVariable represents a additional configuration variable present in a PluginInstance Config or AuthConfig, based on a ConfigVariableTemplate.", "id": "GoogleCloudApihubV1ConfigVariable", "properties": {"boolValue": {"description": "Optional. The config variable value in case of config variable of type boolean.", "type": "boolean"}, "enumValue": {"$ref": "GoogleCloudApihubV1ConfigValueOption", "description": "Optional. The config variable value in case of config variable of type enum."}, "intValue": {"description": "Optional. The config variable value in case of config variable of type integer.", "format": "int64", "type": "string"}, "key": {"description": "Output only. Key will be the id to uniquely identify the config variable.", "readOnly": true, "type": "string"}, "multiIntValues": {"$ref": "GoogleCloudApihubV1MultiIntValues", "description": "Optional. The config variable value in case of config variable of type multi integer."}, "multiSelectValues": {"$ref": "GoogleCloudApihubV1MultiSelectValues", "description": "Optional. The config variable value in case of config variable of type multi select."}, "multiStringValues": {"$ref": "GoogleCloudApihubV1MultiStringValues", "description": "Optional. The config variable value in case of config variable of type multi string."}, "secretValue": {"$ref": "GoogleCloudApihubV1Secret", "description": "Optional. The config variable value in case of config variable of type secret."}, "stringValue": {"description": "Optional. The config variable value in case of config variable of type string.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1ConfigVariableTemplate": {"description": "ConfigVariableTemplate represents a configuration variable template present in a Plugin Config.", "id": "GoogleCloudApihubV1ConfigVariableTemplate", "properties": {"description": {"description": "Optional. Description.", "type": "string"}, "enumOptions": {"description": "Optional. Enum options. To be populated if `ValueType` is `ENUM`.", "items": {"$ref": "GoogleCloudApihubV1ConfigValueOption"}, "type": "array"}, "id": {"description": "Required. ID of the config variable. Must be unique within the configuration.", "type": "string"}, "multiSelectOptions": {"description": "Optional. Multi select options. To be populated if `ValueType` is `MULTI_SELECT`.", "items": {"$ref": "GoogleCloudApihubV1ConfigValueOption"}, "type": "array"}, "required": {"description": "Optional. Flag represents that this `ConfigVariable` must be provided for a PluginInstance.", "type": "boolean"}, "validationRegex": {"description": "Optional. Regular expression in RE2 syntax used for validating the `value` of a `ConfigVariable`.", "type": "string"}, "valueType": {"description": "Required. Type of the parameter: string, int, bool etc.", "enum": ["VALUE_TYPE_UNSPECIFIED", "STRING", "INT", "BOOL", "SECRET", "ENUM", "MULTI_SELECT", "MULTI_STRING", "MULTI_INT"], "enumDescriptions": ["Value type is not specified.", "Value type is string.", "Value type is integer.", "Value type is boolean.", "Value type is secret.", "Value type is enum.", "Value type is multi select.", "Value type is multi string.", "Value type is multi int."], "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1Curation": {"description": "A curation resource in the API Hub.", "id": "GoogleCloudApihubV1Curation", "properties": {"createTime": {"description": "Output only. The time at which the curation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. The description of the curation.", "type": "string"}, "displayName": {"description": "Required. The display name of the curation.", "type": "string"}, "endpoint": {"$ref": "GoogleCloudApihubV1Endpoint", "description": "Required. The endpoint to be triggered for curation."}, "lastExecutionErrorCode": {"description": "Output only. The error code of the last execution of the curation. The error code is populated only when the last execution state is failed.", "enum": ["ERROR_CODE_UNSPECIFIED", "INTERNAL_ERROR", "UNAUTHORIZED"], "enumDescriptions": ["Default unspecified error code.", "The execution failed due to an internal error.", "The curation is not authorized to trigger the endpoint uri."], "readOnly": true, "type": "string"}, "lastExecutionErrorMessage": {"description": "Output only. Error message describing the failure, if any, during the last execution of the curation.", "readOnly": true, "type": "string"}, "lastExecutionState": {"description": "Output only. The last execution state of the curation.", "enum": ["LAST_EXECUTION_STATE_UNSPECIFIED", "SUCCEEDED", "FAILED"], "enumDescriptions": ["Default unspecified state.", "The last curation execution was successful.", "The last curation execution failed."], "readOnly": true, "type": "string"}, "name": {"description": "Identifier. The name of the curation. Format: `projects/{project}/locations/{location}/curations/{curation}`", "type": "string"}, "pluginInstanceActions": {"description": "Output only. The plugin instances and associated actions that are using the curation. Note: A particular curation could be used by multiple plugin instances or multiple actions in a plugin instance.", "items": {"$ref": "GoogleCloudApihubV1PluginInstanceActionID"}, "readOnly": true, "type": "array"}, "updateTime": {"description": "Output only. The time at which the curation was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1CurationConfig": {"description": "The curation information for this plugin instance.", "id": "GoogleCloudApihubV1CurationConfig", "properties": {"curationType": {"description": "Required. The curation type for this plugin instance.", "enum": ["CURATION_TYPE_UNSPECIFIED", "DEFAULT_CURATION_FOR_API_METADATA", "CUSTOM_CURATION_FOR_API_METADATA"], "enumDescriptions": ["Default unspecified curation type.", "Default curation for API metadata will be used.", "Custom curation for API metadata will be used."], "type": "string"}, "customCuration": {"$ref": "GoogleCloudApihubV1CustomCuration", "description": "Optional. Custom curation information for this plugin instance."}}, "type": "object"}, "GoogleCloudApihubV1CustomCuration": {"description": "Custom curation information for this plugin instance.", "id": "GoogleCloudApihubV1CustomCuration", "properties": {"curation": {"description": "Required. The unique name of the curation resource. This will be the name of the curation resource in the format: `projects/{project}/locations/{location}/curations/{curation}`", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1Definition": {"description": "Represents a definition for example schema, request, response definitions contained in an API version. A definition is added/updated/deleted in an API version when a new spec is added or an existing spec is updated/deleted in a version. Currently, definition will be created only corresponding to OpenAPI spec as parsing is supported for OpenAPI spec. Also, within OpenAPI spec, only `schema` object is supported.", "id": "GoogleCloudApihubV1Definition", "properties": {"attributes": {"additionalProperties": {"$ref": "GoogleCloudApihubV1AttributeValues"}, "description": "Optional. The list of user defined attributes associated with the definition resource. The key is the attribute name. It will be of the format: `projects/{project}/locations/{location}/attributes/{attribute}`. The value is the attribute values associated with the resource.", "type": "object"}, "createTime": {"description": "Output only. The time at which the definition was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Identifier. The name of the definition. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}/definitions/{definition}`", "type": "string"}, "schema": {"$ref": "GoogleCloudApihubV1Schema", "description": "Output only. The value of a schema definition.", "readOnly": true}, "spec": {"description": "Output only. The name of the spec from where the definition was parsed. Format is `projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`", "readOnly": true, "type": "string"}, "type": {"description": "Output only. The type of the definition.", "enum": ["TYPE_UNSPECIFIED", "SCHEMA"], "enumDescriptions": ["Definition type unspecified.", "Definition type schema."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time at which the definition was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1Dependency": {"description": "A dependency resource defined in the API hub describes a dependency directed from a consumer to a supplier entity. A dependency can be defined between two Operations or between an Operation and External API.", "id": "GoogleCloudApihubV1Dependency", "properties": {"attributes": {"additionalProperties": {"$ref": "GoogleCloudApihubV1AttributeValues"}, "description": "Optional. The list of user defined attributes associated with the dependency resource. The key is the attribute name. It will be of the format: `projects/{project}/locations/{location}/attributes/{attribute}`. The value is the attribute values associated with the resource.", "type": "object"}, "consumer": {"$ref": "GoogleCloudApihubV1DependencyEntityReference", "description": "Required. Immutable. The entity acting as the consumer in the dependency."}, "createTime": {"description": "Output only. The time at which the dependency was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. Human readable description corresponding of the dependency.", "type": "string"}, "discoveryMode": {"description": "Output only. Discovery mode of the dependency.", "enum": ["DISCOVERY_MODE_UNSPECIFIED", "MANUAL"], "enumDescriptions": ["Default value. This value is unused.", "Manual mode of discovery when the dependency is defined by the user."], "readOnly": true, "type": "string"}, "errorDetail": {"$ref": "GoogleCloudApihubV1DependencyErrorDetail", "description": "Output only. Error details of a dependency if the system has detected it internally.", "readOnly": true}, "name": {"description": "Identifier. The name of the dependency in the API Hub. Format: `projects/{project}/locations/{location}/dependencies/{dependency}`", "type": "string"}, "state": {"description": "Output only. State of the dependency.", "enum": ["STATE_UNSPECIFIED", "PROPOSED", "VALIDATED"], "enumDescriptions": ["Default value. This value is unused.", "Dependency will be in a proposed state when it is newly identified by the API hub on its own.", "Dependency will be in a validated state when it is validated by the admin or manually created in the API hub."], "readOnly": true, "type": "string"}, "supplier": {"$ref": "GoogleCloudApihubV1DependencyEntityReference", "description": "Required. Immutable. The entity acting as the supplier in the dependency."}, "updateTime": {"description": "Output only. The time at which the dependency was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1DependencyEntityReference": {"description": "Reference to an entity participating in a dependency.", "id": "GoogleCloudApihubV1DependencyEntityReference", "properties": {"displayName": {"description": "Output only. Display name of the entity.", "readOnly": true, "type": "string"}, "externalApiResourceName": {"description": "The resource name of an external API in the API Hub. Format: `projects/{project}/locations/{location}/externalApis/{external_api}`", "type": "string"}, "operationResourceName": {"description": "The resource name of an operation in the API Hub. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}/operations/{operation}`", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1DependencyErrorDetail": {"description": "Details describing error condition of a dependency.", "id": "GoogleCloudApihubV1DependencyErrorDetail", "properties": {"error": {"description": "Optional. Error in the dependency.", "enum": ["ERROR_UNSPECIFIED", "SUPPLIER_NOT_FOUND", "SUPPLIER_RECREATED"], "enumDescriptions": ["Default value used for no error in the dependency.", "Supplier entity has been deleted.", "Supplier entity has been recreated."], "type": "string"}, "errorTime": {"description": "Optional. Timestamp at which the error was found.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1Deployment": {"description": "Details of the deployment where APIs are hosted. A deployment could represent an Apigee proxy, API gateway, other Google Cloud services or non-Google Cloud services as well. A deployment entity is a root level entity in the API hub and exists independent of any API.", "id": "GoogleCloudApihubV1Deployment", "properties": {"apiVersions": {"description": "Output only. The API versions linked to this deployment. Note: A particular deployment could be linked to multiple different API versions (of same or different APIs).", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "attributes": {"additionalProperties": {"$ref": "GoogleCloudApihubV1AttributeValues"}, "description": "Optional. The list of user defined attributes associated with the deployment resource. The key is the attribute name. It will be of the format: `projects/{project}/locations/{location}/attributes/{attribute}`. The value is the attribute values associated with the resource.", "type": "object"}, "createTime": {"description": "Output only. The time at which the deployment was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deploymentType": {"$ref": "GoogleCloudApihubV1AttributeValues", "description": "Required. The type of deployment. This maps to the following system defined attribute: `projects/{project}/locations/{location}/attributes/system-deployment-type` attribute. The number of values for this attribute will be based on the cardinality of the attribute. The same can be retrieved via GetAttribute API. All values should be from the list of allowed values defined for the attribute."}, "description": {"description": "Optional. The description of the deployment.", "type": "string"}, "displayName": {"description": "Required. The display name of the deployment.", "type": "string"}, "documentation": {"$ref": "GoogleCloudApihubV1Documentation", "description": "Optional. The documentation of the deployment."}, "endpoints": {"description": "Required. The endpoints at which this deployment resource is listening for API requests. This could be a list of complete URIs, hostnames or an IP addresses.", "items": {"type": "string"}, "type": "array"}, "environment": {"$ref": "GoogleCloudApihubV1AttributeValues", "description": "Optional. The environment mapping to this deployment. This maps to the following system defined attribute: `projects/{project}/locations/{location}/attributes/system-environment` attribute. The number of values for this attribute will be based on the cardinality of the attribute. The same can be retrieved via GetAttribute API. All values should be from the list of allowed values defined for the attribute."}, "name": {"description": "Identifier. The name of the deployment. Format: `projects/{project}/locations/{location}/deployments/{deployment}`", "type": "string"}, "resourceUri": {"description": "Required. A uri that uniquely identfies the deployment within a particular gateway. For example, if the runtime resource is of type APIGEE_PROXY, then this field will be a combination of org, proxy name and environment.", "type": "string"}, "slo": {"$ref": "GoogleCloudApihubV1AttributeValues", "description": "Optional. The SLO for this deployment. This maps to the following system defined attribute: `projects/{project}/locations/{location}/attributes/system-slo` attribute. The number of values for this attribute will be based on the cardinality of the attribute. The same can be retrieved via GetAttribute API. All values should be from the list of allowed values defined for the attribute."}, "sourceMetadata": {"description": "Output only. The list of sources and metadata from the sources of the deployment.", "items": {"$ref": "GoogleCloudApihubV1SourceMetadata"}, "readOnly": true, "type": "array"}, "updateTime": {"description": "Output only. The time at which the deployment was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1DeploymentMetadata": {"description": "The metadata associated with a deployment.", "id": "GoogleCloudApihubV1DeploymentMetadata", "properties": {"deployment": {"$ref": "GoogleCloudApihubV1Deployment", "description": "Required. The deployment resource to be pushed to Hub's collect layer. The ID of the deployment will be generated by Hub."}, "originalCreateTime": {"description": "Optional. Timestamp indicating when the deployment was created at the source.", "format": "google-datetime", "type": "string"}, "originalId": {"description": "Optional. The unique identifier of the deployment in the system where it was originally created.", "type": "string"}, "originalUpdateTime": {"description": "Required. Timestamp indicating when the deployment was last updated at the source.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1DisablePluginInstanceActionRequest": {"description": "The DisablePluginInstanceAction method's request.", "id": "GoogleCloudApihubV1DisablePluginInstanceActionRequest", "properties": {"actionId": {"description": "Required. The action id to disable.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1DisablePluginRequest": {"description": "The DisablePlugin method's request.", "id": "GoogleCloudApihubV1DisablePluginRequest", "properties": {}, "type": "object"}, "GoogleCloudApihubV1Documentation": {"description": "Documentation details.", "id": "GoogleCloudApihubV1Documentation", "properties": {"externalUri": {"description": "Optional. The uri of the externally hosted documentation.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1EnablePluginInstanceActionRequest": {"description": "The EnablePluginInstanceAction method's request.", "id": "GoogleCloudApihubV1EnablePluginInstanceActionRequest", "properties": {"actionId": {"description": "Required. The action id to enable.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1EnablePluginRequest": {"description": "The EnablePlugin method's request.", "id": "GoogleCloudApihubV1EnablePluginRequest", "properties": {}, "type": "object"}, "GoogleCloudApihubV1Endpoint": {"description": "The endpoint to be triggered for curation. The endpoint will be invoked with a request payload containing ApiMetadata. Response should contain curated data in the form of ApiMetadata.", "id": "GoogleCloudApihubV1Endpoint", "properties": {"applicationIntegrationEndpointDetails": {"$ref": "GoogleCloudApihubV1ApplicationIntegrationEndpointDetails", "description": "Required. The details of the Application Integration endpoint to be triggered for curation."}}, "type": "object"}, "GoogleCloudApihubV1EnumAttributeValues": {"description": "The attribute values of data type enum.", "id": "GoogleCloudApihubV1EnumAttributeValues", "properties": {"values": {"description": "Required. The attribute values in case attribute data type is enum.", "items": {"$ref": "GoogleCloudApihubV1AllowedValue"}, "type": "array"}}, "type": "object"}, "GoogleCloudApihubV1ExecutePluginInstanceActionRequest": {"description": "The ExecutePluginInstanceAction method's request.", "id": "GoogleCloudApihubV1ExecutePluginInstanceActionRequest", "properties": {"actionExecutionDetail": {"$ref": "GoogleCloudApihubV1ActionExecutionDetail", "description": "Required. The execution details for the action to execute."}}, "type": "object"}, "GoogleCloudApihubV1ExecutionStatus": {"description": "The execution status for the plugin instance.", "id": "GoogleCloudApihubV1ExecutionStatus", "properties": {"currentExecutionState": {"description": "Output only. The current state of the execution.", "enum": ["CURRENT_EXECUTION_STATE_UNSPECIFIED", "RUNNING", "NOT_RUNNING"], "enumDescriptions": ["Default unspecified execution state.", "The plugin instance is executing.", "The plugin instance is not running an execution."], "readOnly": true, "type": "string"}, "lastExecution": {"$ref": "GoogleCloudApihubV1LastExecution", "description": "Output only. The last execution of the plugin instance.", "readOnly": true}}, "type": "object"}, "GoogleCloudApihubV1ExternalApi": {"description": "An external API represents an API being provided by external sources. This can be used to model third-party APIs and can be used to define dependencies.", "id": "GoogleCloudApihubV1ExternalApi", "properties": {"attributes": {"additionalProperties": {"$ref": "GoogleCloudApihubV1AttributeValues"}, "description": "Optional. The list of user defined attributes associated with the Version resource. The key is the attribute name. It will be of the format: `projects/{project}/locations/{location}/attributes/{attribute}`. The value is the attribute values associated with the resource.", "type": "object"}, "createTime": {"description": "Output only. Creation timestamp.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. Description of the external API. Max length is 2000 characters (Unicode Code Points).", "type": "string"}, "displayName": {"description": "Required. Display name of the external API. Max length is 63 characters (Unicode Code Points).", "type": "string"}, "documentation": {"$ref": "GoogleCloudApihubV1Documentation", "description": "Optional. Documentation of the external API."}, "endpoints": {"description": "Optional. List of endpoints on which this API is accessible.", "items": {"type": "string"}, "type": "array"}, "name": {"description": "Identifier. Format: `projects/{project}/locations/{location}/externalApi/{externalApi}`.", "type": "string"}, "paths": {"description": "Optional. List of paths served by this API.", "items": {"type": "string"}, "type": "array"}, "updateTime": {"description": "Output only. Last update timestamp.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1GoogleServiceAccountConfig": {"description": "Config for Google service account authentication.", "id": "GoogleCloudApihubV1GoogleServiceAccountConfig", "properties": {"serviceAccount": {"description": "Required. The service account to be used for authenticating request. The `iam.serviceAccounts.getAccessToken` permission should be granted on this service account to the impersonator service account.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1HostProjectRegistration": {"description": "Host project registration refers to the registration of a Google cloud project with Api Hub as a host project. This is the project where Api Hub is provisioned. It acts as the consumer project for the Api Hub instance provisioned. Multiple runtime projects can be attached to the host project and these attachments define the scope of Api Hub.", "id": "GoogleCloudApihubV1HostProjectRegistration", "properties": {"createTime": {"description": "Output only. The time at which the host project registration was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "gcpProject": {"description": "Required. Immutable. Google cloud project name in the format: \"projects/abc\" or \"projects/123\". As input, project name with either project id or number are accepted. As output, this field will contain project number.", "type": "string"}, "name": {"description": "Identifier. The name of the host project registration. Format: \"projects/{project}/locations/{location}/hostProjectRegistrations/{host_project_registration}\".", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1HostingService": {"description": "The information related to the service implemented by the plugin developer, used to invoke the plugin's functionality.", "id": "GoogleCloudApihubV1HostingService", "properties": {"serviceUri": {"description": "Optional. The URI of the service implemented by the plugin developer, used to invoke the plugin's functionality. This information is only required for user defined plugins.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1HttpOperation": {"description": "The HTTP Operation.", "id": "GoogleCloudApihubV1HttpOperation", "properties": {"method": {"description": "Optional. Operation method Note: Even though this field is optional, it is required for CreateApiOperation API and we will fail the request if not provided.", "enum": ["METHOD_UNSPECIFIED", "GET", "PUT", "POST", "DELETE", "OPTIONS", "HEAD", "PATCH", "TRACE"], "enumDescriptions": ["Method unspecified.", "Get Operation type.", "Put Operation type.", "Post Operation type.", "Delete Operation type.", "Options Operation type.", "Head Operation type.", "Patch Operation type.", "Trace Operation type."], "type": "string"}, "path": {"$ref": "GoogleCloudApihubV1Path", "description": "Optional. The path details for the Operation. Note: Even though this field is optional, it is required for CreateApiOperation API and we will fail the request if not provided."}}, "type": "object"}, "GoogleCloudApihubV1Issue": {"description": "Issue contains the details of a single issue found by the linter.", "id": "GoogleCloudApihubV1Issue", "properties": {"code": {"description": "Required. Rule code unique to each rule defined in linter.", "type": "string"}, "message": {"description": "Required. Human-readable message describing the issue found by the linter.", "type": "string"}, "path": {"description": "Required. An array of strings indicating the location in the analyzed document where the rule was triggered.", "items": {"type": "string"}, "type": "array"}, "range": {"$ref": "GoogleCloudApihubV1Range", "description": "Required. Object describing where in the file the issue was found."}, "severity": {"description": "Required. Severity level of the rule violation.", "enum": ["SEVERITY_UNSPECIFIED", "SEVERITY_ERROR", "SEVERITY_WARNING", "SEVERITY_INFO", "SEVERITY_HINT"], "enumDescriptions": ["Severity unspecified.", "Severity error.", "Severity warning.", "Severity info.", "Severity hint."], "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1LastExecution": {"description": "The result of the last execution of the plugin instance.", "id": "GoogleCloudApihubV1LastExecution", "properties": {"endTime": {"description": "Output only. The last execution end time of the plugin instance.", "format": "google-datetime", "readOnly": true, "type": "string"}, "errorMessage": {"description": "Output only. Error message describing the failure, if any, during the last execution.", "readOnly": true, "type": "string"}, "result": {"description": "Output only. The result of the last execution of the plugin instance.", "enum": ["RESULT_UNSPECIFIED", "SUCCEEDED", "FAILED"], "enumDescriptions": ["Default unspecified execution result.", "The plugin instance executed successfully.", "The plugin instance execution failed."], "readOnly": true, "type": "string"}, "startTime": {"description": "Output only. The last execution start time of the plugin instance.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1LintResponse": {"description": "LintResponse contains the response from the linter.", "id": "GoogleCloudApihubV1LintResponse", "properties": {"createTime": {"description": "Required. Timestamp when the linting response was generated.", "format": "google-datetime", "type": "string"}, "issues": {"description": "Optional. Array of issues found in the analyzed document.", "items": {"$ref": "GoogleCloudApihubV1Issue"}, "type": "array"}, "linter": {"description": "Required. Name of the linter used.", "enum": ["LINTER_UNSPECIFIED", "SPECTRAL", "OTHER"], "enumDescriptions": ["Linter type unspecified.", "Linter type spectral.", "Linter type other."], "type": "string"}, "source": {"description": "Required. Name of the linting application.", "type": "string"}, "state": {"description": "Required. Lint state represents success or failure for linting.", "enum": ["LINT_STATE_UNSPECIFIED", "LINT_STATE_SUCCESS", "LINT_STATE_ERROR"], "enumDescriptions": ["Lint state unspecified.", "Linting was completed successfully.", "<PERSON><PERSON> encountered errors."], "type": "string"}, "summary": {"description": "Optional. Summary of all issue types and counts for each severity level.", "items": {"$ref": "GoogleCloudApihubV1SummaryEntry"}, "type": "array"}}, "type": "object"}, "GoogleCloudApihubV1LintSpecRequest": {"description": "The LintSpec method's request.", "id": "GoogleCloudApihubV1LintSpecRequest", "properties": {}, "type": "object"}, "GoogleCloudApihubV1ListApiOperationsResponse": {"description": "The ListApiOperations method's response.", "id": "GoogleCloudApihubV1ListApiOperationsResponse", "properties": {"apiOperations": {"description": "The operations corresponding to an API version.", "items": {"$ref": "GoogleCloudApihubV1ApiOperation"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1ListApisResponse": {"description": "The ListApis method's response.", "id": "GoogleCloudApihubV1ListApisResponse", "properties": {"apis": {"description": "The API resources present in the API hub.", "items": {"$ref": "GoogleCloudApihubV1Api"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1ListAttributesResponse": {"description": "The ListAttributes method's response.", "id": "GoogleCloudApihubV1ListAttributesResponse", "properties": {"attributes": {"description": "The list of all attributes.", "items": {"$ref": "GoogleCloudApihubV1Attribute"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1ListCurationsResponse": {"description": "The ListCurations method's response.", "id": "GoogleCloudApihubV1ListCurationsResponse", "properties": {"curations": {"description": "The curation resources present in the API hub.", "items": {"$ref": "GoogleCloudApihubV1Curation"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1ListDependenciesResponse": {"description": "The ListDependencies method's response.", "id": "GoogleCloudApihubV1ListDependenciesResponse", "properties": {"dependencies": {"description": "The dependency resources present in the API hub.", "items": {"$ref": "GoogleCloudApihubV1Dependency"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1ListDeploymentsResponse": {"description": "The ListDeployments method's response.", "id": "GoogleCloudApihubV1ListDeploymentsResponse", "properties": {"deployments": {"description": "The deployment resources present in the API hub.", "items": {"$ref": "GoogleCloudApihubV1Deployment"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1ListExternalApisResponse": {"description": "The ListExternalApis method's response.", "id": "GoogleCloudApihubV1ListExternalApisResponse", "properties": {"externalApis": {"description": "The External API resources present in the API hub.", "items": {"$ref": "GoogleCloudApihubV1ExternalApi"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1ListHostProjectRegistrationsResponse": {"description": "The ListHostProjectRegistrations method's response.", "id": "GoogleCloudApihubV1ListHostProjectRegistrationsResponse", "properties": {"hostProjectRegistrations": {"description": "The list of host project registrations.", "items": {"$ref": "GoogleCloudApihubV1HostProjectRegistration"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1ListPluginInstancesResponse": {"description": "The ListPluginInstances method's response.", "id": "GoogleCloudApihubV1ListPluginInstancesResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "pluginInstances": {"description": "The plugin instances from the specified parent resource.", "items": {"$ref": "GoogleCloudApihubV1PluginInstance"}, "type": "array"}}, "type": "object"}, "GoogleCloudApihubV1ListPluginsResponse": {"description": "The ListPlugins method's response.", "id": "GoogleCloudApihubV1ListPluginsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "plugins": {"description": "The plugins from the specified parent resource.", "items": {"$ref": "GoogleCloudApihubV1Plugin"}, "type": "array"}}, "type": "object"}, "GoogleCloudApihubV1ListRuntimeProjectAttachmentsResponse": {"description": "The ListRuntimeProjectAttachments method's response.", "id": "GoogleCloudApihubV1ListRuntimeProjectAttachmentsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "runtimeProjectAttachments": {"description": "List of runtime project attachments.", "items": {"$ref": "GoogleCloudApihubV1RuntimeProjectAttachment"}, "type": "array"}}, "type": "object"}, "GoogleCloudApihubV1ListSpecsResponse": {"description": "The ListSpecs method's response.", "id": "GoogleCloudApihubV1ListSpecsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "specs": {"description": "The specs corresponding to an API Version.", "items": {"$ref": "GoogleCloudApihubV1Spec"}, "type": "array"}}, "type": "object"}, "GoogleCloudApihubV1ListVersionsResponse": {"description": "The ListVersions method's response.", "id": "GoogleCloudApihubV1ListVersionsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "versions": {"description": "The versions corresponding to an API.", "items": {"$ref": "GoogleCloudApihubV1Version"}, "type": "array"}}, "type": "object"}, "GoogleCloudApihubV1LookupApiHubInstanceResponse": {"description": "The LookupApiHubInstance method's response.`", "id": "GoogleCloudApihubV1LookupApiHubInstanceResponse", "properties": {"apiHubInstance": {"$ref": "GoogleCloudApihubV1ApiHubInstance", "description": "API Hub instance for a project if it exists, empty otherwise."}}, "type": "object"}, "GoogleCloudApihubV1LookupRuntimeProjectAttachmentResponse": {"description": "The ListRuntimeProjectAttachments method's response.", "id": "GoogleCloudApihubV1LookupRuntimeProjectAttachmentResponse", "properties": {"runtimeProjectAttachment": {"$ref": "GoogleCloudApihubV1RuntimeProjectAttachment", "description": "Runtime project attachment for a project if exists, empty otherwise."}}, "type": "object"}, "GoogleCloudApihubV1MultiIntValues": {"description": "The config variable value of data type multi int.", "id": "GoogleCloudApihubV1MultiIntValues", "properties": {"values": {"description": "Optional. The config variable value of data type multi int.", "items": {"format": "int32", "type": "integer"}, "type": "array"}}, "type": "object"}, "GoogleCloudApihubV1MultiSelectValues": {"description": "The config variable value of data type multi select.", "id": "GoogleCloudApihubV1MultiSelectValues", "properties": {"values": {"description": "Optional. The config variable value of data type multi select.", "items": {"$ref": "GoogleCloudApihubV1ConfigValueOption"}, "type": "array"}}, "type": "object"}, "GoogleCloudApihubV1MultiStringValues": {"description": "The config variable value of data type multi string.", "id": "GoogleCloudApihubV1MultiStringValues", "properties": {"values": {"description": "Optional. The config variable value of data type multi string.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudApihubV1Oauth2ClientCredentialsConfig": {"description": "Parameters to support Oauth 2.0 client credentials grant authentication. See https://tools.ietf.org/html/rfc6749#section-1.3.4 for more details.", "id": "GoogleCloudApihubV1Oauth2ClientCredentialsConfig", "properties": {"clientId": {"description": "Required. The client identifier.", "type": "string"}, "clientSecret": {"$ref": "GoogleCloudApihubV1Secret", "description": "Required. Secret version reference containing the client secret. The `secretmanager.versions.access` permission should be granted to the service account accessing the secret."}}, "type": "object"}, "GoogleCloudApihubV1OpenApiSpecDetails": {"description": "OpenApiSpecDetails contains the details parsed from an OpenAPI spec in addition to the fields mentioned in SpecDetails.", "id": "GoogleCloudApihubV1OpenApiSpecDetails", "properties": {"format": {"description": "Output only. The format of the spec.", "enum": ["FORMAT_UNSPECIFIED", "OPEN_API_SPEC_2_0", "OPEN_API_SPEC_3_0", "OPEN_API_SPEC_3_1"], "enumDescriptions": ["SpecFile type unspecified.", "OpenAPI Spec v2.0.", "OpenAPI Spec v3.0.", "OpenAPI Spec v3.1."], "readOnly": true, "type": "string"}, "owner": {"$ref": "GoogleCloudApihubV1Owner", "description": "Output only. Owner details for the spec. This maps to `info.contact` in OpenAPI spec.", "readOnly": true}, "version": {"description": "Output only. The version in the spec. This maps to `info.version` in OpenAPI spec.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1OperationDetails": {"description": "The operation details parsed from the spec.", "id": "GoogleCloudApihubV1OperationDetails", "properties": {"deprecated": {"description": "Optional. For OpenAPI spec, this will be set if `operation.deprecated`is marked as `true` in the spec.", "type": "boolean"}, "description": {"description": "Optional. Description of the operation behavior. For OpenAPI spec, this will map to `operation.description` in the spec, in case description is empty, `operation.summary` will be used.", "type": "string"}, "documentation": {"$ref": "GoogleCloudApihubV1Documentation", "description": "Optional. Additional external documentation for this operation. For OpenAPI spec, this will map to `operation.documentation` in the spec."}, "httpOperation": {"$ref": "GoogleCloudApihubV1HttpOperation", "description": "The HTTP Operation."}}, "type": "object"}, "GoogleCloudApihubV1OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "GoogleCloudApihubV1OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have been cancelled successfully have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1Owner": {"description": "Owner details.", "id": "GoogleCloudApihubV1Owner", "properties": {"displayName": {"description": "Optional. The name of the owner.", "type": "string"}, "email": {"description": "Required. The email of the owner.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1Path": {"description": "The path details derived from the spec.", "id": "GoogleCloudApihubV1Path", "properties": {"description": {"description": "Optional. A short description for the path applicable to all operations.", "type": "string"}, "path": {"description": "Optional. Complete path relative to server endpoint. Note: Even though this field is optional, it is required for CreateApiOperation API and we will fail the request if not provided.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1Plugin": {"description": "A plugin resource in the API Hub.", "id": "GoogleCloudApihubV1Plugin", "properties": {"actionsConfig": {"description": "Optional. The configuration of actions supported by the plugin.", "items": {"$ref": "GoogleCloudApihubV1PluginActionConfig"}, "type": "array"}, "configTemplate": {"$ref": "GoogleCloudApihubV1ConfigTemplate", "description": "Optional. The configuration template for the plugin."}, "createTime": {"description": "Output only. Timestamp indicating when the plugin was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. The plugin description. Max length is 2000 characters (Unicode code points).", "type": "string"}, "displayName": {"description": "Required. The display name of the plugin. Max length is 50 characters (Unicode code points).", "type": "string"}, "documentation": {"$ref": "GoogleCloudApihubV1Documentation", "description": "Optional. The documentation of the plugin, that explains how to set up and use the plugin."}, "hostingService": {"$ref": "GoogleCloudApihubV1HostingService", "description": "Optional. This field is optional. It is used to notify the plugin hosting service for any lifecycle changes of the plugin instance and trigger execution of plugin instance actions in case of API hub managed actions. This field should be provided if the plugin instance lifecycle of the developed plugin needs to be managed from API hub. Also, in this case the plugin hosting service interface needs to be implemented. This field should not be provided if the plugin wants to manage plugin instance lifecycle events outside of hub interface and use plugin framework for only registering of plugin and plugin instances to capture the source of data into hub. Note, in this case the plugin hosting service interface is not required to be implemented. Also, the plugin instance lifecycle actions will be disabled from API hub's UI."}, "name": {"description": "Identifier. The name of the plugin. Format: `projects/{project}/locations/{location}/plugins/{plugin}`", "type": "string"}, "ownershipType": {"description": "Output only. The type of the plugin, indicating whether it is 'SYSTEM_OWNED' or 'USER_OWNED'.", "enum": ["OWNERSHIP_TYPE_UNSPECIFIED", "SYSTEM_OWNED", "USER_OWNED"], "enumDescriptions": ["Default unspecified type.", "System owned plugins are defined by API hub and are available out of the box in API hub.", "User owned plugins are defined by the user and need to be explicitly added to API hub via CreatePlugin method."], "readOnly": true, "type": "string"}, "pluginCategory": {"description": "Optional. The category of the plugin, identifying its primary category or purpose. This field is required for all plugins.", "enum": ["PLUGIN_CATEGORY_UNSPECIFIED", "API_GATEWAY", "API_PRODUCER"], "enumDescriptions": ["Default unspecified plugin type.", "API_GATEWAY plugins represent plugins built for API Gateways like Apigee.", "API_PRODUCER plugins represent plugins built for API Producers like Cloud Run, Application Integration etc."], "type": "string"}, "state": {"description": "Output only. Represents the state of the plugin. Note this field will not be set for plugins developed via plugin framework as the state will be managed at plugin instance level.", "enum": ["STATE_UNSPECIFIED", "ENABLED", "DISABLED"], "enumDescriptions": ["The default value. This value is used if the state is omitted.", "The plugin is enabled.", "The plugin is disabled."], "readOnly": true, "type": "string"}, "type": {"$ref": "GoogleCloudApihubV1AttributeValues", "description": "Optional. The type of the API. This maps to the following system defined attribute: `projects/{project}/locations/{location}/attributes/system-plugin-type` attribute. The number of allowed values for this attribute will be based on the cardinality of the attribute. The same can be retrieved via GetAttribute API. All values should be from the list of allowed values defined for the attribute. Note this field is not required for plugins developed via plugin framework."}, "updateTime": {"description": "Output only. Timestamp indicating when the plugin was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1PluginActionConfig": {"description": "PluginActionConfig represents the configuration of an action supported by a plugin.", "id": "GoogleCloudApihubV1PluginActionConfig", "properties": {"description": {"description": "Required. The description of the operation performed by the action.", "type": "string"}, "displayName": {"description": "Required. The display name of the action.", "type": "string"}, "id": {"description": "Required. The id of the action.", "type": "string"}, "triggerMode": {"description": "Required. The trigger mode supported by the action.", "enum": ["TRIGGER_MODE_UNSPECIFIED", "API_HUB_ON_DEMAND_TRIGGER", "API_HUB_SCHEDULE_TRIGGER", "NON_API_HUB_MANAGED"], "enumDescriptions": ["Default unspecified mode.", "This action can be executed by invoking ExecutePluginInstanceAction API with the given action id. To support this, the plugin hosting service should handle this action id as part of execute call.", "This action will be executed on schedule by invoking ExecutePluginInstanceAction API with the given action id. To set the schedule, the user can provide the cron expression in the PluginAction field for a given plugin instance. To support this, the plugin hosting service should handle this action id as part of execute call. Note, on demand execution will be supported by default in this trigger mode.", "The execution of this plugin is not handled by API hub. In this case, the plugin hosting service need not handle this action id as part of the execute call."], "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1PluginInstance": {"description": "Represents a plugin instance resource in the API Hub. A PluginInstance is a specific instance of a hub plugin with its own configuration, state, and execution details.", "id": "GoogleCloudApihubV1PluginInstance", "properties": {"actions": {"description": "Required. The action status for the plugin instance.", "items": {"$ref": "GoogleCloudApihubV1PluginInstanceAction"}, "type": "array"}, "additionalConfig": {"additionalProperties": {"$ref": "GoogleCloudApihubV1ConfigVariable"}, "description": "Optional. The additional information for this plugin instance corresponding to the additional config template of the plugin. This information will be sent to plugin hosting service on each call to plugin hosted service. The key will be the config_variable_template.display_name to uniquely identify the config variable.", "type": "object"}, "authConfig": {"$ref": "GoogleCloudApihubV1AuthConfig", "description": "Optional. The authentication information for this plugin instance."}, "createTime": {"description": "Output only. Timestamp indicating when the plugin instance was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Required. The display name for this plugin instance. Max length is 255 characters.", "type": "string"}, "errorMessage": {"description": "Output only. Error message describing the failure, if any, during Create, Delete or ApplyConfig operation corresponding to the plugin instance.This field will only be populated if the plugin instance is in the ERROR or FAILED state.", "readOnly": true, "type": "string"}, "name": {"description": "Identifier. The unique name of the plugin instance resource. Format: `projects/{project}/locations/{location}/plugins/{plugin}/instances/{instance}`", "type": "string"}, "state": {"description": "Output only. The current state of the plugin instance (e.g., enabled, disabled, provisioning).", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "APPLYING_CONFIG", "ERROR", "FAILED", "DELETING"], "enumDescriptions": ["Default unspecified state.", "The plugin instance is being created.", "The plugin instance is active and ready for executions. This is the only state where executions can run on the plugin instance.", "The updated config that contains additional_config and auth_config is being applied.", "The ERROR state can come while applying config. Users can retrigger ApplyPluginInstanceConfig to restore the plugin instance back to active state. Note, In case the ERROR state happens while applying config (auth_config, additional_config), the plugin instance will reflect the config which was trying to be applied while error happened. In order to overwrite, trigger ApplyConfig with a new config.", "The plugin instance is in a failed state. This indicates that an unrecoverable error occurred during a previous operation (Create, Delete).", "The plugin instance is being deleted. Delete is only possible if there is no other operation running on the plugin instance and plugin instance action."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Timestamp indicating when the plugin instance was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1PluginInstanceAction": {"description": "PluginInstanceAction represents an action which can be executed in the plugin instance.", "id": "GoogleCloudApihubV1PluginInstanceAction", "properties": {"actionId": {"description": "Required. This should map to one of the action id specified in actions_config in the plugin.", "type": "string"}, "curationConfig": {"$ref": "GoogleCloudApihubV1CurationConfig", "description": "Optional. This configuration should be provided if the plugin action is publishing data to API hub curate layer."}, "hubInstanceAction": {"$ref": "GoogleCloudApihubV1ExecutionStatus", "description": "Optional. The execution information for the plugin instance action done corresponding to an API hub instance."}, "scheduleCronExpression": {"description": "Optional. The schedule for this plugin instance action. This can only be set if the plugin supports API_HUB_SCHEDULE_TRIGGER mode for this action.", "type": "string"}, "scheduleTimeZone": {"description": "Optional. The time zone for the schedule cron expression. If not provided, UTC will be used.", "type": "string"}, "state": {"description": "Output only. The current state of the plugin action in the plugin instance.", "enum": ["STATE_UNSPECIFIED", "ENABLED", "DISABLED", "ENABLING", "DISABLING", "ERROR"], "enumDescriptions": ["Default unspecified state.", "The action is enabled in the plugin instance i.e., executions can be triggered for this action.", "The action is disabled in the plugin instance i.e., no executions can be triggered for this action. This state indicates that the user explicitly disabled the instance, and no further action is needed unless the user wants to re-enable it.", "The action in the plugin instance is being enabled.", "The action in the plugin instance is being disabled.", "The ERROR state can come while enabling/disabling plugin instance action. Users can retrigger enable, disable via EnablePluginInstanceAction and DisablePluginInstanceAction to restore the action back to enabled/disabled state. Note enable/disable on actions can only be triggered if plugin instance is in Active state."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1PluginInstanceActionID": {"description": "The plugin instance and associated action that is using the curation.", "id": "GoogleCloudApihubV1PluginInstanceActionID", "properties": {"actionId": {"description": "Output only. The action ID that is using the curation. This should map to one of the action IDs specified in action configs in the plugin.", "readOnly": true, "type": "string"}, "pluginInstance": {"description": "Output only. Plugin instance that is using the curation. Format is `projects/{project}/locations/{location}/plugins/{plugin}/instances/{instance}`", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1PluginInstanceActionSource": {"description": "PluginInstanceActionSource represents the plugin instance action source.", "id": "GoogleCloudApihubV1PluginInstanceActionSource", "properties": {"actionId": {"description": "Output only. The id of the plugin instance action.", "readOnly": true, "type": "string"}, "pluginInstance": {"description": "Output only. The resource name of the source plugin instance. Format is `projects/{project}/locations/{location}/plugins/{plugin}/instances/{instance}`", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1Point": {"description": "Point within the file (line and character).", "id": "GoogleCloudApihubV1Point", "properties": {"character": {"description": "Required. Character position within the line (zero-indexed).", "format": "int32", "type": "integer"}, "line": {"description": "Required. Line number (zero-indexed).", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudApihubV1Range": {"description": "Object describing where in the file the issue was found.", "id": "GoogleCloudApihubV1Range", "properties": {"end": {"$ref": "GoogleCloudApihubV1Point", "description": "Required. End of the issue."}, "start": {"$ref": "GoogleCloudApihubV1Point", "description": "Required. Start of the issue."}}, "type": "object"}, "GoogleCloudApihubV1RuntimeProjectAttachment": {"description": "Runtime project attachment represents an attachment from the runtime project to the host project. Api Hub looks for deployments in the attached runtime projects and creates corresponding resources in Api Hub for the discovered deployments.", "id": "GoogleCloudApihubV1RuntimeProjectAttachment", "properties": {"createTime": {"description": "Output only. Create time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Identifier. The resource name of a runtime project attachment. Format: \"projects/{project}/locations/{location}/runtimeProjectAttachments/{runtime_project_attachment}\".", "type": "string"}, "runtimeProject": {"description": "Required. Immutable. Google cloud project name in the format: \"projects/abc\" or \"projects/123\". As input, project name with either project id or number are accepted. As output, this field will contain project number.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1Schema": {"description": "The schema details derived from the spec. Currently, this entity is supported for OpenAPI spec only. For OpenAPI spec, this maps to the schema defined in the `definitions` section for OpenAPI 2.0 version and in `components.schemas` section for OpenAPI 3.0 and 3.1 version.", "id": "GoogleCloudApihubV1Schema", "properties": {"displayName": {"description": "Output only. The display name of the schema. This will map to the name of the schema in the spec.", "readOnly": true, "type": "string"}, "rawValue": {"description": "Output only. The raw value of the schema definition corresponding to the schema name in the spec.", "format": "byte", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1SearchResourcesRequest": {"description": "The SearchResources method's request.", "id": "GoogleCloudApihubV1SearchResourcesRequest", "properties": {"filter": {"description": "Optional. An expression that filters the list of search results. A filter expression consists of a field name, a comparison operator, and a value for filtering. The value must be a string, a number, or a boolean. The comparison operator must be `=`. Filters are not case sensitive. The following field names are eligible for filtering: * `resource_type` - The type of resource in the search results. Must be one of the following: `Api`, `ApiOperation`, `Deployment`, `Definition`, `Spec` or `Version`. This field can only be specified once in the filter. Here are is an example: * `resource_type = Api` - The resource_type is _Api_.", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of search results to return. The service may return fewer than this value. If unspecified at most 10 search results will be returned. If value is negative then `INVALID_ARGUMENT` error is returned. The maximum value is 25; values above 25 will be coerced to 25. While paginating, you can specify a new page size parameter for each page of search results to be listed.", "format": "int32", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous SearchResources call. Specify this parameter to retrieve the next page of transactions. When paginating, you must specify the `page_token` parameter and all the other parameters except page_size should be specified with the same value which was used in the previous call. If the other fields are set with a different value than the previous call then `INVALID_ARGUMENT` error is returned.", "type": "string"}, "query": {"description": "Required. The free text search query. This query can contain keywords which could be related to any detail of the API-Hub resources such display names, descriptions, attributes etc.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1SearchResourcesResponse": {"description": "Response for the SearchResources method.", "id": "GoogleCloudApihubV1SearchResourcesResponse", "properties": {"nextPageToken": {"description": "Pass this token in the SearchResourcesRequest to continue to list results. If all results have been returned, this field is an empty string or not present in the response.", "type": "string"}, "searchResults": {"description": "List of search results according to the filter and search query specified. The order of search results represents the ranking.", "items": {"$ref": "GoogleCloudApihubV1SearchResult"}, "type": "array"}}, "type": "object"}, "GoogleCloudApihubV1SearchResult": {"description": "Represents the search results.", "id": "GoogleCloudApihubV1SearchResult", "properties": {"resource": {"$ref": "GoogleCloudApihubV1ApiHubResource", "description": "This represents the ApiHubResource. Note: Only selected fields of the resources are populated in response."}}, "type": "object"}, "GoogleCloudApihubV1Secret": {"description": "Secret provides a reference to entries in Secret Manager.", "id": "GoogleCloudApihubV1Secret", "properties": {"secretVersion": {"description": "Required. The resource name of the secret version in the format, format as: `projects/*/secrets/*/versions/*`.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1SourceMetadata": {"description": "SourceMetadata represents the metadata for a resource at the source.", "id": "GoogleCloudApihubV1SourceMetadata", "properties": {"originalResourceCreateTime": {"description": "Output only. The time at which the resource was created at the source.", "format": "google-datetime", "readOnly": true, "type": "string"}, "originalResourceId": {"description": "Output only. The unique identifier of the resource at the source.", "readOnly": true, "type": "string"}, "originalResourceUpdateTime": {"description": "Output only. The time at which the resource was last updated at the source.", "format": "google-datetime", "readOnly": true, "type": "string"}, "pluginInstanceActionSource": {"$ref": "GoogleCloudApihubV1PluginInstanceActionSource", "description": "Output only. The source of the resource is a plugin instance action.", "readOnly": true}, "sourceType": {"description": "Output only. The type of the source.", "enum": ["SOURCE_TYPE_UNSPECIFIED", "PLUGIN"], "enumDescriptions": ["Source type not specified.", "Source type plugin."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1Spec": {"description": "Represents a spec associated with an API version in the API Hub. Note that specs of various types can be uploaded, however parsing of details is supported for OpenAPI spec currently.", "id": "GoogleCloudApihubV1Spec", "properties": {"attributes": {"additionalProperties": {"$ref": "GoogleCloudApihubV1AttributeValues"}, "description": "Optional. The list of user defined attributes associated with the spec. The key is the attribute name. It will be of the format: `projects/{project}/locations/{location}/attributes/{attribute}`. The value is the attribute values associated with the resource.", "type": "object"}, "contents": {"$ref": "GoogleCloudApihubV1SpecContents", "description": "Optional. Input only. The contents of the uploaded spec."}, "createTime": {"description": "Output only. The time at which the spec was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "details": {"$ref": "GoogleCloudApihubV1SpecDetails", "description": "Output only. Details parsed from the spec.", "readOnly": true}, "displayName": {"description": "Required. The display name of the spec. This can contain the file name of the spec.", "type": "string"}, "documentation": {"$ref": "GoogleCloudApihubV1Documentation", "description": "Optional. The documentation of the spec. For OpenAPI spec, this will be populated from `externalDocs` in OpenAPI spec."}, "lintResponse": {"$ref": "GoogleCloudApihubV1LintResponse", "description": "Optional. The lint response for the spec."}, "name": {"description": "Identifier. The name of the spec. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`", "type": "string"}, "parsingMode": {"description": "Optional. Input only. Enum specifying the parsing mode for OpenAPI Specification (OAS) parsing.", "enum": ["PARSING_MODE_UNSPECIFIED", "RELAXED", "STRICT"], "enumDescriptions": ["Defaults to `RELAXED`.", "Parsing of the Spec on create and update is relaxed, meaning that parsing errors the spec contents will not fail the API call.", "Parsing of the Spec on create and update is strict, meaning that parsing errors in the spec contents will fail the API call."], "type": "string"}, "sourceMetadata": {"description": "Output only. The list of sources and metadata from the sources of the spec.", "items": {"$ref": "GoogleCloudApihubV1SourceMetadata"}, "readOnly": true, "type": "array"}, "sourceUri": {"description": "Optional. The URI of the spec source in case file is uploaded from an external version control system.", "type": "string"}, "specType": {"$ref": "GoogleCloudApihubV1AttributeValues", "description": "Required. The type of spec. The value should be one of the allowed values defined for `projects/{project}/locations/{location}/attributes/system-spec-type` attribute. The number of values for this attribute will be based on the cardinality of the attribute. The same can be retrieved via GetAttribute API. Note, this field is mandatory if content is provided."}, "updateTime": {"description": "Output only. The time at which the spec was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1SpecContents": {"description": "The spec contents.", "id": "GoogleCloudApihubV1SpecContents", "properties": {"contents": {"description": "Required. The contents of the spec.", "format": "byte", "type": "string"}, "mimeType": {"description": "Required. The mime type of the content for example application/json, application/yaml, application/wsdl etc.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1SpecDetails": {"description": "SpecDetails contains the details parsed from supported spec types.", "id": "GoogleCloudApihubV1SpecDetails", "properties": {"description": {"description": "Output only. The description of the spec.", "readOnly": true, "type": "string"}, "openApiSpecDetails": {"$ref": "GoogleCloudApihubV1OpenApiSpecDetails", "description": "Output only. Additional details apart from `OperationDetails` parsed from an OpenAPI spec. The OperationDetails parsed from the spec can be obtained by using ListAPIOperations method.", "readOnly": true}}, "type": "object"}, "GoogleCloudApihubV1SpecMetadata": {"description": "The metadata associated with a spec of the API version.", "id": "GoogleCloudApihubV1SpecMetadata", "properties": {"originalCreateTime": {"description": "Optional. Timestamp indicating when the spec was created at the source.", "format": "google-datetime", "type": "string"}, "originalId": {"description": "Optional. The unique identifier of the spec in the system where it was originally created.", "type": "string"}, "originalUpdateTime": {"description": "Required. Timestamp indicating when the spec was last updated at the source.", "format": "google-datetime", "type": "string"}, "spec": {"$ref": "GoogleCloudApihubV1Spec", "description": "Required. The spec resource to be pushed to Hub's collect layer. The ID of the spec will be generated by Hub."}}, "type": "object"}, "GoogleCloudApihubV1StringAttributeValues": {"description": "The attribute values of data type string or JSON.", "id": "GoogleCloudApihubV1StringAttributeValues", "properties": {"values": {"description": "Required. The attribute values in case attribute data type is string or JSON.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudApihubV1StyleGuide": {"description": "Represents a singleton style guide resource to be used for linting Open API specs.", "id": "GoogleCloudApihubV1StyleGuide", "properties": {"contents": {"$ref": "GoogleCloudApihubV1StyleGuideContents", "description": "Required. Input only. The contents of the uploaded style guide."}, "linter": {"description": "Required. Target linter for the style guide.", "enum": ["LINTER_UNSPECIFIED", "SPECTRAL", "OTHER"], "enumDescriptions": ["Linter type unspecified.", "Linter type spectral.", "Linter type other."], "type": "string"}, "name": {"description": "Identifier. The name of the style guide. Format: `projects/{project}/locations/{location}/plugins/{plugin}/styleGuide`", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1StyleGuideContents": {"description": "The style guide contents.", "id": "GoogleCloudApihubV1StyleGuideContents", "properties": {"contents": {"description": "Required. The contents of the style guide.", "format": "byte", "type": "string"}, "mimeType": {"description": "Required. The mime type of the content.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1SummaryEntry": {"description": "Count of issues with a given severity.", "id": "GoogleCloudApihubV1SummaryEntry", "properties": {"count": {"description": "Required. Count of issues with the given severity.", "format": "int32", "type": "integer"}, "severity": {"description": "Required. Severity of the issue.", "enum": ["SEVERITY_UNSPECIFIED", "SEVERITY_ERROR", "SEVERITY_WARNING", "SEVERITY_INFO", "SEVERITY_HINT"], "enumDescriptions": ["Severity unspecified.", "Severity error.", "Severity warning.", "Severity info.", "Severity hint."], "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1UserPasswordConfig": {"description": "Parameters to support Username and Password Authentication.", "id": "GoogleCloudApihubV1UserPasswordConfig", "properties": {"password": {"$ref": "GoogleCloudApihubV1Secret", "description": "Required. Secret version reference containing the password. The `secretmanager.versions.access` permission should be granted to the service account accessing the secret."}, "username": {"description": "Required. Username.", "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1Version": {"description": "Represents a version of the API resource in API hub. This is also referred to as the API version.", "id": "GoogleCloudApihubV1Version", "properties": {"accreditation": {"$ref": "GoogleCloudApihubV1AttributeValues", "description": "Optional. The accreditations associated with the API version. This maps to the following system defined attribute: `projects/{project}/locations/{location}/attributes/system-accreditation` attribute. The number of values for this attribute will be based on the cardinality of the attribute. The same can be retrieved via GetAttribute API. All values should be from the list of allowed values defined for the attribute."}, "apiOperations": {"description": "Output only. The operations contained in the API version. These operations will be added to the version when a new spec is added or when an existing spec is updated. Format is `projects/{project}/locations/{location}/apis/{api}/versions/{version}/operations/{operation}`", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "attributes": {"additionalProperties": {"$ref": "GoogleCloudApihubV1AttributeValues"}, "description": "Optional. The list of user defined attributes associated with the Version resource. The key is the attribute name. It will be of the format: `projects/{project}/locations/{location}/attributes/{attribute}`. The value is the attribute values associated with the resource.", "type": "object"}, "compliance": {"$ref": "GoogleCloudApihubV1AttributeValues", "description": "Optional. The compliance associated with the API version. This maps to the following system defined attribute: `projects/{project}/locations/{location}/attributes/system-compliance` attribute. The number of values for this attribute will be based on the cardinality of the attribute. The same can be retrieved via GetAttribute API. All values should be from the list of allowed values defined for the attribute."}, "createTime": {"description": "Output only. The time at which the version was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "definitions": {"description": "Output only. The definitions contained in the API version. These definitions will be added to the version when a new spec is added or when an existing spec is updated. Format is `projects/{project}/locations/{location}/apis/{api}/versions/{version}/definitions/{definition}`", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "deployments": {"description": "Optional. The deployments linked to this API version. Note: A particular API version could be deployed to multiple deployments (for dev deployment, UAT deployment, etc) Format is `projects/{project}/locations/{location}/deployments/{deployment}`", "items": {"type": "string"}, "type": "array"}, "description": {"description": "Optional. The description of the version.", "type": "string"}, "displayName": {"description": "Required. The display name of the version.", "type": "string"}, "documentation": {"$ref": "GoogleCloudApihubV1Documentation", "description": "Optional. The documentation of the version."}, "lifecycle": {"$ref": "GoogleCloudApihubV1AttributeValues", "description": "Optional. The lifecycle of the API version. This maps to the following system defined attribute: `projects/{project}/locations/{location}/attributes/system-lifecycle` attribute. The number of values for this attribute will be based on the cardinality of the attribute. The same can be retrieved via GetAttribute API. All values should be from the list of allowed values defined for the attribute."}, "name": {"description": "Identifier. The name of the version. Format: `projects/{project}/locations/{location}/apis/{api}/versions/{version}`", "type": "string"}, "selectedDeployment": {"description": "Optional. The selected deployment for a Version resource. This can be used when special handling is needed on client side for a particular deployment linked to the version. Format is `projects/{project}/locations/{location}/deployments/{deployment}`", "type": "string"}, "sourceMetadata": {"description": "Output only. The list of sources and metadata from the sources of the version.", "items": {"$ref": "GoogleCloudApihubV1SourceMetadata"}, "readOnly": true, "type": "array"}, "specs": {"description": "Output only. The specs associated with this version. Note that an API version can be associated with multiple specs. Format is `projects/{project}/locations/{location}/apis/{api}/versions/{version}/specs/{spec}`", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "updateTime": {"description": "Output only. The time at which the version was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudApihubV1VersionMetadata": {"description": "The metadata associated with a version of the API resource.", "id": "GoogleCloudApihubV1VersionMetadata", "properties": {"deployments": {"description": "Optional. The deployments linked to this API version. Note: A particular API version could be deployed to multiple deployments (for dev deployment, UAT deployment, etc.)", "items": {"$ref": "GoogleCloudApihubV1DeploymentMetadata"}, "type": "array"}, "originalCreateTime": {"description": "Optional. Timestamp indicating when the version was created at the source.", "format": "google-datetime", "type": "string"}, "originalId": {"description": "Optional. The unique identifier of the version in the system where it was originally created.", "type": "string"}, "originalUpdateTime": {"description": "Required. Timestamp indicating when the version was last updated at the source.", "format": "google-datetime", "type": "string"}, "specs": {"description": "Optional. The specs associated with this version. Note that an API version can be associated with multiple specs.", "items": {"$ref": "GoogleCloudApihubV1SpecMetadata"}, "type": "array"}, "version": {"$ref": "GoogleCloudApihubV1Version", "description": "Required. Represents a version of the API resource in API hub. The ID of the version will be generated by Hub."}}, "type": "object"}, "GoogleCloudCommonOperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "GoogleCloudCommonOperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "cancelRequested": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have been cancelled successfully have google.longrunning.Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "statusDetail": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudLocationListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "GoogleCloudLocationListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "GoogleCloudLocationLocation"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "GoogleCloudLocationLocation": {"description": "A resource that represents a Google Cloud location.", "id": "GoogleCloudLocationLocation", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "GoogleLongrunningCancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "GoogleLongrunningCancelOperationRequest", "properties": {}, "type": "object"}, "GoogleLongrunningListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "GoogleLongrunningListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "GoogleLongrunningOperation"}, "type": "array"}}, "type": "object"}, "GoogleLongrunningOperation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "GoogleLongrunningOperation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "GoogleRpcStatus", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "GoogleRpcStatus": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "GoogleRpcStatus", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "API hub API", "version": "v1", "version_module": true}
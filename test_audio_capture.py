"""
Simple System Audio Capture Test
Test different methods to capture what's actually playing on your speakers
"""

import time
import numpy as np

def test_method_1_sounddevice():
    """Test Method 1: SoundDevice with loopback"""
    print("🎵 Testing Method 1: SoundDevice with Loopback")
    try:
        import sounddevice as sd
        
        print("Available audio devices:")
        print(sd.query_devices())
        
        # Try to find loopback device
        devices = sd.query_devices()
        loopback_device = None
        
        for i, device in enumerate(devices):
            device_name = str(device['name']).lower()
            if 'stereo mix' in device_name or 'what u hear' in device_name or 'loopback' in device_name:
                loopback_device = i
                print(f"Found loopback device: {device['name']}")
                break
        
        if loopback_device is None:
            print("❌ No loopback device found")
            return False
        
        # Record 3 seconds
        print("🎵 Recording 3 seconds from loopback...")
        duration = 3
        sample_rate = 44100
        
        audio_data = sd.rec(int(duration * sample_rate), 
                           samplerate=sample_rate, 
                           channels=2, 
                           device=loopback_device)
        sd.wait()
        
        # Analyze volume
        volume = np.sqrt(np.mean(audio_data**2))
        print(f"✅ Recorded! Volume level: {volume:.6f}")
        
        if volume > 0.001:
            print("🎉 SUCCESS: Audio detected!")
            return True
        else:
            print("❌ No audio detected")
            return False
            
    except Exception as e:
        print(f"❌ Method 1 failed: {e}")
        return False

def test_method_2_pyaudio():
    """Test Method 2: PyAudio with WASAPI loopback"""
    print("\n🎵 Testing Method 2: PyAudio with WASAPI")
    try:
        import pyaudio
        
        p = pyaudio.PyAudio()
        
        print("Available audio devices:")
        for i in range(p.get_device_count()):
            try:
                info = p.get_device_info_by_index(i)
                print(f"Device {i}: {info['name']} - Inputs: {info['maxInputChannels']}")
            except:
                continue
        
        # Try to find stereo mix or loopback
        loopback_device = None
        for i in range(p.get_device_count()):
            try:
                info = p.get_device_info_by_index(i)
                device_name = str(info['name']).lower()
                if ('stereo mix' in device_name or 'what u hear' in device_name or 
                    'loopback' in device_name) and info['maxInputChannels'] > 0:
                    loopback_device = i
                    print(f"Found loopback device: {info['name']}")
                    break
            except:
                continue
        
        if loopback_device is None:
            print("❌ No loopback device found, trying default input...")
            loopback_device = p.get_default_input_device_info()['index']
        
        # Record 3 seconds
        print("🎵 Recording 3 seconds...")
        CHUNK = 1024
        FORMAT = pyaudio.paInt16
        CHANNELS = 2
        RATE = 44100
        RECORD_SECONDS = 3
        
        stream = p.open(format=FORMAT,
                       channels=CHANNELS,
                       rate=RATE,
                       input=True,
                       input_device_index=loopback_device,
                       frames_per_buffer=CHUNK)
        
        frames = []
        for _ in range(0, int(RATE / CHUNK * RECORD_SECONDS)):
            data = stream.read(CHUNK, exception_on_overflow=False)
            frames.append(data)
        
        stream.stop_stream()
        stream.close()
        p.terminate()
        
        # Convert to numpy and analyze
        audio_data = np.frombuffer(b''.join(frames), dtype=np.int16)
        volume = np.sqrt(np.mean(audio_data.astype(float)**2))
        print(f"✅ Recorded! Volume level: {volume:.2f}")
        
        if volume > 100:
            print("🎉 SUCCESS: Audio detected!")
            return True
        else:
            print("❌ No significant audio detected")
            return False
            
    except Exception as e:
        print(f"❌ Method 2 failed: {e}")
        return False

def test_method_3_windows_wasapi():
    """Test Method 3: Windows WASAPI directly"""
    print("\n🎵 Testing Method 3: Windows WASAPI Direct")
    try:
        import subprocess
        import os
        
        # Try using Windows Sound Recorder via command line
        print("Attempting to use Windows built-in audio capture...")
        
        # This is a placeholder - Windows WASAPI requires more complex setup
        print("❌ Method 3 requires additional Windows-specific libraries")
        return False
        
    except Exception as e:
        print(f"❌ Method 3 failed: {e}")
        return False

def test_method_4_pycaw():
    """Test Method 4: PyCaw for Windows audio"""
    print("\n🎵 Testing Method 4: PyCaw (Windows Audio)")
    try:
        # This would require: pip install pycaw
        print("Testing PyCaw for Windows audio capture...")
        print("❌ PyCaw requires additional setup")
        return False
        
    except Exception as e:
        print(f"❌ Method 4 failed: {e}")
        return False

def main():
    print("🎯 SYSTEM AUDIO CAPTURE TEST")
    print("=" * 50)
    print("🔊 IMPORTANT: Make sure you have audio playing!")
    print("   - Play a YouTube video")
    print("   - Play music")
    print("   - Have any sound playing on your system")
    print("=" * 50)
    
    input("Press Enter when you have audio playing...")
    
    methods = [
        test_method_1_sounddevice,
        test_method_2_pyaudio,
        test_method_3_windows_wasapi,
        test_method_4_pycaw
    ]
    
    successful_methods = []
    
    for method in methods:
        try:
            if method():
                successful_methods.append(method.__name__)
        except Exception as e:
            print(f"❌ {method.__name__} crashed: {e}")
        
        print("-" * 30)
    
    print("\n🎯 RESULTS:")
    print("=" * 50)
    if successful_methods:
        print("✅ Successful methods:")
        for method in successful_methods:
            print(f"   • {method}")
        print("\n🎉 We found working audio capture methods!")
    else:
        print("❌ No methods successfully captured system audio")
        print("\n🔧 TROUBLESHOOTING:")
        print("1. Enable 'Stereo Mix' in Windows Sound settings")
        print("2. Check if audio is actually playing")
        print("3. Try running as administrator")
        print("4. Install additional audio drivers")

if __name__ == "__main__":
    main()

{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}, "https://www.googleapis.com/auth/cloud-platform.read-only": {"description": "View your data across Google Cloud services and see the email address of your Google Account"}, "https://www.googleapis.com/auth/firebase": {"description": "View and administer all your Firebase data and settings"}, "https://www.googleapis.com/auth/firebase.readonly": {"description": "View all your Firebase data and settings"}}}}, "basePath": "", "baseUrl": "https://firebasedatabase.googleapis.com/", "batchPath": "batch", "canonicalName": "Firebase Realtime Database", "description": "The Firebase Realtime Database API enables programmatic provisioning and management of Realtime Database instances.", "discoveryVersion": "v1", "documentationLink": "https://firebase.google.com/docs/reference/rest/database/database-management/rest/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "firebasedatabase:v1beta", "kind": "discovery#restDescription", "mtlsRootUrl": "https://firebasedatabase.mtls.googleapis.com/", "name": "firebasedatabase", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"resources": {"instances": {"methods": {"create": {"description": "Requests that a new DatabaseInstance be created. The state of a successfully created DatabaseInstance is ACTIVE. Only available for projects on the Blaze plan. Projects can be upgraded using the Cloud Billing API https://cloud.google.com/billing/reference/rest/v1/projects/updateBillingInfo. Note that it might take a few minutes for billing enablement state to propagate to Firebase systems.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/instances", "httpMethod": "POST", "id": "firebasedatabase.projects.locations.instances.create", "parameterOrder": ["parent"], "parameters": {"databaseId": {"description": "The globally unique identifier of the database instance.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent project for which to create a database instance, in the form: `projects/{project-number}/locations/{location-id}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "When set to true, the request will be validated but not submitted.", "location": "query", "type": "boolean"}}, "path": "v1beta/{+parent}/instances", "request": {"$ref": "DatabaseInstance"}, "response": {"$ref": "DatabaseInstance"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "delete": {"description": "Marks a DatabaseInstance to be deleted. The DatabaseInstance will be set to the DELETED state for 20 days, and will be purged within 30 days. The default database cannot be deleted. IDs for deleted database instances may never be recovered or re-used. The Database may only be deleted if it is already in a DISABLED state.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}", "httpMethod": "DELETE", "id": "firebasedatabase.projects.locations.instances.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified resource name of the database instance, in the form: `projects/{project-number}/locations/{location-id}/instances/{database-id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "DatabaseInstance"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "disable": {"description": "Disables a DatabaseInstance. The database can be re-enabled later using ReenableDatabaseInstance. When a database is disabled, all reads and writes are denied, including view access in the Firebase console.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:disable", "httpMethod": "POST", "id": "firebasedatabase.projects.locations.instances.disable", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified resource name of the database instance, in the form: `projects/{project-number}/locations/{location-id}/instances/{database-id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}:disable", "request": {"$ref": "DisableDatabaseInstanceRequest"}, "response": {"$ref": "DatabaseInstance"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "get": {"description": "Gets the DatabaseInstance identified by the specified resource name.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}", "httpMethod": "GET", "id": "firebasedatabase.projects.locations.instances.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified resource name of the database instance, in the form: `projects/{project-number}/locations/{location-id}/instances/{database-id}`. `database-id` is a globally unique identifier across all parent collections. For convenience, this method allows you to supply `-` as a wildcard character in place of specific collections under `projects` and `locations`. The resulting wildcarding form of the method is: `projects/-/locations/-/instances/{database-id}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "DatabaseInstance"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "list": {"description": "Lists each DatabaseInstance associated with the specified parent project. The list items are returned in no particular order, but will be a consistent view of the database instances when additional requests are made with a `pageToken`. The resulting list contains instances in any STATE. The list results may be stale by a few seconds. Use GetDatabaseInstance for consistent reads.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/instances", "httpMethod": "GET", "id": "firebasedatabase.projects.locations.instances.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of database instances to return in the response. The server may return fewer than this at its discretion. If no value is specified (or too large a value is specified), then the server will impose its own limit.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "<PERSON><PERSON> returned from a previous call to `ListDatabaseInstances` indicating where in the set of database instances to resume listing.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent project for which to list database instances, in the form: `projects/{project-number}/locations/{location-id}` To list across all locations, use a parent in the form: `projects/{project-number}/locations/-`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "Indicate that DatabaseInstances in the `DELETED` state should also be returned.", "location": "query", "type": "boolean"}}, "path": "v1beta/{+parent}/instances", "response": {"$ref": "ListDatabaseInstancesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "reenable": {"description": "Enables a DatabaseInstance. The database must have been disabled previously using DisableDatabaseInstance. The state of a successfully reenabled DatabaseInstance is ACTIVE.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:reenable", "httpMethod": "POST", "id": "firebasedatabase.projects.locations.instances.reenable", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified resource name of the database instance, in the form: `projects/{project-number}/locations/{location-id}/instances/{database-id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}:reenable", "request": {"$ref": "ReenableDatabaseInstanceRequest"}, "response": {"$ref": "DatabaseInstance"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "undelete": {"description": "Restores a DatabaseInstance that was previously marked to be deleted. After the delete method is used, DatabaseInstances are set to the DELETED state for 20 days, and will be purged within 30 days. Databases in the DELETED state can be undeleted without losing any data. This method may only be used on a DatabaseInstance in the DELETED state. Purged DatabaseInstances may not be recovered.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:undelete", "httpMethod": "POST", "id": "firebasedatabase.projects.locations.instances.undelete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified resource name of the database instance, in the form: `projects/{project-number}/locations/{location-id}/instances/{database-id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}:undelete", "request": {"$ref": "UndeleteDatabaseInstanceRequest"}, "response": {"$ref": "DatabaseInstance"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}}}}}}}}, "revision": "20241202", "rootUrl": "https://firebasedatabase.googleapis.com/", "schemas": {"DatabaseInstance": {"description": "Representation of a Realtime Database instance. Details on interacting with contents of a DatabaseInstance can be found at: https://firebase.google.com/docs/database/rest/start.", "id": "DatabaseInstance", "properties": {"databaseUrl": {"description": "Output only. Output Only. The globally unique hostname of the database.", "readOnly": true, "type": "string"}, "name": {"description": "The fully qualified resource name of the database instance, in the form: `projects/{project-number}/locations/{location-id}/instances/{database-id}`.", "type": "string"}, "project": {"description": "Output only. The resource name of the project this instance belongs to. For example: `projects/{project-number}`.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The database's lifecycle state. Read-only.", "enum": ["LIFECYCLE_STATE_UNSPECIFIED", "ACTIVE", "DISABLED", "DELETED"], "enumDescriptions": ["Unspecified state, likely the result of an error on the backend. This is only used for distinguishing unset values.", "The normal and active state.", "The database is in a disabled state. It can be re-enabled later.", "The database is in a deleted state."], "readOnly": true, "type": "string"}, "type": {"description": "Immutable. The database instance type. On creation only USER_DATABASE is allowed, which is also the default when omitted.", "enum": ["DATABASE_INSTANCE_TYPE_UNSPECIFIED", "DEFAULT_DATABASE", "USER_DATABASE"], "enumDescriptions": ["Unknown state, likely the result of an error on the backend. This is only used for distinguishing unset values.", "The default database that is provisioned when a project is created.", "A database that the user created."], "type": "string"}}, "type": "object"}, "DisableDatabaseInstanceRequest": {"description": "The request sent to the DisableDatabaseInstance method.", "id": "DisableDatabaseInstanceRequest", "properties": {}, "type": "object"}, "ListDatabaseInstancesResponse": {"description": "The response from the ListDatabaseInstances method.", "id": "ListDatabaseInstancesResponse", "properties": {"instances": {"description": "List of each DatabaseInstance that is in the parent Firebase project.", "items": {"$ref": "DatabaseInstance"}, "type": "array"}, "nextPageToken": {"description": "If the result list is too large to fit in a single response, then a token is returned. If the string is empty, then this response is the last page of results. This token can be used in a subsequent call to `ListDatabaseInstances` to find the next group of database instances. Page tokens are short-lived and should not be persisted.", "type": "string"}}, "type": "object"}, "ReenableDatabaseInstanceRequest": {"description": "The request sent to the ReenableDatabaseInstance method.", "id": "ReenableDatabaseInstanceRequest", "properties": {}, "type": "object"}, "UndeleteDatabaseInstanceRequest": {"description": "The request sent to UndeleteDatabaseInstance method.", "id": "UndeleteDatabaseInstanceRequest", "properties": {}, "type": "object"}}, "servicePath": "", "title": "Firebase Realtime Database Management API", "version": "v1beta", "version_module": true}
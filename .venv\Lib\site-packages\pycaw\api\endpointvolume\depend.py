from ctypes import <PERSON><PERSON>IN<PERSON><PERSON>, Structure, c_float
from ctypes.wintypes import <PERSON><PERSON><PERSON>, UINT

from comtypes import GUID


class AUDIO_VOLUME_NOTIFICATION_DATA(Structure):
    _fields_ = [
        ("guidEventContext", GUID),
        ("bMuted", BOOL),
        ("fMasterVolume", c_float),
        ("nChannels", UINT),
        ("afChannelVolumes", c_float * 8),
    ]


PAUDIO_VOLUME_NOTIFICATION_DATA = POINTER(AUDIO_VOLUME_NOTIFICATION_DATA)

"""
Test Properly Enabled Stereo Mix
After enabling Stereo Mix in Windows Sound settings
"""

import time
import numpy as np

def test_enabled_stereo_mix():
    """Test Stereo Mix after proper Windows configuration"""
    print("🎵 Testing Properly Enabled Stereo Mix")
    
    try:
        import sounddevice as sd
        
        # Find Stereo Mix
        devices = sd.query_devices()
        stereo_mix_device = None
        
        for i, device in enumerate(devices):
            if 'Stereo Mix' in str(device['name']) and device['max_input_channels'] > 0:
                stereo_mix_device = i
                print(f"✅ Found Stereo Mix: Device {i}")
                print(f"   Name: {device['name']}")
                break
        
        if stereo_mix_device is None:
            print("❌ Stereo Mix device not found")
            return False
        
        print("🎵 Recording 5 seconds from Stereo Mix...")
        print("   (Make sure audio is playing LOUDLY!)")
        
        # Record with optimal settings
        duration = 5
        sample_rate = 44100
        channels = 2
        
        audio_data = sd.rec(int(duration * sample_rate), 
                           samplerate=sample_rate, 
                           channels=channels, 
                           device=stereo_mix_device,
                           dtype='float32')
        
        # Show countdown
        for i in range(5, 0, -1):
            print(f"   Recording... {i}")
            time.sleep(1)
        
        sd.wait()
        
        # Detailed analysis
        volume_rms = np.sqrt(np.mean(audio_data**2))
        volume_max = np.max(np.abs(audio_data))
        volume_avg = np.mean(np.abs(audio_data))
        
        print(f"\n📊 DETAILED AUDIO ANALYSIS:")
        print(f"   RMS Volume: {volume_rms:.6f}")
        print(f"   Max Volume: {volume_max:.6f}")
        print(f"   Avg Volume: {volume_avg:.6f}")
        
        # Check different thresholds
        if volume_rms > 0.1:
            print("🎉 EXCELLENT: Strong audio signal detected!")
            return True
        elif volume_rms > 0.01:
            print("✅ GOOD: Moderate audio signal detected!")
            return True
        elif volume_rms > 0.001:
            print("⚠️ WEAK: Low audio signal (may need volume boost)")
            return True
        elif volume_rms > 0.0001:
            print("❌ VERY WEAK: Audio barely detectable")
            return False
        else:
            print("❌ NO AUDIO: Stereo Mix not working properly")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def create_working_audio_capture():
    """Create a working audio capture function"""
    print("\n🔧 Creating Working Audio Capture Function...")
    
    code = '''
def capture_system_audio(duration=3):
    """Capture system audio using Stereo Mix"""
    import sounddevice as sd
    import numpy as np
    
    # Use Stereo Mix device (adjust device number if needed)
    stereo_mix_device = 4  # From our tests
    
    try:
        # Record audio
        audio_data = sd.rec(int(duration * 44100), 
                           samplerate=44100, 
                           channels=2, 
                           device=stereo_mix_device,
                           dtype='float32')
        sd.wait()
        
        # Analyze volume
        volume = np.sqrt(np.mean(audio_data**2))
        
        if volume > 0.001:
            return f"🔊 Audio detected! Volume: {volume:.4f}"
        else:
            return "🔇 No audio detected"
            
    except Exception as e:
        return f"❌ Error: {e}"

# Test the function
print(capture_system_audio())
'''
    
    print("📝 Working code template:")
    print(code)
    
    return code

def main():
    print("🎯 TESTING PROPERLY ENABLED STEREO MIX")
    print("=" * 60)
    print("🔧 BEFORE RUNNING THIS TEST:")
    print("   1. Follow the Stereo Mix enabling steps")
    print("   2. Set Stereo Mix as default recording device")
    print("   3. Set Stereo Mix volume to 100%")
    print("   4. Play LOUD audio (YouTube, music, etc.)")
    print("=" * 60)
    
    input("Press Enter ONLY after enabling Stereo Mix properly...")
    
    if test_enabled_stereo_mix():
        print("\n🎉 SUCCESS! Stereo Mix is working!")
        create_working_audio_capture()
        print("\n✅ Ready to integrate with your screen monitoring system!")
    else:
        print("\n❌ Stereo Mix still not working properly")
        print("\n🔧 ADDITIONAL TROUBLESHOOTING:")
        print("1. Try running this script as Administrator")
        print("2. Check Windows Privacy Settings → Microphone → Allow apps to access")
        print("3. Restart your computer after enabling Stereo Mix")
        print("4. Try a different audio player (VLC, Windows Media Player)")
        print("5. Check if your audio driver supports Stereo Mix")

if __name__ == "__main__":
    main()

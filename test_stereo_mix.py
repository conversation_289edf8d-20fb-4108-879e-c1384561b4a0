"""
Stereo Mix Configuration Test
Try different sample rates and configurations to get Stereo Mix working
"""

import time
import numpy as np

def test_stereo_mix_configurations():
    """Test Stereo Mix with different configurations"""
    print("🎵 Testing Stereo Mix with Different Configurations")
    
    try:
        import sounddevice as sd
        
        # Find Stereo Mix device
        devices = sd.query_devices()
        stereo_mix_device = None
        
        for i, device in enumerate(devices):
            if 'Stereo Mix' in str(device['name']) and device['max_input_channels'] > 0:
                stereo_mix_device = i
                print(f"✅ Found Stereo Mix: Device {i}")
                print(f"   Name: {device['name']}")
                print(f"   Channels: {device['max_input_channels']}")
                print(f"   Default Sample Rate: {device['default_samplerate']}")
                break
        
        if stereo_mix_device is None:
            print("❌ Stereo Mix device not found")
            return False
        
        # Get device info
        device_info = sd.query_devices(stereo_mix_device)
        default_sr = int(device_info['default_samplerate'])
        max_channels = int(device_info['max_input_channels'])
        
        # Try different sample rates
        sample_rates = [default_sr, 44100, 48000, 22050, 16000, 8000]
        channels_to_try = [min(2, max_channels), 1]
        
        print(f"\n🔧 Testing different configurations...")
        
        for sr in sample_rates:
            for channels in channels_to_try:
                try:
                    print(f"   Testing: {sr}Hz, {channels} channel(s)...")
                    
                    # Try recording 2 seconds
                    duration = 2
                    audio_data = sd.rec(int(duration * sr), 
                                       samplerate=sr, 
                                       channels=channels, 
                                       device=stereo_mix_device,
                                       dtype='float32')
                    sd.wait()
                    
                    # Analyze volume
                    volume = np.sqrt(np.mean(audio_data**2))
                    max_vol = np.max(np.abs(audio_data))
                    
                    print(f"      ✅ SUCCESS! Volume: {volume:.6f}, Max: {max_vol:.6f}")
                    
                    if volume > 0.001:
                        print(f"🎉 AUDIO DETECTED with {sr}Hz, {channels} channels!")
                        return (stereo_mix_device, sr, channels)
                    else:
                        print(f"      ⚠️ No audio (very quiet)")
                    
                except Exception as e:
                    print(f"      ❌ Failed: {str(e)[:50]}...")
        
        return False
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_pyaudio_stereo_mix():
    """Test PyAudio with Stereo Mix"""
    print("\n🎵 Testing PyAudio with Stereo Mix")
    
    try:
        import pyaudio
        
        p = pyaudio.PyAudio()
        
        # Find Stereo Mix
        stereo_mix_device = None
        for i in range(p.get_device_count()):
            try:
                info = p.get_device_info_by_index(i)
                if 'Stereo Mix' in str(info['name']) and info['maxInputChannels'] > 0:
                    stereo_mix_device = i
                    print(f"✅ Found Stereo Mix: Device {i}")
                    print(f"   Name: {info['name']}")
                    print(f"   Max Channels: {info['maxInputChannels']}")
                    print(f"   Default Sample Rate: {info['defaultSampleRate']}")
                    break
            except:
                continue
        
        if stereo_mix_device is None:
            print("❌ Stereo Mix not found in PyAudio")
            p.terminate()
            return False
        
        # Get device info
        device_info = p.get_device_info_by_index(stereo_mix_device)
        default_sr = int(device_info['defaultSampleRate'])
        
        # Try different configurations
        sample_rates = [default_sr, 44100, 48000, 22050]
        formats = [pyaudio.paFloat32, pyaudio.paInt16]
        
        for sr in sample_rates:
            for fmt in formats:
                try:
                    fmt_name = "Float32" if fmt == pyaudio.paFloat32 else "Int16"
                    print(f"   Testing PyAudio: {sr}Hz, {fmt_name}...")
                    
                    CHUNK = 1024
                    CHANNELS = 2
                    RECORD_SECONDS = 2
                    
                    stream = p.open(format=fmt,
                                   channels=CHANNELS,
                                   rate=sr,
                                   input=True,
                                   input_device_index=stereo_mix_device,
                                   frames_per_buffer=CHUNK)
                    
                    frames = []
                    for _ in range(0, int(sr / CHUNK * RECORD_SECONDS)):
                        data = stream.read(CHUNK, exception_on_overflow=False)
                        frames.append(data)
                    
                    stream.stop_stream()
                    stream.close()
                    
                    # Analyze
                    if fmt == pyaudio.paFloat32:
                        audio_data = np.frombuffer(b''.join(frames), dtype=np.float32)
                    else:
                        audio_data = np.frombuffer(b''.join(frames), dtype=np.int16).astype(float)
                    
                    volume = np.sqrt(np.mean(audio_data**2))
                    print(f"      ✅ SUCCESS! Volume: {volume:.2f}")
                    
                    if volume > 100:  # Adjust threshold for int16
                        print(f"🎉 AUDIO DETECTED with PyAudio {sr}Hz, {fmt_name}!")
                        p.terminate()
                        return (stereo_mix_device, sr, fmt)
                    
                except Exception as e:
                    print(f"      ❌ Failed: {str(e)[:50]}...")
        
        p.terminate()
        return False
        
    except Exception as e:
        print(f"❌ PyAudio test failed: {e}")
        return False

def enable_stereo_mix_instructions():
    """Show instructions to enable Stereo Mix"""
    print("\n🔧 HOW TO ENABLE STEREO MIX:")
    print("=" * 50)
    print("1. Right-click the speaker icon in system tray")
    print("2. Click 'Open Sound settings'")
    print("3. Scroll down and click 'Sound Control Panel'")
    print("4. Go to 'Recording' tab")
    print("5. Right-click in empty space → 'Show Disabled Devices'")
    print("6. You should see 'Stereo Mix' appear")
    print("7. Right-click 'Stereo Mix' → 'Enable'")
    print("8. Right-click 'Stereo Mix' → 'Set as Default Device'")
    print("9. Click 'OK' and try again")
    print("=" * 50)

def main():
    print("🎯 STEREO MIX CONFIGURATION TEST")
    print("=" * 60)
    print("🔊 Make sure you have LOUD audio playing!")
    print("   - YouTube video at high volume")
    print("   - Music playing loudly")
    print("   - Any system audio that you can hear clearly")
    print("=" * 60)
    
    input("Press Enter when audio is playing loudly...")
    
    # Test SoundDevice configurations
    sd_result = test_stereo_mix_configurations()
    
    # Test PyAudio configurations  
    pa_result = test_pyaudio_stereo_mix()
    
    print("\n" + "=" * 60)
    print("🎯 FINAL RESULTS:")
    
    if sd_result:
        device, sr, channels = sd_result
        print(f"✅ SoundDevice SUCCESS!")
        print(f"   Device: {device}, Sample Rate: {sr}, Channels: {channels}")
        
        # Create a simple working example
        print("\n📝 Working SoundDevice code:")
        print(f"import sounddevice as sd")
        print(f"import numpy as np")
        print(f"audio = sd.rec(44100, samplerate={sr}, channels={channels}, device={device})")
        print(f"sd.wait()")
        print(f"volume = np.sqrt(np.mean(audio**2))")
        
    elif pa_result:
        device, sr, fmt = pa_result
        print(f"✅ PyAudio SUCCESS!")
        print(f"   Device: {device}, Sample Rate: {sr}, Format: {fmt}")
        
    else:
        print("❌ No working configuration found")
        enable_stereo_mix_instructions()
        print("\n💡 ALTERNATIVE: Try running as Administrator")
        print("💡 ALTERNATIVE: Check Windows Privacy settings for microphone access")

if __name__ == "__main__":
    main()

{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/bigquery": {"description": "View and manage your data in Google BigQuery and see the email address for your Google Account"}, "https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://bigqueryconnection.googleapis.com/", "batchPath": "batch", "canonicalName": "BigQuery Connection Service", "description": "Allows users to manage BigQuery connections to external data sources.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/bigquery/docs/connections-api-intro", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "bigqueryconnection:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://bigqueryconnection.mtls.googleapis.com/", "name": "bigqueryconnection", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"resources": {"connections": {"methods": {"create": {"description": "Creates a new connection.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/connections", "httpMethod": "POST", "id": "bigqueryconnection.projects.locations.connections.create", "parameterOrder": ["parent"], "parameters": {"connectionId": {"description": "Optional. Connection id that should be assigned to the created connection.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent resource name. Must be in the format `projects/{project_id}/locations/{location_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/connections", "request": {"$ref": "Connection"}, "response": {"$ref": "Connection"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes connection and associated credential.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}", "httpMethod": "DELETE", "id": "bigqueryconnection.projects.locations.connections.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the deleted connection, for example: `projects/{project_id}/locations/{location_id}/connections/{connection_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Returns specified connection.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}", "httpMethod": "GET", "id": "bigqueryconnection.projects.locations.connections.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the requested connection, for example: `projects/{project_id}/locations/{location_id}/connections/{connection_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Connection"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}:getIamPolicy", "httpMethod": "POST", "id": "bigqueryconnection.projects.locations.connections.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Returns a list of connections in the given project.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/connections", "httpMethod": "GET", "id": "bigqueryconnection.projects.locations.connections.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Required. Page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent resource name. Must be in the form: `projects/{project_id}/locations/{location_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/connections", "response": {"$ref": "ListConnectionsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the specified connection. For security reasons, also resets credential if connection properties are in the update field mask.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}", "httpMethod": "PATCH", "id": "bigqueryconnection.projects.locations.connections.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the connection to update, for example: `projects/{project_id}/locations/{location_id}/connections/{connection_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Update mask for the connection fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Connection"}, "response": {"$ref": "Connection"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}:setIamPolicy", "httpMethod": "POST", "id": "bigqueryconnection.projects.locations.connections.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}:testIamPermissions", "httpMethod": "POST", "id": "bigqueryconnection.projects.locations.connections.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20240727", "rootUrl": "https://bigqueryconnection.googleapis.com/", "schemas": {"AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "AwsAccessRole": {"description": "Authentication method for Amazon Web Services (AWS) that uses Google owned Google service account to assume into customer's AWS IAM Role.", "id": "AwsAccessRole", "properties": {"iamRoleId": {"description": "The user’s AWS IAM Role that trusts the Google-owned AWS IAM user Connection.", "type": "string"}, "identity": {"description": "A unique Google-owned and Google-generated identity for the Connection. This identity will be used to access the user's AWS IAM Role.", "type": "string"}}, "type": "object"}, "AwsProperties": {"description": "Connection properties specific to Amazon Web Services (AWS).", "id": "AwsProperties", "properties": {"accessRole": {"$ref": "AwsAccessRole", "description": "Authentication using Google owned service account to assume into customer's AWS IAM Role."}}, "type": "object"}, "AzureProperties": {"description": "Container for connection properties specific to Azure.", "id": "AzureProperties", "properties": {"application": {"description": "Output only. The name of the Azure Active Directory Application.", "readOnly": true, "type": "string"}, "clientId": {"description": "Output only. The client id of the Azure Active Directory Application.", "readOnly": true, "type": "string"}, "customerTenantId": {"description": "The id of customer's directory that host the data.", "type": "string"}, "federatedApplicationClientId": {"description": "The client ID of the user's Azure Active Directory Application used for a federated connection.", "type": "string"}, "identity": {"description": "Output only. A unique Google-owned and Google-generated identity for the Connection. This identity will be used to access the user's Azure Active Directory Application.", "readOnly": true, "type": "string"}, "objectId": {"description": "Output only. The object id of the Azure Active Directory Application.", "readOnly": true, "type": "string"}, "redirectUri": {"description": "The URL user will be redirected to after granting consent during connection setup.", "type": "string"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "CloudResourceProperties": {"description": "Container for connection properties for delegation of access to GCP resources.", "id": "CloudResourceProperties", "properties": {"serviceAccountId": {"description": "Output only. The account ID of the service created for the purpose of this connection. The service account does not have any permissions associated with it when it is created. After creation, customers delegate permissions to the service account. When the connection is used in the context of an operation in BigQuery, the service account will be used to connect to the desired resources in GCP. The account ID is in the form of: @gcp-sa-bigquery-cloudresource.iam.gserviceaccount.com", "readOnly": true, "type": "string"}}, "type": "object"}, "CloudSpannerProperties": {"description": "Connection properties specific to Cloud Spanner.", "id": "CloudSpannerProperties", "properties": {"database": {"description": "Cloud Spanner database in the form `project/instance/database'", "type": "string"}, "databaseRole": {"description": "Optional. Cloud Spanner database role for fine-grained access control. The Cloud Spanner admin should have provisioned the database role with appropriate permissions, such as `SELECT` and `INSERT`. Other users should only use roles provided by their Cloud Spanner admins. For more details, see [About fine-grained access control] (https://cloud.google.com/spanner/docs/fgac-about). REQUIRES: The database role name must start with a letter, and can only contain letters, numbers, and underscores.", "type": "string"}, "maxParallelism": {"description": "Allows setting max parallelism per query when executing on Spanner independent compute resources. If unspecified, default values of parallelism are chosen that are dependent on the Cloud Spanner instance configuration. REQUIRES: `use_parallelism` must be set. REQUIRES: `use_data_boost` must be set.", "format": "int32", "type": "integer"}, "useDataBoost": {"description": "If set, the request will be executed via Spanner independent compute resources. REQUIRES: `use_parallelism` must be set.", "type": "boolean"}, "useParallelism": {"description": "If parallelism should be used when reading from <PERSON> Spanner", "type": "boolean"}, "useServerlessAnalytics": {"deprecated": true, "description": "Deprecated: prefer use_data_boost instead. If the serverless analytics service should be used to read data from Cloud Spanner. Note: `use_parallelism` must be set when using serverless analytics.", "type": "boolean"}}, "type": "object"}, "CloudSqlCredential": {"description": "Credential info for the Cloud SQL.", "id": "CloudSqlCredential", "properties": {"password": {"description": "The password for the credential.", "type": "string"}, "username": {"description": "The username for the credential.", "type": "string"}}, "type": "object"}, "CloudSqlProperties": {"description": "Connection properties specific to the Cloud SQL.", "id": "CloudSqlProperties", "properties": {"credential": {"$ref": "CloudSqlCredential", "description": "Input only. Cloud SQL credential."}, "database": {"description": "Database name.", "type": "string"}, "instanceId": {"description": "Cloud SQL instance ID in the form `project:location:instance`.", "type": "string"}, "serviceAccountId": {"description": "Output only. The account ID of the service used for the purpose of this connection. When the connection is used in the context of an operation in BigQuery, this service account will serve as the identity being used for connecting to the CloudSQL instance specified in this connection.", "readOnly": true, "type": "string"}, "type": {"description": "Type of the Cloud SQL database.", "enum": ["DATABASE_TYPE_UNSPECIFIED", "POSTGRES", "MYSQL"], "enumDescriptions": ["Unspecified database type.", "Cloud SQL for PostgreSQL.", "Cloud SQL for MySQL."], "type": "string"}}, "type": "object"}, "Connection": {"description": "Configuration parameters to establish connection with an external data source, except the credential attributes.", "id": "Connection", "properties": {"aws": {"$ref": "AwsProperties", "description": "Amazon Web Services (AWS) properties."}, "azure": {"$ref": "AzureProperties", "description": "Azure properties."}, "cloudResource": {"$ref": "CloudResourceProperties", "description": "Cloud Resource properties."}, "cloudSpanner": {"$ref": "CloudSpannerProperties", "description": "Cloud Spanner properties."}, "cloudSql": {"$ref": "CloudSqlProperties", "description": "Cloud SQL properties."}, "configuration": {"$ref": "ConnectorConfiguration", "description": "Optional. Connector configuration."}, "creationTime": {"description": "Output only. The creation timestamp of the connection.", "format": "int64", "readOnly": true, "type": "string"}, "description": {"description": "User provided description.", "type": "string"}, "friendlyName": {"description": "User provided display name for the connection.", "type": "string"}, "hasCredential": {"description": "Output only. True, if credential is configured for this connection.", "readOnly": true, "type": "boolean"}, "kmsKeyName": {"description": "Optional. The Cloud KMS key that is used for credentials encryption. If omitted, internal Google owned encryption keys are used. Example: `projects/[kms_project_id]/locations/[region]/keyRings/[key_region]/cryptoKeys/[key]`", "type": "string"}, "lastModifiedTime": {"description": "Output only. The last update timestamp of the connection.", "format": "int64", "readOnly": true, "type": "string"}, "name": {"description": "Output only. The resource name of the connection in the form of: `projects/{project_id}/locations/{location_id}/connections/{connection_id}`", "readOnly": true, "type": "string"}, "salesforceDataCloud": {"$ref": "SalesforceDataCloudProperties", "description": "Optional. Salesforce DataCloud properties. This field is intended for use only by Salesforce partner projects. This field contains properties for your Salesforce DataCloud connection."}, "spark": {"$ref": "SparkProperties", "description": "Spark properties."}}, "type": "object"}, "ConnectorConfiguration": {"description": "Represents concrete parameter values for Connector Configuration.", "id": "ConnectorConfiguration", "properties": {"asset": {"$ref": "ConnectorConfigurationAsset", "description": "Data asset."}, "authentication": {"$ref": "ConnectorConfigurationAuthentication", "description": "Client authentication."}, "connectorId": {"description": "Required. Immutable. The ID of the Connector these parameters are configured for.", "type": "string"}, "endpoint": {"$ref": "ConnectorConfigurationEndpoint", "description": "Specifies how to reach the remote system this connection is pointing to."}, "network": {"$ref": "ConnectorConfigurationNetwork", "description": "Networking configuration."}}, "type": "object"}, "ConnectorConfigurationAsset": {"description": "Data Asset - a resource within instance of the system, reachable under specified endpoint. For example a database name in a SQL DB.", "id": "ConnectorConfigurationAsset", "properties": {"database": {"description": "Name of the database.", "type": "string"}, "googleCloudResource": {"description": "Full Google Cloud resource name - https://cloud.google.com/apis/design/resource_names#full_resource_name. Example: `//library.googleapis.com/shelves/shelf1/books/book2`", "type": "string"}}, "type": "object"}, "ConnectorConfigurationAuthentication": {"description": "Client authentication.", "id": "ConnectorConfigurationAuthentication", "properties": {"serviceAccount": {"description": "Output only. Google-managed service account associated with this connection, e.g., `service-{project_number}@gcp-sa-bigqueryconnection.iam.gserviceaccount.com`. BigQuery jobs using this connection will act as `service_account` identity while connecting to the datasource.", "readOnly": true, "type": "string"}, "usernamePassword": {"$ref": "ConnectorConfigurationUsernamePassword", "description": "Username/password authentication."}}, "type": "object"}, "ConnectorConfigurationEndpoint": {"description": "Remote endpoint specification.", "id": "ConnectorConfigurationEndpoint", "properties": {"hostPort": {"description": "Host and port in a format of `hostname:port` as defined in https://www.ietf.org/rfc/rfc3986.html#section-3.2.2 and https://www.ietf.org/rfc/rfc3986.html#section-3.2.3.", "type": "string"}}, "type": "object"}, "ConnectorConfigurationNetwork": {"description": "Network related configuration.", "id": "ConnectorConfigurationNetwork", "properties": {"privateServiceConnect": {"$ref": "ConnectorConfigurationPrivateServiceConnect", "description": "Private Service Connect networking configuration."}}, "type": "object"}, "ConnectorConfigurationPrivateServiceConnect": {"description": "Private Service Connect configuration.", "id": "ConnectorConfigurationPrivateServiceConnect", "properties": {"networkAttachment": {"description": "Required. Network Attachment name in the format of `projects/{project}/regions/{region}/networkAttachments/{networkattachment}`.", "type": "string"}}, "type": "object"}, "ConnectorConfigurationSecret": {"description": "Secret value parameter.", "id": "ConnectorConfigurationSecret", "properties": {"plaintext": {"description": "Input only. Secret as plaintext.", "type": "string"}, "secretType": {"description": "Output only. Indicates type of secret. Can be used to check type of stored secret value even if it's `INPUT_ONLY`.", "enum": ["SECRET_TYPE_UNSPECIFIED", "PLAINTEXT"], "enumDescriptions": ["", ""], "readOnly": true, "type": "string"}}, "type": "object"}, "ConnectorConfigurationUsernamePassword": {"description": "Username and Password authentication.", "id": "ConnectorConfigurationUsernamePassword", "properties": {"password": {"$ref": "ConnectorConfigurationSecret", "description": "Required. Password."}, "username": {"description": "Required. Username.", "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "GetIamPolicyRequest": {"description": "Request message for `GetIamPolicy` method.", "id": "GetIamPolicyRequest", "properties": {"options": {"$ref": "GetPolicyOptions", "description": "OPTIONAL: A `GetPolicyOptions` object for specifying options to `GetIamPolicy`."}}, "type": "object"}, "GetPolicyOptions": {"description": "Encapsulates settings provided to GetIamPolicy.", "id": "GetPolicyOptions", "properties": {"requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "ListConnectionsResponse": {"description": "The response for ConnectionService.ListConnections.", "id": "ListConnectionsResponse", "properties": {"connections": {"description": "List of connections.", "items": {"$ref": "Connection"}, "type": "array"}, "nextPageToken": {"description": "Next page token.", "type": "string"}}, "type": "object"}, "MetastoreServiceConfig": {"description": "Configuration of the Dataproc Metastore Service.", "id": "MetastoreServiceConfig", "properties": {"metastoreService": {"description": "Optional. Resource name of an existing Dataproc Metastore service. Example: * `projects/[project_id]/locations/[region]/services/[service_id]`", "type": "string"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "SalesforceDataCloudProperties": {"description": "Connection properties specific to Salesforce DataCloud. This is intended for use only by Salesforce partner projects.", "id": "SalesforceDataCloudProperties", "properties": {"identity": {"description": "Output only. A unique Google-owned and Google-generated service account identity for the connection.", "readOnly": true, "type": "string"}, "instanceUri": {"description": "The URL to the user's Salesforce DataCloud instance.", "type": "string"}, "tenantId": {"description": "The ID of the user's Salesforce tenant.", "type": "string"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "SparkHistoryServerConfig": {"description": "Configuration of the Spark History Server.", "id": "SparkHistoryServerConfig", "properties": {"dataprocCluster": {"description": "Optional. Resource name of an existing Dataproc Cluster to act as a Spark History Server for the connection. Example: * `projects/[project_id]/regions/[region]/clusters/[cluster_name]`", "type": "string"}}, "type": "object"}, "SparkProperties": {"description": "Container for connection properties to execute stored procedures for Apache Spark.", "id": "SparkProperties", "properties": {"metastoreServiceConfig": {"$ref": "MetastoreServiceConfig", "description": "Optional. Dataproc Metastore Service configuration for the connection."}, "serviceAccountId": {"description": "Output only. The account ID of the service created for the purpose of this connection. The service account does not have any permissions associated with it when it is created. After creation, customers delegate permissions to the service account. When the connection is used in the context of a stored procedure for Apache Spark in BigQuery, the service account is used to connect to the desired resources in Google Cloud. The account ID is in the form of: <EMAIL>", "readOnly": true, "type": "string"}, "sparkHistoryServerConfig": {"$ref": "SparkHistoryServerConfig", "description": "Optional. Spark History Server configuration for the connection."}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}}, "servicePath": "", "title": "BigQuery Connection API", "version": "v1", "version_module": true}
{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud_search": {"description": "Index and serve your organization's data with Cloud Search"}, "https://www.googleapis.com/auth/cloud_search.debug": {"description": "Index and serve your organization's data with Cloud Search"}, "https://www.googleapis.com/auth/cloud_search.indexing": {"description": "Index and serve your organization's data with Cloud Search"}, "https://www.googleapis.com/auth/cloud_search.query": {"description": "Search your organization's data in the Cloud Search index"}, "https://www.googleapis.com/auth/cloud_search.settings": {"description": "Index and serve your organization's data with Cloud Search"}, "https://www.googleapis.com/auth/cloud_search.settings.indexing": {"description": "Index and serve your organization's data with Cloud Search"}, "https://www.googleapis.com/auth/cloud_search.settings.query": {"description": "Index and serve your organization's data with Cloud Search"}, "https://www.googleapis.com/auth/cloud_search.stats": {"description": "Index and serve your organization's data with Cloud Search"}, "https://www.googleapis.com/auth/cloud_search.stats.indexing": {"description": "Index and serve your organization's data with Cloud Search"}}}}, "basePath": "", "baseUrl": "https://cloudsearch.googleapis.com/", "batchPath": "batch", "canonicalName": "Cloud Search", "description": "Cloud Search provides cloud-based search capabilities over Google Workspace data. The Cloud Search API allows indexing of non-Google Workspace data into Cloud Search.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/cloud-search/docs/guides/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "cloudsearch:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://cloudsearch.mtls.googleapis.com/", "name": "cloudsearch", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"debug": {"resources": {"datasources": {"resources": {"items": {"methods": {"checkAccess": {"description": "Checks whether an item is accessible by specified principal. Principal must be a user; groups and domain values aren't supported. **Note:** This API requires an admin account to execute.", "flatPath": "v1/debug/datasources/{datasourcesId}/items/{itemsId}:checkAccess", "httpMethod": "POST", "id": "cloudsearch.debug.datasources.items.checkAccess", "parameterOrder": ["name"], "parameters": {"debugOptions.enableDebugging": {"description": "If you are asked by Google to help with debugging, set this field. Otherwise, ignore this field.", "location": "query", "type": "boolean"}, "name": {"description": "Item name, format: datasources/{source_id}/items/{item_id}", "location": "path", "pattern": "^datasources/[^/]+/items/[^/]+$", "required": true, "type": "string"}}, "path": "v1/debug/{+name}:checkAccess", "request": {"$ref": "Principal"}, "response": {"$ref": "CheckAccessResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.debug"]}, "searchByViewUrl": {"description": "Fetches the item whose viewUrl exactly matches that of the URL provided in the request. **Note:** This API requires an admin account to execute.", "flatPath": "v1/debug/datasources/{datasourcesId}/items:searchByViewUrl", "httpMethod": "POST", "id": "cloudsearch.debug.datasources.items.searchByViewUrl", "parameterOrder": ["name"], "parameters": {"name": {"description": "Source name, format: datasources/{source_id}", "location": "path", "pattern": "^datasources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/debug/{+name}/items:searchByViewUrl", "request": {"$ref": "SearchItemsByViewUrlRequest"}, "response": {"$ref": "SearchItemsByViewUrlResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.debug"]}}, "resources": {"unmappedids": {"methods": {"list": {"description": "List all unmapped identities for a specific item. **Note:** This API requires an admin account to execute.", "flatPath": "v1/debug/datasources/{datasourcesId}/items/{itemsId}/unmappedids", "httpMethod": "GET", "id": "cloudsearch.debug.datasources.items.unmappedids.list", "parameterOrder": ["parent"], "parameters": {"debugOptions.enableDebugging": {"description": "If you are asked by Google to help with debugging, set this field. Otherwise, ignore this field.", "location": "query", "type": "boolean"}, "pageSize": {"description": "Maximum number of items to fetch in a request. Defaults to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}, "parent": {"description": "The name of the item, in the following format: datasources/{source_id}/items/{ID}", "location": "path", "pattern": "^datasources/[^/]+/items/[^/]+$", "required": true, "type": "string"}}, "path": "v1/debug/{+parent}/unmappedids", "response": {"$ref": "ListUnmappedIdentitiesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.debug"]}}}}}}}, "identitysources": {"resources": {"items": {"methods": {"listForunmappedidentity": {"description": "Lists names of items associated with an unmapped identity. **Note:** This API requires an admin account to execute.", "flatPath": "v1/debug/identitysources/{identitysourcesId}/items:forunmappedidentity", "httpMethod": "GET", "id": "cloudsearch.debug.identitysources.items.listForunmappedidentity", "parameterOrder": ["parent"], "parameters": {"debugOptions.enableDebugging": {"description": "If you are asked by Google to help with debugging, set this field. Otherwise, ignore this field.", "location": "query", "type": "boolean"}, "groupResourceName": {"location": "query", "type": "string"}, "pageSize": {"description": "Maximum number of items to fetch in a request. Defaults to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}, "parent": {"description": "The name of the identity source, in the following format: identitysources/{source_id}}", "location": "path", "pattern": "^identitysources/[^/]+$", "required": true, "type": "string"}, "userResourceName": {"location": "query", "type": "string"}}, "path": "v1/debug/{+parent}/items:forunmappedidentity", "response": {"$ref": "ListItemNamesForUnmappedIdentityResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.debug"]}}}, "unmappedids": {"methods": {"list": {"description": "Lists unmapped user identities for an identity source. **Note:** This API requires an admin account to execute.", "flatPath": "v1/debug/identitysources/{identitysourcesId}/unmappedids", "httpMethod": "GET", "id": "cloudsearch.debug.identitysources.unmappedids.list", "parameterOrder": ["parent"], "parameters": {"debugOptions.enableDebugging": {"description": "If you are asked by Google to help with debugging, set this field. Otherwise, ignore this field.", "location": "query", "type": "boolean"}, "pageSize": {"description": "Maximum number of items to fetch in a request. Defaults to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}, "parent": {"description": "The name of the identity source, in the following format: identitysources/{source_id}", "location": "path", "pattern": "^identitysources/[^/]+$", "required": true, "type": "string"}, "resolutionStatusCode": {"description": "Limit users selection to this status.", "enum": ["CODE_UNSPECIFIED", "NOT_FOUND", "IDENTITY_SOURCE_NOT_FOUND", "IDENTITY_SOURCE_MISCONFIGURED", "TOO_MANY_MAPPINGS_FOUND", "INTERNAL_ERROR"], "enumDescriptions": ["Input-only value. Used to list all unmapped identities regardless of status.", "The unmapped identity was not found in IDaaS, and needs to be provided by the user.", "The identity source associated with the identity was either not found or deleted.", "IDaaS does not understand the identity source, probably because the schema was modified in a non compatible way.", "The number of users associated with the external identity is too large.", "Internal error."], "location": "query", "type": "string"}}, "path": "v1/debug/{+parent}/unmappedids", "response": {"$ref": "ListUnmappedIdentitiesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.debug"]}}}}}}}, "indexing": {"resources": {"datasources": {"methods": {"deleteSchema": {"description": "Deletes the schema of a data source. **Note:** This API requires an admin or service account to execute.", "flatPath": "v1/indexing/datasources/{datasourcesId}/schema", "httpMethod": "DELETE", "id": "cloudsearch.indexing.datasources.deleteSchema", "parameterOrder": ["name"], "parameters": {"debugOptions.enableDebugging": {"description": "If you are asked by Google to help with debugging, set this field. Otherwise, ignore this field.", "location": "query", "type": "boolean"}, "name": {"description": "The name of the data source to delete Schema. Format: datasources/{source_id}", "location": "path", "pattern": "^datasources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/indexing/{+name}/schema", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.indexing"]}, "getSchema": {"description": "Gets the schema of a data source. **Note:** This API requires an admin or service account to execute.", "flatPath": "v1/indexing/datasources/{datasourcesId}/schema", "httpMethod": "GET", "id": "cloudsearch.indexing.datasources.getSchema", "parameterOrder": ["name"], "parameters": {"debugOptions.enableDebugging": {"description": "If you are asked by Google to help with debugging, set this field. Otherwise, ignore this field.", "location": "query", "type": "boolean"}, "name": {"description": "The name of the data source to get Schema. Format: datasources/{source_id}", "location": "path", "pattern": "^datasources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/indexing/{+name}/schema", "response": {"$ref": "<PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.indexing"]}, "updateSchema": {"description": "Updates the schema of a data source. This method does not perform incremental updates to the schema. Instead, this method updates the schema by overwriting the entire schema. **Note:** This API requires an admin or service account to execute.", "flatPath": "v1/indexing/datasources/{datasourcesId}/schema", "httpMethod": "PUT", "id": "cloudsearch.indexing.datasources.updateSchema", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the data source to update Schema. Format: datasources/{source_id}", "location": "path", "pattern": "^datasources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/indexing/{+name}/schema", "request": {"$ref": "UpdateSchemaRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.indexing"]}}, "resources": {"items": {"methods": {"delete": {"description": "Deletes Item resource for the specified resource name. This API requires an admin or service account to execute. The service account used is the one whitelisted in the corresponding data source.", "flatPath": "v1/indexing/datasources/{datasourcesId}/items/{itemsId}", "httpMethod": "DELETE", "id": "cloudsearch.indexing.datasources.items.delete", "parameterOrder": ["name"], "parameters": {"connectorName": {"description": "The name of connector making this call. Format: datasources/{source_id}/connectors/{ID}", "location": "query", "type": "string"}, "debugOptions.enableDebugging": {"description": "If you are asked by Google to help with debugging, set this field. Otherwise, ignore this field.", "location": "query", "type": "boolean"}, "mode": {"description": "Required. The RequestMode for this request.", "enum": ["UNSPECIFIED", "SYNCHRONOUS", "ASYNCHRONOUS"], "enumDescriptions": ["The priority is not specified in the update request. Leaving priority unspecified results in an update failure.", "For real-time updates.", "For changes that are executed after the response is sent back to the caller."], "location": "query", "type": "string"}, "name": {"description": "Required. The name of the item to delete. Format: datasources/{source_id}/items/{item_id}", "location": "path", "pattern": "^datasources/[^/]+/items/[^/]+$", "required": true, "type": "string"}, "version": {"description": "Required. The incremented version of the item to delete from the index. The indexing system stores the version from the datasource as a byte string and compares the Item version in the index to the version of the queued Item using lexical ordering. Cloud Search Indexing won't delete any queued item with a version value that is less than or equal to the version of the currently indexed item. The maximum length for this field is 1024 bytes. For information on how item version affects the deletion process, refer to [Handle revisions after manual deletes](https://developers.google.com/cloud-search/docs/guides/operations).", "format": "byte", "location": "query", "type": "string"}}, "path": "v1/indexing/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.indexing"]}, "deleteQueueItems": {"description": "Deletes all items in a queue. This method is useful for deleting stale items. This API requires an admin or service account to execute. The service account used is the one whitelisted in the corresponding data source.", "flatPath": "v1/indexing/datasources/{datasourcesId}/items:deleteQueueItems", "httpMethod": "POST", "id": "cloudsearch.indexing.datasources.items.deleteQueueItems", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the Data Source to delete items in a queue. Format: datasources/{source_id}", "location": "path", "pattern": "^datasources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/indexing/{+name}/items:deleteQueueItems", "request": {"$ref": "DeleteQueueItemsRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.indexing"]}, "get": {"description": "Gets Item resource by item name. This API requires an admin or service account to execute. The service account used is the one whitelisted in the corresponding data source.", "flatPath": "v1/indexing/datasources/{datasourcesId}/items/{itemsId}", "httpMethod": "GET", "id": "cloudsearch.indexing.datasources.items.get", "parameterOrder": ["name"], "parameters": {"connectorName": {"description": "The name of connector making this call. Format: datasources/{source_id}/connectors/{ID}", "location": "query", "type": "string"}, "debugOptions.enableDebugging": {"description": "If you are asked by Google to help with debugging, set this field. Otherwise, ignore this field.", "location": "query", "type": "boolean"}, "name": {"description": "The name of the item to get info. Format: datasources/{source_id}/items/{item_id}", "location": "path", "pattern": "^datasources/[^/]+/items/[^/]+$", "required": true, "type": "string"}}, "path": "v1/indexing/{+name}", "response": {"$ref": "<PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.indexing"]}, "index": {"description": "Updates Item ACL, metadata, and content. It will insert the Item if it does not exist. This method does not support partial updates. Fields with no provided values are cleared out in the Cloud Search index. This API requires an admin or service account to execute. The service account used is the one whitelisted in the corresponding data source.", "flatPath": "v1/indexing/datasources/{datasourcesId}/items/{itemsId}:index", "httpMethod": "POST", "id": "cloudsearch.indexing.datasources.items.index", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the Item. Format: datasources/{source_id}/items/{item_id} This is a required field. The maximum length is 1536 characters.", "location": "path", "pattern": "^datasources/[^/]+/items/[^/]+$", "required": true, "type": "string"}}, "path": "v1/indexing/{+name}:index", "request": {"$ref": "IndexItemRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.indexing"]}, "list": {"description": "Lists all or a subset of Item resources. This API requires an admin or service account to execute. The service account used is the one whitelisted in the corresponding data source.", "flatPath": "v1/indexing/datasources/{datasourcesId}/items", "httpMethod": "GET", "id": "cloudsearch.indexing.datasources.items.list", "parameterOrder": ["name"], "parameters": {"brief": {"description": "When set to true, the indexing system only populates the following fields: name, version, queue. metadata.hash, metadata.title, metadata.sourceRepositoryURL, metadata.objectType, metadata.createTime, metadata.updateTime, metadata.contentLanguage, metadata.mimeType, structured_data.hash, content.hash, itemType, itemStatus.code, itemStatus.processingError.code, itemStatus.repositoryError.type, If this value is false, then all the fields are populated in Item.", "location": "query", "type": "boolean"}, "connectorName": {"description": "The name of connector making this call. Format: datasources/{source_id}/connectors/{ID}", "location": "query", "type": "string"}, "debugOptions.enableDebugging": {"description": "If you are asked by Google to help with debugging, set this field. Otherwise, ignore this field.", "location": "query", "type": "boolean"}, "name": {"description": "The name of the Data Source to list Items. Format: datasources/{source_id}", "location": "path", "pattern": "^datasources/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Maximum number of items to fetch in a request. The max value is 1000 when brief is true. The max value is 10 if brief is false. The default value is 10", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}}, "path": "v1/indexing/{+name}/items", "response": {"$ref": "ListItemsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.indexing"]}, "poll": {"description": "Polls for unreserved items from the indexing queue and marks a set as reserved, starting with items that have the oldest timestamp from the highest priority ItemStatus. The priority order is as follows: ERROR MODIFIED NEW_ITEM ACCEPTED Reserving items ensures that polling from other threads cannot create overlapping sets. After handling the reserved items, the client should put items back into the unreserved state, either by calling index, or by calling push with the type REQUEUE. Items automatically become available (unreserved) after 4 hours even if no update or push method is called. This API requires an admin or service account to execute. The service account used is the one whitelisted in the corresponding data source.", "flatPath": "v1/indexing/datasources/{datasourcesId}/items:poll", "httpMethod": "POST", "id": "cloudsearch.indexing.datasources.items.poll", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the Data Source to poll items. Format: datasources/{source_id}", "location": "path", "pattern": "^datasources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/indexing/{+name}/items:poll", "request": {"$ref": "PollItemsRequest"}, "response": {"$ref": "PollItemsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.indexing"]}, "push": {"description": "Pushes an item onto a queue for later polling and updating. This API requires an admin or service account to execute. The service account used is the one whitelisted in the corresponding data source.", "flatPath": "v1/indexing/datasources/{datasourcesId}/items/{itemsId}:push", "httpMethod": "POST", "id": "cloudsearch.indexing.datasources.items.push", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the item to push into the indexing queue. Format: datasources/{source_id}/items/{ID} This is a required field. The maximum length is 1536 characters.", "location": "path", "pattern": "^datasources/[^/]+/items/[^/]+$", "required": true, "type": "string"}}, "path": "v1/indexing/{+name}:push", "request": {"$ref": "PushItemRequest"}, "response": {"$ref": "<PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.indexing"]}, "unreserve": {"description": "Unreserves all items from a queue, making them all eligible to be polled. This method is useful for resetting the indexing queue after a connector has been restarted. This API requires an admin or service account to execute. The service account used is the one whitelisted in the corresponding data source.", "flatPath": "v1/indexing/datasources/{datasourcesId}/items:unreserve", "httpMethod": "POST", "id": "cloudsearch.indexing.datasources.items.unreserve", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the Data Source to unreserve all items. Format: datasources/{source_id}", "location": "path", "pattern": "^datasources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/indexing/{+name}/items:unreserve", "request": {"$ref": "UnreserveItemsRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.indexing"]}, "upload": {"description": "Creates an upload session for uploading item content. For items smaller than 100 KB, it's easier to embed the content inline within an index request. This API requires an admin or service account to execute. The service account used is the one whitelisted in the corresponding data source.", "flatPath": "v1/indexing/datasources/{datasourcesId}/items/{itemsId}:upload", "httpMethod": "POST", "id": "cloudsearch.indexing.datasources.items.upload", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the Item to start a resumable upload. Format: datasources/{source_id}/items/{item_id}. The maximum length is 1536 bytes.", "location": "path", "pattern": "^datasources/[^/]+/items/[^/]+$", "required": true, "type": "string"}}, "path": "v1/indexing/{+name}:upload", "request": {"$ref": "StartUploadItemRequest"}, "response": {"$ref": "UploadItemRef"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.indexing"]}}}}}}}, "media": {"methods": {"upload": {"description": "Uploads media for indexing. The upload endpoint supports direct and resumable upload protocols and is intended for large items that can not be [inlined during index requests](https://developers.google.com/cloud-search/docs/reference/rest/v1/indexing.datasources.items#itemcontent). To index large content: 1. Call indexing.datasources.items.upload with the item name to begin an upload session and retrieve the UploadItemRef. 1. Call media.upload to upload the content, as a streaming request, using the same resource name from the UploadItemRef from step 1. 1. Call indexing.datasources.items.index to index the item. Populate the [ItemContent](/cloud-search/docs/reference/rest/v1/indexing.datasources.items#ItemContent) with the UploadItemRef from step 1. For additional information, see [Create a content connector using the REST API](https://developers.google.com/cloud-search/docs/guides/content-connector#rest). **Note:** This API requires a service account to execute.", "flatPath": "v1/media/{mediaId}", "httpMethod": "POST", "id": "cloudsearch.media.upload", "mediaUpload": {"accept": ["*/*"], "protocols": {"simple": {"multipart": true, "path": "/upload/v1/media/{+resourceName}"}}}, "parameterOrder": ["resourceName"], "parameters": {"resourceName": {"description": "Name of the media that is being downloaded. See ReadRequest.resource_name.", "location": "path", "pattern": "^.*$", "required": true, "type": "string"}}, "path": "v1/media/{+resourceName}", "request": {"$ref": "Media"}, "response": {"$ref": "Media"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.indexing"], "supportsMediaUpload": true}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/operations/{operationsId}", "httpMethod": "GET", "id": "cloudsearch.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^operations/.*$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.debug", "https://www.googleapis.com/auth/cloud_search.indexing", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.indexing", "https://www.googleapis.com/auth/cloud_search.settings.query"]}}, "resources": {"lro": {"methods": {"list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/operations/{operationsId}/lro", "httpMethod": "GET", "id": "cloudsearch.operations.lro.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^operations/.*$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/lro", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.debug", "https://www.googleapis.com/auth/cloud_search.indexing", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.indexing", "https://www.googleapis.com/auth/cloud_search.settings.query"]}}}}}, "query": {"methods": {"debugSearch": {"description": "Returns Debug information for Cloud Search Query API provides the search method. **Note:** This API requires a standard end user account to execute. A service account can't perform Query API requests directly; to use a service account to perform queries, set up [Google Workspace domain-wide delegation of authority](https://developers.google.com/cloud-search/docs/guides/delegation/).", "flatPath": "v1/query:debugSearch", "httpMethod": "POST", "id": "cloudsearch.query.debugSearch", "parameterOrder": [], "parameters": {}, "path": "v1/query:debugSearch", "request": {"$ref": "SearchRequest"}, "response": {"$ref": "DebugResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.query"]}, "removeActivity": {"description": "Provides functionality to remove logged activity for a user. Currently to be used only for Chat 1p clients **Note:** This API requires a standard end user account to execute. A service account can't perform Remove Activity requests directly; to use a service account to perform queries, set up [Google Workspace domain-wide delegation of authority](https://developers.google.com/cloud-search/docs/guides/delegation/).", "flatPath": "v1/query:removeActivity", "httpMethod": "POST", "id": "cloudsearch.query.removeActivity", "parameterOrder": [], "parameters": {}, "path": "v1/query:removeActivity", "request": {"$ref": "RemoveActivityRequest"}, "response": {"$ref": "RemoveActivityResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.query"]}, "search": {"description": "The Cloud Search Query API provides the search method, which returns the most relevant results from a user query. The results can come from Google Workspace apps, such as Gmail or Google Drive, or they can come from data that you have indexed from a third party. **Note:** This API requires a standard end user account to execute. A service account can't perform Query API requests directly; to use a service account to perform queries, set up [Google Workspace domain-wide delegation of authority](https://developers.google.com/cloud-search/docs/guides/delegation/).", "flatPath": "v1/query/search", "httpMethod": "POST", "id": "cloudsearch.query.search", "parameterOrder": [], "parameters": {}, "path": "v1/query/search", "request": {"$ref": "SearchRequest"}, "response": {"$ref": "SearchResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.query"]}, "suggest": {"description": "Provides suggestions for autocompleting the query. **Note:** This API requires a standard end user account to execute. A service account can't perform Query API requests directly; to use a service account to perform queries, set up [Google Workspace domain-wide delegation of authority](https://developers.google.com/cloud-search/docs/guides/delegation/).", "flatPath": "v1/query/suggest", "httpMethod": "POST", "id": "cloudsearch.query.suggest", "parameterOrder": [], "parameters": {}, "path": "v1/query/suggest", "request": {"$ref": "SuggestRequest"}, "response": {"$ref": "SuggestResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.query"]}}, "resources": {"sources": {"methods": {"list": {"description": "Returns list of sources that user can use for Search and Suggest APIs. **Note:** This API requires a standard end user account to execute. A service account can't perform Query API requests directly; to use a service account to perform queries, set up [Google Workspace domain-wide delegation of authority](https://developers.google.com/cloud-search/docs/guides/delegation/).", "flatPath": "v1/query/sources", "httpMethod": "GET", "id": "cloudsearch.query.sources.list", "parameterOrder": [], "parameters": {"pageToken": {"description": "Number of sources to return in the response.", "location": "query", "type": "string"}, "requestOptions.debugOptions.enableDebugging": {"description": "If you are asked by Google to help with debugging, set this field. Otherwise, ignore this field.", "location": "query", "type": "boolean"}, "requestOptions.languageCode": {"description": "The BCP-47 language code, such as \"en-US\" or \"sr-Latn\". For more information, see http://www.unicode.org/reports/tr35/#Unicode_locale_identifier. For translations. Set this field using the language set in browser or for the page. In the event that the user's language preference is known, set this field to the known user language. When specified, the documents in search results are biased towards the specified language. The Suggest API uses this field as a hint to make better third-party autocomplete predictions.", "location": "query", "type": "string"}, "requestOptions.searchApplicationId": {"description": "The ID generated when you create a search application using the [admin console](https://support.google.com/a/answer/9043922).", "location": "query", "type": "string"}, "requestOptions.timeZone": {"description": "Current user's time zone id, such as \"America/Los_Angeles\" or \"Australia/Sydney\". These IDs are defined by [Unicode Common Locale Data Repository (CLDR)](http://cldr.unicode.org/) project, and currently available in the file [timezone.xml](http://unicode.org/repos/cldr/trunk/common/bcp47/timezone.xml). This field is used to correctly interpret date and time queries. If this field is not specified, the default time zone (UTC) is used.", "location": "query", "type": "string"}}, "path": "v1/query/sources", "response": {"$ref": "ListQuerySourcesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.query"]}}}}}, "settings": {"methods": {"getCustomer": {"description": "Get customer settings. **Note:** This API requires an admin account to execute.", "flatPath": "v1/settings/customer", "httpMethod": "GET", "id": "cloudsearch.settings.getCustomer", "parameterOrder": [], "parameters": {}, "path": "v1/settings/customer", "response": {"$ref": "CustomerSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.indexing"]}, "updateCustomer": {"description": "Update customer settings. **Note:** This API requires an admin account to execute.", "flatPath": "v1/settings/customer", "httpMethod": "PATCH", "id": "cloudsearch.settings.updateCustomer", "parameterOrder": [], "parameters": {"updateMask": {"description": "Update mask to control which fields get updated. If you specify a field in the update_mask but don't specify its value here, that field will be cleared. If the mask is not present or empty, all fields will be updated. Currently supported field paths: vpc_settings and audit_logging_settings", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/settings/customer", "request": {"$ref": "CustomerSettings"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.indexing"]}}, "resources": {"datasources": {"methods": {"create": {"description": "Creates a datasource. **Note:** This API requires an admin account to execute.", "flatPath": "v1/settings/datasources", "httpMethod": "POST", "id": "cloudsearch.settings.datasources.create", "parameterOrder": [], "parameters": {}, "path": "v1/settings/datasources", "request": {"$ref": "DataSource"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.indexing"]}, "delete": {"description": "Deletes a datasource. **Note:** This API requires an admin account to execute.", "flatPath": "v1/settings/datasources/{datasourcesId}", "httpMethod": "DELETE", "id": "cloudsearch.settings.datasources.delete", "parameterOrder": ["name"], "parameters": {"debugOptions.enableDebugging": {"description": "If you are asked by Google to help with debugging, set this field. Otherwise, ignore this field.", "location": "query", "type": "boolean"}, "name": {"description": "The name of the datasource. Format: datasources/{source_id}.", "location": "path", "pattern": "^datasources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/settings/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.indexing"]}, "get": {"description": "Gets a datasource. **Note:** This API requires an admin account to execute.", "flatPath": "v1/settings/datasources/{datasourcesId}", "httpMethod": "GET", "id": "cloudsearch.settings.datasources.get", "parameterOrder": ["name"], "parameters": {"debugOptions.enableDebugging": {"description": "If you are asked by Google to help with debugging, set this field. Otherwise, ignore this field.", "location": "query", "type": "boolean"}, "name": {"description": "The name of the datasource resource. Format: datasources/{source_id}.", "location": "path", "pattern": "^datasources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/settings/{+name}", "response": {"$ref": "DataSource"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.indexing"]}, "list": {"description": "Lists datasources. **Note:** This API requires an admin account to execute.", "flatPath": "v1/settings/datasources", "httpMethod": "GET", "id": "cloudsearch.settings.datasources.list", "parameterOrder": [], "parameters": {"debugOptions.enableDebugging": {"description": "If you are asked by Google to help with debugging, set this field. Otherwise, ignore this field.", "location": "query", "type": "boolean"}, "pageSize": {"description": "Maximum number of datasources to fetch in a request. The max value is 1000. The default value is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Starting index of the results.", "location": "query", "type": "string"}}, "path": "v1/settings/datasources", "response": {"$ref": "ListDataSourceResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.indexing"]}, "patch": {"description": "Updates a datasource. **Note:** This API requires an admin account to execute.", "flatPath": "v1/settings/datasources/{datasourcesId}", "httpMethod": "PATCH", "id": "cloudsearch.settings.datasources.patch", "parameterOrder": ["name"], "parameters": {"debugOptions.enableDebugging": {"description": "If you are asked by Google to help with debugging, set this field. Otherwise, ignore this field.", "location": "query", "type": "boolean"}, "name": {"description": "The name of the datasource resource. Format: datasources/{source_id}. The name is ignored when creating a datasource.", "location": "path", "pattern": "^datasources/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Only applies to [`settings.datasources.patch`](https://developers.google.com/cloud-search/docs/reference/rest/v1/settings.datasources/patch). Update mask to control which fields to update. Example field paths: `name`, `displayName`. * If `update_mask` is non-empty, then only the fields specified in the `update_mask` are updated. * If you specify a field in the `update_mask`, but don't specify its value in the source, that field is cleared. * If the `update_mask` is not present or empty or has the value `*`, then all fields are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/settings/{+name}", "request": {"$ref": "DataSource"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.indexing"]}, "update": {"description": "Updates a datasource. **Note:** This API requires an admin account to execute.", "flatPath": "v1/settings/datasources/{datasourcesId}", "httpMethod": "PUT", "id": "cloudsearch.settings.datasources.update", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the datasource resource. Format: datasources/{source_id}. The name is ignored when creating a datasource.", "location": "path", "pattern": "^datasources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/settings/{+name}", "request": {"$ref": "UpdateDataSourceRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.indexing"]}}}, "searchapplications": {"methods": {"create": {"description": "Creates a search application. **Note:** This API requires an admin account to execute.", "flatPath": "v1/settings/searchapplications", "httpMethod": "POST", "id": "cloudsearch.settings.searchapplications.create", "parameterOrder": [], "parameters": {}, "path": "v1/settings/searchapplications", "request": {"$ref": "SearchApplication"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.query"]}, "delete": {"description": "Deletes a search application. **Note:** This API requires an admin account to execute.", "flatPath": "v1/settings/searchapplications/{searchapplicationsId}", "httpMethod": "DELETE", "id": "cloudsearch.settings.searchapplications.delete", "parameterOrder": ["name"], "parameters": {"debugOptions.enableDebugging": {"description": "If you are asked by Google to help with debugging, set this field. Otherwise, ignore this field.", "location": "query", "type": "boolean"}, "name": {"description": "The name of the search application to be deleted. Format: applications/{application_id}.", "location": "path", "pattern": "^searchapplications/[^/]+$", "required": true, "type": "string"}}, "path": "v1/settings/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.query"]}, "get": {"description": "Gets the specified search application. **Note:** This API requires an admin account to execute.", "flatPath": "v1/settings/searchapplications/{searchapplicationsId}", "httpMethod": "GET", "id": "cloudsearch.settings.searchapplications.get", "parameterOrder": ["name"], "parameters": {"debugOptions.enableDebugging": {"description": "If you are asked by Google to help with debugging, set this field. Otherwise, ignore this field.", "location": "query", "type": "boolean"}, "name": {"description": "The name of the search application. Format: searchapplications/{application_id}.", "location": "path", "pattern": "^searchapplications/[^/]+$", "required": true, "type": "string"}}, "path": "v1/settings/{+name}", "response": {"$ref": "SearchApplication"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.query"]}, "list": {"description": "Lists all search applications. **Note:** This API requires an admin account to execute.", "flatPath": "v1/settings/searchapplications", "httpMethod": "GET", "id": "cloudsearch.settings.searchapplications.list", "parameterOrder": [], "parameters": {"debugOptions.enableDebugging": {"description": "If you are asked by Google to help with debugging, set this field. Otherwise, ignore this field.", "location": "query", "type": "boolean"}, "pageSize": {"description": "The maximum number of items to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any. The default value is 10", "location": "query", "type": "string"}}, "path": "v1/settings/searchapplications", "response": {"$ref": "ListSearchApplicationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.query"]}, "patch": {"description": "Updates a search application. **Note:** This API requires an admin account to execute.", "flatPath": "v1/settings/searchapplications/{searchapplicationsId}", "httpMethod": "PATCH", "id": "cloudsearch.settings.searchapplications.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the Search Application. Format: searchapplications/{application_id}.", "location": "path", "pattern": "^searchapplications/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Only applies to [`settings.searchapplications.patch`](https://developers.google.com/cloud-search/docs/reference/rest/v1/settings.searchapplications/patch). Update mask to control which fields to update. Example field paths: `search_application.name`, `search_application.displayName`. * If `update_mask` is non-empty, then only the fields specified in the `update_mask` are updated. * If you specify a field in the `update_mask`, but don't specify its value in the `search_application`, then that field is cleared. * If the `update_mask` is not present or empty or has the value `*`, then all fields are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/settings/{+name}", "request": {"$ref": "SearchApplication"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.query"]}, "reset": {"description": "Resets a search application to default settings. This will return an empty response. **Note:** This API requires an admin account to execute.", "flatPath": "v1/settings/searchapplications/{searchapplicationsId}:reset", "httpMethod": "POST", "id": "cloudsearch.settings.searchapplications.reset", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the search application to be reset. Format: applications/{application_id}.", "location": "path", "pattern": "^searchapplications/[^/]+$", "required": true, "type": "string"}}, "path": "v1/settings/{+name}:reset", "request": {"$ref": "ResetSearchApplicationRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.query"]}, "update": {"description": "Updates a search application. **Note:** This API requires an admin account to execute.", "flatPath": "v1/settings/searchapplications/{searchapplicationsId}", "httpMethod": "PUT", "id": "cloudsearch.settings.searchapplications.update", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the Search Application. Format: searchapplications/{application_id}.", "location": "path", "pattern": "^searchapplications/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Only applies to [`settings.searchapplications.patch`](https://developers.google.com/cloud-search/docs/reference/rest/v1/settings.searchapplications/patch). Update mask to control which fields to update. Example field paths: `search_application.name`, `search_application.displayName`. * If `update_mask` is non-empty, then only the fields specified in the `update_mask` are updated. * If you specify a field in the `update_mask`, but don't specify its value in the `search_application`, then that field is cleared. * If the `update_mask` is not present or empty or has the value `*`, then all fields are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/settings/{+name}", "request": {"$ref": "SearchApplication"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.query"]}}}}}, "stats": {"methods": {"getIndex": {"description": "Gets indexed item statistics aggreggated across all data sources. This API only returns statistics for previous dates; it doesn't return statistics for the current day. **Note:** This API requires a standard end user account to execute.", "flatPath": "v1/stats/index", "httpMethod": "GET", "id": "cloudsearch.stats.getIndex", "parameterOrder": [], "parameters": {"fromDate.day": {"description": "Day of month. Must be from 1 to 31 and valid for the year and month.", "format": "int32", "location": "query", "type": "integer"}, "fromDate.month": {"description": "Month of date. Must be from 1 to 12.", "format": "int32", "location": "query", "type": "integer"}, "fromDate.year": {"description": "Year of date. Must be from 1 to 9999.", "format": "int32", "location": "query", "type": "integer"}, "toDate.day": {"description": "Day of month. Must be from 1 to 31 and valid for the year and month.", "format": "int32", "location": "query", "type": "integer"}, "toDate.month": {"description": "Month of date. Must be from 1 to 12.", "format": "int32", "location": "query", "type": "integer"}, "toDate.year": {"description": "Year of date. Must be from 1 to 9999.", "format": "int32", "location": "query", "type": "integer"}}, "path": "v1/stats/index", "response": {"$ref": "GetCustomerIndexStatsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.stats", "https://www.googleapis.com/auth/cloud_search.stats.indexing"]}, "getQuery": {"description": "Get the query statistics for customer. **Note:** This API requires a standard end user account to execute.", "flatPath": "v1/stats/query", "httpMethod": "GET", "id": "cloudsearch.stats.getQuery", "parameterOrder": [], "parameters": {"fromDate.day": {"description": "Day of month. Must be from 1 to 31 and valid for the year and month.", "format": "int32", "location": "query", "type": "integer"}, "fromDate.month": {"description": "Month of date. Must be from 1 to 12.", "format": "int32", "location": "query", "type": "integer"}, "fromDate.year": {"description": "Year of date. Must be from 1 to 9999.", "format": "int32", "location": "query", "type": "integer"}, "toDate.day": {"description": "Day of month. Must be from 1 to 31 and valid for the year and month.", "format": "int32", "location": "query", "type": "integer"}, "toDate.month": {"description": "Month of date. Must be from 1 to 12.", "format": "int32", "location": "query", "type": "integer"}, "toDate.year": {"description": "Year of date. Must be from 1 to 9999.", "format": "int32", "location": "query", "type": "integer"}}, "path": "v1/stats/query", "response": {"$ref": "GetCustomerQueryStatsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.stats", "https://www.googleapis.com/auth/cloud_search.stats.indexing"]}, "getSearchapplication": {"description": "Get search application stats for customer. **Note:** This API requires a standard end user account to execute.", "flatPath": "v1/stats/searchapplication", "httpMethod": "GET", "id": "cloudsearch.stats.getSearchapplication", "parameterOrder": [], "parameters": {"endDate.day": {"description": "Day of month. Must be from 1 to 31 and valid for the year and month.", "format": "int32", "location": "query", "type": "integer"}, "endDate.month": {"description": "Month of date. Must be from 1 to 12.", "format": "int32", "location": "query", "type": "integer"}, "endDate.year": {"description": "Year of date. Must be from 1 to 9999.", "format": "int32", "location": "query", "type": "integer"}, "startDate.day": {"description": "Day of month. Must be from 1 to 31 and valid for the year and month.", "format": "int32", "location": "query", "type": "integer"}, "startDate.month": {"description": "Month of date. Must be from 1 to 12.", "format": "int32", "location": "query", "type": "integer"}, "startDate.year": {"description": "Year of date. Must be from 1 to 9999.", "format": "int32", "location": "query", "type": "integer"}}, "path": "v1/stats/searchapplication", "response": {"$ref": "GetCustomerSearchApplicationStatsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.stats", "https://www.googleapis.com/auth/cloud_search.stats.indexing"]}, "getSession": {"description": "Get the # of search sessions, % of successful sessions with a click query statistics for customer. **Note:** This API requires a standard end user account to execute.", "flatPath": "v1/stats/session", "httpMethod": "GET", "id": "cloudsearch.stats.getSession", "parameterOrder": [], "parameters": {"fromDate.day": {"description": "Day of month. Must be from 1 to 31 and valid for the year and month.", "format": "int32", "location": "query", "type": "integer"}, "fromDate.month": {"description": "Month of date. Must be from 1 to 12.", "format": "int32", "location": "query", "type": "integer"}, "fromDate.year": {"description": "Year of date. Must be from 1 to 9999.", "format": "int32", "location": "query", "type": "integer"}, "toDate.day": {"description": "Day of month. Must be from 1 to 31 and valid for the year and month.", "format": "int32", "location": "query", "type": "integer"}, "toDate.month": {"description": "Month of date. Must be from 1 to 12.", "format": "int32", "location": "query", "type": "integer"}, "toDate.year": {"description": "Year of date. Must be from 1 to 9999.", "format": "int32", "location": "query", "type": "integer"}}, "path": "v1/stats/session", "response": {"$ref": "GetCustomerSessionStatsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.stats", "https://www.googleapis.com/auth/cloud_search.stats.indexing"]}, "getUser": {"description": "Get the users statistics for customer. **Note:** This API requires a standard end user account to execute.", "flatPath": "v1/stats/user", "httpMethod": "GET", "id": "cloudsearch.stats.getUser", "parameterOrder": [], "parameters": {"fromDate.day": {"description": "Day of month. Must be from 1 to 31 and valid for the year and month.", "format": "int32", "location": "query", "type": "integer"}, "fromDate.month": {"description": "Month of date. Must be from 1 to 12.", "format": "int32", "location": "query", "type": "integer"}, "fromDate.year": {"description": "Year of date. Must be from 1 to 9999.", "format": "int32", "location": "query", "type": "integer"}, "toDate.day": {"description": "Day of month. Must be from 1 to 31 and valid for the year and month.", "format": "int32", "location": "query", "type": "integer"}, "toDate.month": {"description": "Month of date. Must be from 1 to 12.", "format": "int32", "location": "query", "type": "integer"}, "toDate.year": {"description": "Year of date. Must be from 1 to 9999.", "format": "int32", "location": "query", "type": "integer"}}, "path": "v1/stats/user", "response": {"$ref": "GetCustomerUserStatsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.stats", "https://www.googleapis.com/auth/cloud_search.stats.indexing"]}}, "resources": {"index": {"resources": {"datasources": {"methods": {"get": {"description": "Gets indexed item statistics for a single data source. **Note:** This API requires a standard end user account to execute.", "flatPath": "v1/stats/index/datasources/{datasourcesId}", "httpMethod": "GET", "id": "cloudsearch.stats.index.datasources.get", "parameterOrder": ["name"], "parameters": {"fromDate.day": {"description": "Day of month. Must be from 1 to 31 and valid for the year and month.", "format": "int32", "location": "query", "type": "integer"}, "fromDate.month": {"description": "Month of date. Must be from 1 to 12.", "format": "int32", "location": "query", "type": "integer"}, "fromDate.year": {"description": "Year of date. Must be from 1 to 9999.", "format": "int32", "location": "query", "type": "integer"}, "name": {"description": "The resource id of the data source to retrieve statistics for, in the following format: \"datasources/{source_id}\"", "location": "path", "pattern": "^datasources/[^/]+$", "required": true, "type": "string"}, "toDate.day": {"description": "Day of month. Must be from 1 to 31 and valid for the year and month.", "format": "int32", "location": "query", "type": "integer"}, "toDate.month": {"description": "Month of date. Must be from 1 to 12.", "format": "int32", "location": "query", "type": "integer"}, "toDate.year": {"description": "Year of date. Must be from 1 to 9999.", "format": "int32", "location": "query", "type": "integer"}}, "path": "v1/stats/index/{+name}", "response": {"$ref": "GetDataSourceIndexStatsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.stats", "https://www.googleapis.com/auth/cloud_search.stats.indexing"]}}}}}, "query": {"resources": {"searchapplications": {"methods": {"get": {"description": "Get the query statistics for search application. **Note:** This API requires a standard end user account to execute.", "flatPath": "v1/stats/query/searchapplications/{searchapplicationsId}", "httpMethod": "GET", "id": "cloudsearch.stats.query.searchapplications.get", "parameterOrder": ["name"], "parameters": {"fromDate.day": {"description": "Day of month. Must be from 1 to 31 and valid for the year and month.", "format": "int32", "location": "query", "type": "integer"}, "fromDate.month": {"description": "Month of date. Must be from 1 to 12.", "format": "int32", "location": "query", "type": "integer"}, "fromDate.year": {"description": "Year of date. Must be from 1 to 9999.", "format": "int32", "location": "query", "type": "integer"}, "name": {"description": "The resource id of the search application query stats, in the following format: searchapplications/{application_id}", "location": "path", "pattern": "^searchapplications/[^/]+$", "required": true, "type": "string"}, "toDate.day": {"description": "Day of month. Must be from 1 to 31 and valid for the year and month.", "format": "int32", "location": "query", "type": "integer"}, "toDate.month": {"description": "Month of date. Must be from 1 to 12.", "format": "int32", "location": "query", "type": "integer"}, "toDate.year": {"description": "Year of date. Must be from 1 to 9999.", "format": "int32", "location": "query", "type": "integer"}}, "path": "v1/stats/query/{+name}", "response": {"$ref": "GetSearchApplicationQueryStatsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.stats", "https://www.googleapis.com/auth/cloud_search.stats.indexing"]}}}}}, "session": {"resources": {"searchapplications": {"methods": {"get": {"description": "Get the # of search sessions, % of successful sessions with a click query statistics for search application. **Note:** This API requires a standard end user account to execute.", "flatPath": "v1/stats/session/searchapplications/{searchapplicationsId}", "httpMethod": "GET", "id": "cloudsearch.stats.session.searchapplications.get", "parameterOrder": ["name"], "parameters": {"fromDate.day": {"description": "Day of month. Must be from 1 to 31 and valid for the year and month.", "format": "int32", "location": "query", "type": "integer"}, "fromDate.month": {"description": "Month of date. Must be from 1 to 12.", "format": "int32", "location": "query", "type": "integer"}, "fromDate.year": {"description": "Year of date. Must be from 1 to 9999.", "format": "int32", "location": "query", "type": "integer"}, "name": {"description": "The resource id of the search application session stats, in the following format: searchapplications/{application_id}", "location": "path", "pattern": "^searchapplications/[^/]+$", "required": true, "type": "string"}, "toDate.day": {"description": "Day of month. Must be from 1 to 31 and valid for the year and month.", "format": "int32", "location": "query", "type": "integer"}, "toDate.month": {"description": "Month of date. Must be from 1 to 12.", "format": "int32", "location": "query", "type": "integer"}, "toDate.year": {"description": "Year of date. Must be from 1 to 9999.", "format": "int32", "location": "query", "type": "integer"}}, "path": "v1/stats/session/{+name}", "response": {"$ref": "GetSearchApplicationSessionStatsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.stats", "https://www.googleapis.com/auth/cloud_search.stats.indexing"]}}}}}, "user": {"resources": {"searchapplications": {"methods": {"get": {"description": "Get the users statistics for search application. **Note:** This API requires a standard end user account to execute.", "flatPath": "v1/stats/user/searchapplications/{searchapplicationsId}", "httpMethod": "GET", "id": "cloudsearch.stats.user.searchapplications.get", "parameterOrder": ["name"], "parameters": {"fromDate.day": {"description": "Day of month. Must be from 1 to 31 and valid for the year and month.", "format": "int32", "location": "query", "type": "integer"}, "fromDate.month": {"description": "Month of date. Must be from 1 to 12.", "format": "int32", "location": "query", "type": "integer"}, "fromDate.year": {"description": "Year of date. Must be from 1 to 9999.", "format": "int32", "location": "query", "type": "integer"}, "name": {"description": "The resource id of the search application session stats, in the following format: searchapplications/{application_id}", "location": "path", "pattern": "^searchapplications/[^/]+$", "required": true, "type": "string"}, "toDate.day": {"description": "Day of month. Must be from 1 to 31 and valid for the year and month.", "format": "int32", "location": "query", "type": "integer"}, "toDate.month": {"description": "Month of date. Must be from 1 to 12.", "format": "int32", "location": "query", "type": "integer"}, "toDate.year": {"description": "Year of date. Must be from 1 to 9999.", "format": "int32", "location": "query", "type": "integer"}}, "path": "v1/stats/user/{+name}", "response": {"$ref": "GetSearchApplicationUserStatsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.stats", "https://www.googleapis.com/auth/cloud_search.stats.indexing"]}}}}}}}, "v1": {"methods": {"initializeCustomer": {"description": "Enables `third party` support in Google Cloud Search. **Note:** This API requires an admin account to execute.", "flatPath": "v1:initializeCustomer", "httpMethod": "POST", "id": "cloudsearch.initializeCustomer", "parameterOrder": [], "parameters": {}, "path": "v1:initializeCustomer", "request": {"$ref": "InitializeCustomerRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud_search", "https://www.googleapis.com/auth/cloud_search.settings", "https://www.googleapis.com/auth/cloud_search.settings.indexing"]}}}}, "revision": "********", "rootUrl": "https://cloudsearch.googleapis.com/", "schemas": {"Action": {"id": "Action", "properties": {"title": {"description": "[Required] Title of the action.", "type": "string"}, "url": {"description": "[Optional] Url of the action.", "type": "string"}}, "type": "object"}, "AuditLoggingSettings": {"description": "Represents the settings for Cloud audit logging", "id": "AuditLoggingSettings", "properties": {"logAdminReadActions": {"description": "Indicates whether audit logging is on/off for admin activity read APIs i.e. Get/List DataSources, Get/List SearchApplications etc.", "type": "boolean"}, "logDataReadActions": {"description": "Indicates whether audit logging is on/off for data access read APIs i.e. ListItems, GetItem etc.", "type": "boolean"}, "logDataWriteActions": {"description": "Indicates whether audit logging is on/off for data access write APIs i.e. IndexItem etc.", "type": "boolean"}, "project": {"description": "The resource name of the GCP Project to store audit logs. Cloud audit logging will be enabled after project_name has been updated through CustomerService. Format: projects/{project_id}", "type": "string"}}, "type": "object"}, "BackgroundColoredText": {"id": "BackgroundColoredText", "properties": {"backgroundColor": {"description": "[Optional] Color of the background. The text color can change depending on the selected background color, and the client does not have control over this. If missing, the background will be WHITE.", "enum": ["UNKNOWN_COLOR", "WHITE", "YELLOW", "ORANGE", "GREEN", "BLUE", "GREY"], "enumDescriptions": ["", "", "", "", "", "", ""], "type": "string"}, "text": {"description": "[Required] The text to display.", "type": "string"}}, "type": "object"}, "BooleanOperatorOptions": {"description": "Used to provide a search operator for boolean properties. This is optional. Search operators let users restrict the query to specific fields relevant to the type of item being searched.", "id": "BooleanOperatorOptions", "properties": {"operatorName": {"description": "Indicates the operator name required in the query in order to isolate the boolean property. For example, if operator<PERSON>ame is *closed* and the property's name is *isClosed*, then queries like *closed:<value>* show results only where the value of the property named *isClosed* matches *<value>*. By contrast, a search that uses the same *<value>* without an operator returns all items where *<value>* matches the value of any String properties or text within the content field for the item. The operator name can only contain lowercase letters (a-z). The maximum length is 32 characters.", "type": "string"}}, "type": "object"}, "BooleanPropertyOptions": {"description": "The options for boolean properties.", "id": "BooleanPropertyOptions", "properties": {"operatorOptions": {"$ref": "BooleanOperatorOptions", "description": "If set, describes how the boolean should be used as a search operator."}}, "type": "object"}, "CheckAccessResponse": {"id": "CheckAccessResponse", "properties": {"hasAccess": {"description": "Returns true if principal has access. Returns false otherwise.", "type": "boolean"}}, "type": "object"}, "CompositeFilter": {"id": "CompositeFilter", "properties": {"logicOperator": {"description": "The logic operator of the sub filter.", "enum": ["AND", "OR", "NOT"], "enumDescriptions": ["Logical operators, which can only be applied to sub filters.", "", "NOT can only be applied on a single sub filter."], "type": "string"}, "subFilters": {"description": "Sub filters.", "items": {"$ref": "Filter"}, "type": "array"}}, "type": "object"}, "Content": {"id": "Content", "properties": {"actions": {"description": "[Optional] Actions for this card.", "items": {"$ref": "Action"}, "type": "array"}, "description": {"$ref": "SafeHtmlProto", "description": "[Optional] Description of the card."}, "subtitle": {"$ref": "BackgroundColoredText", "description": "[Optional] Subtitle of the card."}, "title": {"$ref": "BackgroundColoredText", "description": "[Optional] Title of the card."}}, "type": "object"}, "Context": {"id": "Context", "properties": {"app": {"description": "[Optional] App where the card should be shown. If missing, the card will be shown in TOPAZ.", "items": {"enum": ["UNKNOWN_APP", "TOPAZ", "MOMA"], "enumDescriptions": ["", "", ""], "type": "string"}, "type": "array"}, "dayOfWeek": {"description": "[Optional] Day of week when the card should be shown, where 0 is Monday.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "endDateSec": {"description": "[Optional] Date (in seconds since epoch) when the card should stop being shown. If missing, end_date_sec will be set to Jan 1st, 2100.", "format": "int64", "type": "string"}, "endDayOffsetSec": {"description": "[Optional] End time in seconds, within a day, when the card should stop being shown if it's within [start_date_sec, end_date_sec]. If missing, this is set to 86400 (24 hours x 3600 sec/hour), i.e., midnight next day.", "format": "int64", "type": "string"}, "locale": {"description": "[Optional] The locales for which the card should be triggered (e.g., en_US and en_CA). If missing, the card is going to show to clients regardless of their locale.", "items": {"type": "string"}, "type": "array"}, "location": {"description": "[Optional] Text-free locations where the card should be shown. This is expected to match the user's location in focus. If no location is specified, the card will be shown for any location.", "items": {"type": "string"}, "type": "array"}, "query": {"description": "[Required only for Answer and RHS cards - will be ignored for Homepage] cards. It's the exact case-insensitive queries that will trigger the Answer or RHS card.", "items": {"type": "string"}, "type": "array"}, "startDateSec": {"description": "[Optional] Date (in seconds since epoch) when the card should start being shown. If missing, start_date_sec will be Jan 1st, 1970 UTC.", "format": "int64", "type": "string"}, "startDayOffsetSec": {"description": "[Optional] Start time in seconds, within a day, when the card should be shown if it's within [start_date_sec, end_date_sec]. If 0, the card will be shown from 12:00am on.", "format": "int64", "type": "string"}, "surface": {"description": "[Optional] Surface where the card should be shown in. If missing, the card will be shown in any surface.", "items": {"enum": ["UNKNOWN_SURFACE", "DESKTOP", "ANDROID", "IOS", "MOBILE", "ANY"], "enumDescriptions": ["", "", "", "", "Any mobile device.", ""], "type": "string"}, "type": "array"}, "type": {"description": "[Required] Type of the card (homepage, Answer or RHS).", "items": {"enum": ["UNKNOWN_CARD_TYPE", "HOMEPAGE_CARD", "ANSWER_CARD", "RHS_CARD"], "enumDescriptions": ["", "", "", ""], "type": "string"}, "type": "array"}}, "type": "object"}, "ContextAttribute": {"description": "A named attribute associated with an item which can be used for influencing the ranking of the item based on the context in the request.", "id": "ContextAttribute", "properties": {"name": {"description": "The name of the attribute. It should not be empty. The maximum length is 32 characters. The name must start with a letter and can only contain letters (A-Z, a-z) or numbers (0-9). The name will be normalized (lower-cased) before being matched.", "type": "string"}, "values": {"description": "Text values of the attribute. The maximum number of elements is 10. The maximum length of an element in the array is 32 characters. The value will be normalized (lower-cased) before being matched.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "CustomerIndexStats": {"description": "Aggregation of items by status code as of the specified date.", "id": "CustomerIndexStats", "properties": {"date": {"$ref": "Date", "description": "The date for which statistics were calculated."}, "itemCountByStatus": {"description": "Number of items aggregrated by status code.", "items": {"$ref": "ItemCountByStatus"}, "type": "array"}}, "type": "object"}, "CustomerQueryStats": {"id": "CustomerQueryStats", "properties": {"date": {"$ref": "Date", "description": "The date for which query stats were calculated. Stats calculated on the next day close to midnight are returned."}, "queryCountByStatus": {"items": {"$ref": "QueryCountByStatus"}, "type": "array"}}, "type": "object"}, "CustomerSearchApplicationStats": {"description": "Search application stats for a customer for the given date.", "id": "CustomerSearchApplicationStats", "properties": {"count": {"description": "The count of search applications for the date.", "format": "int64", "type": "string"}, "date": {"$ref": "Date", "description": "The date for which search application stats were calculated."}}, "type": "object"}, "CustomerSessionStats": {"id": "CustomerSessionStats", "properties": {"date": {"$ref": "Date", "description": "The date for which session stats were calculated. Stats are calculated on the following day, close to midnight PST, and then returned."}, "searchSessionsCount": {"description": "The count of search sessions on the day", "format": "int64", "type": "string"}}, "type": "object"}, "CustomerSettings": {"description": "Represents settings at a customer level.", "id": "CustomerSettings", "properties": {"auditLoggingSettings": {"$ref": "AuditLoggingSettings", "description": "Audit Logging settings for the customer. If update_mask is empty then this field will be updated based on UpdateCustomerSettings request."}, "vpcSettings": {"$ref": "VPCSettings", "description": "VPC SC settings for the customer. If update_mask is empty then this field will be updated based on UpdateCustomerSettings request."}}, "type": "object"}, "CustomerUserStats": {"id": "CustomerUserStats", "properties": {"date": {"$ref": "Date", "description": "The date for which session stats were calculated. Stats calculated on the next day close to midnight are returned."}, "oneDayActiveUsersCount": {"description": "The count of unique active users in the past one day", "format": "int64", "type": "string"}, "sevenDaysActiveUsersCount": {"description": "The count of unique active users in the past seven days", "format": "int64", "type": "string"}, "thirtyDaysActiveUsersCount": {"description": "The count of unique active users in the past thirty days", "format": "int64", "type": "string"}}, "type": "object"}, "DataSource": {"description": "Datasource is a logical namespace for items to be indexed. All items must belong to a datasource. This is the prerequisite before items can be indexed into Cloud Search. ", "id": "DataSource", "properties": {"disableModifications": {"description": "If true, sets the datasource to read-only mode. In read-only mode, the Indexing API rejects any requests to index or delete items in this source. Enabling read-only mode does not stop the processing of previously accepted data.", "type": "boolean"}, "disableServing": {"description": "Disable serving any search or assist results.", "type": "boolean"}, "displayName": {"description": "Required. Display name of the datasource The maximum length is 300 characters.", "type": "string"}, "indexingServiceAccounts": {"description": "List of service accounts that have indexing access.", "items": {"type": "string"}, "type": "array"}, "itemsVisibility": {"description": "This field restricts visibility to items at the datasource level. Items within the datasource are restricted to the union of users and groups included in this field. Note that, this does not ensure access to a specific item, as users need to have ACL permissions on the contained items. This ensures a high level access on the entire datasource, and that the individual items are not shared outside this visibility.", "items": {"$ref": "GSuitePrincipal"}, "type": "array"}, "name": {"description": "The name of the datasource resource. Format: datasources/{source_id}. The name is ignored when creating a datasource.", "type": "string"}, "operationIds": {"description": "IDs of the Long Running Operations (LROs) currently running for this schema.", "items": {"type": "string"}, "type": "array"}, "returnThumbnailUrls": {"description": "Can a user request to get thumbnail URI for Items indexed in this data source.", "type": "boolean"}, "shortName": {"description": "A short name or alias for the source. This value will be used to match the 'source' operator. For example, if the short name is *<value>* then queries like *source:<value>* will only return results for this source. The value must be unique across all datasources. The value must only contain alphanumeric characters (a-zA-Z0-9). The value cannot start with 'google' and cannot be one of the following: mail, gmail, docs, drive, groups, sites, calendar, hangouts, gplus, keep, people, teams. Its maximum length is 32 characters.", "type": "string"}}, "type": "object"}, "DataSourceIndexStats": {"description": "Aggregation of items by status code as of the specified date.", "id": "DataSourceIndexStats", "properties": {"date": {"$ref": "Date", "description": "The date for which index stats were calculated. If the date of request is not the current date then stats calculated on the next day are returned. Stats are calculated close to mid night in this case. If date of request is current date, then real time stats are returned."}, "itemCountByStatus": {"description": "Number of items aggregrated by status code.", "items": {"$ref": "ItemCountByStatus"}, "type": "array"}}, "type": "object"}, "DataSourceRestriction": {"description": "Restriction on Datasource.", "id": "DataSourceRestriction", "properties": {"filterOptions": {"description": "Filter options restricting the results. If multiple filters are present, they are grouped by object type before joining. Filters with the same object type are joined conjunctively, then the resulting expressions are joined disjunctively. The maximum number of elements is 20. NOTE: Suggest API supports only few filters at the moment: \"objecttype\", \"type\" and \"mimetype\". For now, schema specific filters cannot be used to filter suggestions.", "items": {"$ref": "FilterOptions"}, "type": "array"}, "source": {"$ref": "Source", "description": "The source of restriction."}}, "type": "object"}, "Date": {"description": "Represents a whole calendar date, for example a date of birth. The time of day and time zone are either specified elsewhere or are not significant. The date is relative to the [Proleptic Gregorian Calendar](https://en.wikipedia.org/wiki/Proleptic_Gregorian_calendar). The date must be a valid calendar date between the year 1 and 9999.", "id": "Date", "properties": {"day": {"description": "Day of month. Must be from 1 to 31 and valid for the year and month.", "format": "int32", "type": "integer"}, "month": {"description": "Month of date. Must be from 1 to 12.", "format": "int32", "type": "integer"}, "year": {"description": "Year of date. Must be from 1 to 9999.", "format": "int32", "type": "integer"}}, "type": "object"}, "DateOperatorOptions": {"description": "Optional. Provides a search operator for date properties. Search operators let users restrict the query to specific fields relevant to the type of item being searched.", "id": "DateOperatorOptions", "properties": {"greaterThanOperatorName": {"description": "Indicates the operator name required in the query in order to isolate the date property using the greater-than operator. For example, if greaterThanOperatorName is *closedafter* and the property's name is *closeDate*, then queries like *closedafter:<value>* show results only where the value of the property named *closeDate* is later than *<value>*. The operator name can only contain lowercase letters (a-z). The maximum length is 32 characters.", "type": "string"}, "lessThanOperatorName": {"description": "Indicates the operator name required in the query in order to isolate the date property using the less-than operator. For example, if lessThanOperatorName is *closedbefore* and the property's name is *closeDate*, then queries like *closedbefore:<value>* show results only where the value of the property named *closeDate* is earlier than *<value>*. The operator name can only contain lowercase letters (a-z). The maximum length is 32 characters.", "type": "string"}, "operatorName": {"description": "Indicates the actual string required in the query in order to isolate the date property. For example, suppose an issue tracking schema object has a property named *closeDate* that specifies an operator with an operatorName of *closedon*. For searches on that data, queries like *closedon:<value>* show results only where the value of the *closeDate* property matches *<value>*. By contrast, a search that uses the same *<value>* without an operator returns all items where *<value>* matches the value of any String properties or text within the content field for the indexed datasource. The operator name can only contain lowercase letters (a-z). The maximum length is 32 characters.", "type": "string"}}, "type": "object"}, "DatePropertyOptions": {"description": "The options for date properties.", "id": "DatePropertyOptions", "properties": {"operatorOptions": {"$ref": "DateOperatorOptions", "description": "If set, describes how the date should be used as a search operator."}}, "type": "object"}, "DateValues": {"description": "List of date values.", "id": "DateV<PERSON>ues", "properties": {"values": {"items": {"$ref": "Date"}, "type": "array"}}, "type": "object"}, "DebugOptions": {"description": "Shared request debug options for all cloudsearch RPC methods.", "id": "DebugOptions", "properties": {"enableDebugging": {"description": "If you are asked by Google to help with debugging, set this field. Otherwise, ignore this field.", "type": "boolean"}}, "type": "object"}, "DebugResponse": {"description": "Debug Search Response.", "id": "DebugResponse", "properties": {"gsrRequest": {"description": "Serialized string of GenericSearchRequest.", "format": "byte", "type": "string"}, "gsrResponse": {"description": "Serialized string of GenericSearchResponse.", "format": "byte", "type": "string"}, "searchResponse": {"$ref": "SearchResponse", "description": "Search response."}}, "type": "object"}, "DeleteQueueItemsRequest": {"id": "DeleteQueueItemsRequest", "properties": {"connectorName": {"description": "The name of connector making this call. Format: datasources/{source_id}/connectors/{ID}", "type": "string"}, "debugOptions": {"$ref": "DebugOptions", "description": "Common debug options."}, "queue": {"description": "The name of a queue to delete items from.", "type": "string"}}, "type": "object"}, "DisplayedProperty": {"description": "A reference to a top-level property within the object that should be displayed in search results. The values of the chosen properties is displayed in the search results along with the display label for that property if one is specified. If a display label is not specified, only the values is shown.", "id": "DisplayedProperty", "properties": {"propertyName": {"description": "The name of the top-level property as defined in a property definition for the object. If the name is not a defined property in the schema, an error is given when attempting to update the schema.", "type": "string"}}, "type": "object"}, "DoubleOperatorOptions": {"description": "Used to provide a search operator for double properties. This is optional. Search operators let users restrict the query to specific fields relevant to the type of item being searched.", "id": "DoubleOperatorOptions", "properties": {"operatorName": {"description": "Indicates the operator name required in the query in order to use the double property in sorting or as a facet. The operator name can only contain lowercase letters (a-z). The maximum length is 32 characters.", "type": "string"}}, "type": "object"}, "DoublePropertyOptions": {"description": "The options for double properties.", "id": "DoublePropertyOptions", "properties": {"operatorOptions": {"$ref": "DoubleOperatorOptions", "description": "If set, describes how the double should be used as a search operator."}}, "type": "object"}, "DoubleValues": {"description": "List of double values.", "id": "DoubleV<PERSON>ues", "properties": {"values": {"items": {"format": "double", "type": "number"}, "type": "array"}}, "type": "object"}, "DriveFollowUpRestrict": {"description": "Drive follow-up search restricts (e.g. \"followup:suggestions\").", "id": "DriveFollowUpRestrict", "properties": {"type": {"enum": ["UNSPECIFIED", "FOLLOWUP_SUGGESTIONS", "FOLLOWUP_ACTION_ITEMS"], "enumDescriptions": ["", "", ""], "type": "string"}}, "type": "object"}, "DriveLocationRestrict": {"description": "Drive location search restricts (e.g. \"is:starred\").", "id": "DriveLocationRestrict", "properties": {"type": {"enum": ["UNSPECIFIED", "TRASHED", "STARRED"], "enumDescriptions": ["", "", ""], "type": "string"}}, "type": "object"}, "DriveMimeTypeRestrict": {"description": "Drive mime-type search restricts (e.g. \"type:pdf\").", "id": "DriveMimeTypeRestrict", "properties": {"type": {"enum": ["UNSPECIFIED", "PDF", "DOCUMENT", "PRESENTATION", "SPREADSHEET", "FORM", "DRAWING", "SCRIPT", "MAP", "IMAGE", "AUDIO", "VIDEO", "FOLDER", "ARCHIVE", "SITE"], "enumDescriptions": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "type": "string"}}, "type": "object"}, "DriveTimeSpanRestrict": {"description": "The time span search restrict (e.g. \"after:2017-09-11 before:2017-09-12\").", "id": "DriveTimeSpanRestrict", "properties": {"type": {"enum": ["UNSPECIFIED", "TODAY", "YESTERDAY", "LAST_7_DAYS", "LAST_30_DAYS", "LAST_90_DAYS"], "enumDescriptions": ["", "", "", "", "Not Enabled", "Not Enabled"], "type": "string"}}, "type": "object"}, "EmailAddress": {"description": "A person's email address.", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"customType": {"description": "If the value of type is custom, this property contains the custom type string.", "type": "string"}, "emailAddress": {"description": "The email address.", "type": "string"}, "emailUrl": {"description": "The URL to send email.", "type": "string"}, "primary": {"description": "Indicates if this is the user's primary email. Only one entry can be marked as primary.", "type": "boolean"}, "type": {"description": "The type of the email account. Acceptable values are: \"custom\", \"home\", \"other\", \"work\".", "type": "string"}}, "type": "object"}, "EnterpriseTopazFrontendTeamsLink": {"id": "EnterpriseTopazFrontendTeamsLink", "properties": {"type": {"description": "The identifying link type", "type": "string"}, "url": {"$ref": "SafeUrlProto"}}, "type": "object"}, "EnterpriseTopazFrontendTeamsPersonCorePhoneNumber": {"id": "EnterpriseTopazFrontendTeamsPersonCorePhoneNumber", "properties": {"phoneNumber": {"description": "Phone number in no particular format (as comes from the Focus profile).", "type": "string"}, "phoneUrl": {"$ref": "SafeUrlProto", "description": "Phone number URL"}, "type": {"enum": ["UNKNOWN", "MOBILE", "OFFICE", "OTHER"], "enumDescriptions": ["", "", "", ""], "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickAgendaEntry": {"description": "An AgendaEntry, e.g., a Calendar Event.", "id": "EnterpriseTopazSidekickAgendaEntry", "properties": {"agendaItemUrl": {"description": "URL of the agenda item.", "type": "string"}, "chronology": {"description": "The chronology from the present.", "enum": ["STALE", "ALL_DAY", "PAST", "RECENTLY_PAST", "PRESENT", "NEAR_FUTURE", "FUTURE"], "enumDescriptions": ["Stale.", "All day.", "Past.", "Recently past.", "Present.", "Near future.", "Future."], "type": "string"}, "creator": {"$ref": "EnterpriseTopazSidekickPerson", "description": "Person who created the event."}, "currentUserAttendingStatus": {"description": "Attendance status for the current user making the request. This is a convenience data member in order to avoid figuring out the same by iterating the invitee list above on the caller side.", "enum": ["AWAITING", "YES", "NO", "MAYBE"], "enumDescriptions": ["Awaiting for the user to set the status.", "Attending.", "Not attending.", "Tentatively attending."], "type": "string"}, "description": {"description": "Description of the agenda item (i.e., typically, summary in calendar event).", "type": "string"}, "document": {"description": "Items related to the current AgendaEntry. E.g., related drive/mail/groups documents.", "items": {"$ref": "EnterpriseTopazSidekickCommonDocument"}, "type": "array"}, "endDate": {"description": "End date \"Friday, August 26\" in the user's timezone.", "type": "string"}, "endTime": {"description": "End time (HH:mm) in the user's timezone.", "type": "string"}, "endTimeMs": {"description": "End time in milliseconds", "format": "int64", "type": "string"}, "eventId": {"description": "Event id provided by Calendar API.", "type": "string"}, "guestsCanInviteOthers": {"description": "Whether the guests can invite other guests.", "type": "boolean"}, "guestsCanModify": {"description": "Whether the guests can modify the event.", "type": "boolean"}, "guestsCanSeeGuests": {"description": "Whether the guests of the event can be seen. If false, the user is going to be reported as the only attendee to the meeting, even though there may be more attendees.", "type": "boolean"}, "hangoutId": {"description": "Hangout meeting identifier.", "type": "string"}, "hangoutUrl": {"description": "Absolute URL for the Hangout meeting.", "type": "string"}, "invitee": {"description": "People attending the meeting.", "items": {"$ref": "EnterpriseTopazSidekickPerson"}, "type": "array"}, "isAllDay": {"description": "Whether the entry lasts all day.", "type": "boolean"}, "lastModificationTimeMs": {"description": "Last time the event was modified.", "format": "int64", "type": "string"}, "location": {"description": "Agenda item location.", "type": "string"}, "notifyToUser": {"description": "Whether this should be notified to the user.", "type": "boolean"}, "otherAttendeesExcluded": {"description": "Whether guest list is not returned because number of attendees is too large.", "type": "boolean"}, "requesterIsOwner": {"description": "Whether the requester is the owner of the agenda entry.", "type": "boolean"}, "showFullEventDetailsToUse": {"description": "Whether the details of this entry should be displayed to the user.", "type": "boolean"}, "startDate": {"description": "Start date \"Friday, August 26\" in the user's timezone.", "type": "string"}, "startTime": {"description": "Start time (HH:mm) in the user's timezone.", "type": "string"}, "startTimeMs": {"description": "Start time in milliseconds.", "format": "int64", "type": "string"}, "timeZone": {"description": "User's calendar timezone;", "type": "string"}, "title": {"description": "Title of the agenda item.", "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickAgendaGroupCardProto": {"id": "EnterpriseTopazSidekickAgendaGroupCardProto", "properties": {"agendaItem": {"items": {"$ref": "EnterpriseTopazSidekickAgendaItem"}, "type": "array"}, "context": {"$ref": "EnterpriseTopazSidekickAgendaGroupCardProtoContext"}, "currentAgendaItem": {"$ref": "EnterpriseTopazSidekickAgendaItem"}}, "type": "object"}, "EnterpriseTopazSidekickAgendaGroupCardProtoContext": {"description": "The context that resulted in the generation of the card.", "id": "EnterpriseTopazSidekickAgendaGroupCardProtoContext", "properties": {"context": {"description": "User friendly free text that describes the context of the card (e.g. \"Next meeting with <PERSON>\"). This is largely only applicable when the card is generated from a query.", "type": "string"}, "date": {"description": "Localized free text that describes the dates represented by the card. Currently, the card will only represent a single day.", "type": "string"}, "eventsRestrict": {"description": "Represents restrictions applied to the events requested in the user's query.", "enum": ["NONE", "NEXT_MEETING"], "enumDescriptions": ["No specific event was requested.", "The next meeting was requested."], "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickAgendaItem": {"id": "EnterpriseTopazSidekickAgendaItem", "properties": {"conflictedGroup": {"$ref": "EnterpriseTopazSidekickConflictingEventsCardProto"}, "gapBefore": {"$ref": "EnterpriseTopazSidekickGap"}, "meeting": {"$ref": "EnterpriseTopazSidekickAgendaEntry"}}, "type": "object"}, "EnterpriseTopazSidekickAnswerAnswerList": {"description": "A list of answers represented as free text.", "id": "EnterpriseTopazSidekickAnswerAnswerList", "properties": {"labeledAnswer": {"description": "Answers that have a corresponding label.", "items": {"$ref": "EnterpriseTopazSidekickAnswerAnswerListLabeledAnswer"}, "type": "array"}, "type": {"description": "Answer type.", "enum": ["UNKNOWN", "PERSON_ADDRESS", "PERSON_BIRTHDAY", "PERSON_DEPARTMENT", "PERSON_DESK_LOCATION", "PERSON_EMAIL", "PERSON_JOB_TITLE", "PERSON_PHONE"], "enumDescriptions": ["", "", "", "", "", "", "", ""], "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickAnswerAnswerListLabeledAnswer": {"description": "An answer with a corresponding label.", "id": "EnterpriseTopazSidekickAnswerAnswerListLabeledAnswer", "properties": {"answer": {"description": "The free text answer.", "type": "string"}, "label": {"description": "A localized label for the answer (e.g. \"Cell phone\" vs \"Desk phone\").", "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickAnswerSuggestedQueryAnswerCard": {"description": "Contains a list of suggested queries. Allows the user to determine what natural language queries they can ask Cloud Search (e.g. \"what can I search for?\").", "id": "EnterpriseTopazSidekickAnswerSuggestedQueryAnswerCard", "properties": {"suggestedQueryCategory": {"description": "A list of queries to suggest.", "items": {"$ref": "EnterpriseTopazSidekickAnswerSuggestedQueryCategory"}, "type": "array"}}, "type": "object"}, "EnterpriseTopazSidekickAnswerSuggestedQueryCategory": {"description": "Contains a list of suggested queries for a single category.", "id": "EnterpriseTopazSidekickAnswerSuggestedQueryCategory", "properties": {"category": {"description": "The query list category.", "enum": ["UNKNOWN", "CALENDAR", "DOCUMENT", "PEOPLE"], "enumDescriptions": ["Unknown.", "Calendar based queries (e.g. \"my agenda for tomorrow\").", "Document based queries (e.g. \"files shared with me\").", "People based queries (e.g. \"what is x's email address?\")."], "type": "string"}, "isEnabled": {"description": "Whether this category is enabled.", "type": "boolean"}, "query": {"description": "List of suggested queries to show the user.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "EnterpriseTopazSidekickAssistCardProto": {"description": "Wrapper proto for the Assist cards.", "id": "EnterpriseTopazSidekickAssistCardProto", "properties": {"agendaGroupCardProto": {"$ref": "EnterpriseTopazSidekickAgendaGroupCardProto", "description": "Agenda group card."}, "cardMetadata": {"$ref": "EnterpriseTopazSidekickCardMetadata", "description": "Card metadata such as chronology and render mode of the card."}, "cardType": {"description": "Card type.", "enum": ["UNKNOWN_TYPE", "AGENDA", "CHANGELISTS", "CONFLICTING_MEETINGS", "CREATE_NOTES_FOR_MEETING", "CREATE_NOTES_FOR_MEETING_REQUEST", "CUSTOMER_NEWS", "FIND_MEETING_TIME", "NEXT_MEETING", "PERSONALIZED_DOCS", "TRENDING_DOCS", "UPCOMING_TRIP", "SUMMARY", "MEETINGS", "HOMEPAGE", "SHARE_MEETING_DOCS", "DISCOVER_PEOPLE", "HOMEPAGE_V3", "AGENDA_GROUP", "WORK_IN_PROGRESS", "GET_AND_KEEP_AHEAD", "GENERIC_ANSWER_CARD", "THIRD_PARTY_ANSWER_CARD", "DOMAIN_TRENDING_DOCS", "TEAM_TRENDING_DOCS", "DOCUMENT_LIST_ANSWER_CARD", "SUGGESTED_QUERY_ANSWER_CARD", "PERSON_ANSWER_CARD", "RELATED_PEOPLE_ANSWER_CARD", "PERSON_KNOWLEDGE_CARD", "PEOPLE_SEARCH_PROMOTION_CARD"], "enumDeprecated": [false, true, true, true, true, true, true, false, true, true, true, true, true, true, true, true, true, true, false, false, false, false, false, true, true, false, false, false, false, false, false], "enumDescriptions": ["The default type, an unknown card type.", "The user's agenda for the day.", "Changelists.", "Any group of meetings for the day that are overlapping.", "Create notes for a meeting specified in the request.", "Create notes for meeting query.", "News about your SFDC customers.", "Find a time for two people to meet.", "The user's next non-declined meeting.", "Important documents for you since you have viewed them in the last month and there's some action going on.", "Documents that are trending in your company. A TRENDING_DOCS card can be of two types: TRENDING_IN_COLLABORATORS (i.e., Stay in the Loop) and TRENDING_IN_DOMAIN (i.e., Popular Content). Use DOMAIN_TRENDING_DOCS/TEAM_TRENDING_DOCS instead.", "An upcoming trip with all trip information along with calendar events in the destination timezone.", "The Morning/Evening Summary Card for the next working day.", "A meeting. When requesting meetings, the service will return a MEETING card for each meeting the user has in his agenda.", "All cards related to the homepage (agenda, conflicting-meetings, summary, etc...). This type should no longer be used. Use HOMEPAGE_V3 instead.", "A card to with specifics to share meeting documents with all attendees.", "Represents a set of users that the requesting user has low affinity with.", "All cards related to the homepage-v3 (agenda-group, WIP, etc...)", "A group of agenda-events (meeting, conflicts)", "The documents that you were recently working on.", "(v3) The \"get and keep ahead\" card for today and tomorrow", "Generic answer card.", "Third party answer card.", "Documents that are trending in your company/domain (i.e., Popular Content).", "Documents that are trending in your team (i.e., Stay in the Loop).", "Documents that match the user's query (e.g. sheets shared with me).", "A help card that provides examples of queries the user can ask.", "Answer card for a single person (e.g. what is x's phone number).", "Answer card for a list of people related to the person that is the subject of the query (e.g. who reports to x).", "Knowledge card for a single person and their related people.", "People Search promotion card."], "type": "string"}, "conflictingMeetingsCard": {"$ref": "EnterpriseTopazSidekickConflictingEventsCardProto", "deprecated": true, "description": "Conflicting events card."}, "documentListCard": {"$ref": "EnterpriseTopazSidekickDocumentPerCategoryList", "description": "Answer card for documents that are applicable to the current query."}, "documentsWithMentions": {"$ref": "EnterpriseTopazSidekickDocumentPerCategoryList", "deprecated": true, "description": "Documents with mentions."}, "findMeetingTimeCard": {"$ref": "EnterpriseTopazSidekickFindMeetingTimeCardProto", "description": "Find meeting time card."}, "genericAnswerCard": {"$ref": "EnterpriseTopazSidekickGenericAnswerCard", "description": "Generic answer card."}, "getAndKeepAheadCard": {"$ref": "EnterpriseTopazSidekickGetAndKeepAheadCardProto", "description": "Get and keep ahead card."}, "meeting": {"$ref": "EnterpriseTopazSidekickAgendaEntry", "deprecated": true, "description": "Meeting card."}, "meetingNotesCard": {"$ref": "EnterpriseTopazSidekickMeetingNotesCardProto", "description": "Meeting notes card."}, "meetingNotesCardRequest": {"$ref": "EnterpriseTopazSidekickMeetingNotesCardRequest", "description": "Request for meeting notes card."}, "peopleDisambiguationCard": {"$ref": "EnterpriseTopazSidekickPeopleDisambiguationCard", "description": "The people disambiguation card."}, "peoplePromotionCard": {"$ref": "PeoplePromotionCard", "description": "People Search promotion card."}, "personAnswerCard": {"$ref": "EnterpriseTopazSidekickPeopleAnswerPersonAnswerCard", "description": "Answer card that represents a single person."}, "personProfileCard": {"$ref": "EnterpriseTopazSidekickPersonProfileCard", "description": "Full profile card."}, "personalizedDocsCard": {"$ref": "EnterpriseTopazSidekickPersonalizedDocsCardProto", "deprecated": true, "description": "Card with recommended documents for the user."}, "relatedPeopleAnswerCard": {"$ref": "EnterpriseTopazSidekickPeopleAnswerRelatedPeopleAnswerCard", "description": "Answer card that represents a list of people related to a person."}, "shareMeetingDocsCard": {"$ref": "EnterpriseTopazSidekickShareMeetingDocsCardProto", "deprecated": true, "description": "<PERSON><PERSON><PERSON> meeting docs card."}, "sharedDocuments": {"$ref": "EnterpriseTopazSidekickDocumentPerCategoryList", "deprecated": true, "description": "Shared documents."}, "suggestedQueryAnswerCard": {"$ref": "EnterpriseTopazSidekickAnswerSuggestedQueryAnswerCard", "description": "Answer card for what natural language queries the user can ask."}, "thirdPartyAnswerCard": {"$ref": "ThirdPartyGenericCard", "description": "Third party answer cards."}, "workInProgressCardProto": {"$ref": "EnterpriseTopazSidekickRecentDocumentsCardProto", "description": "Work In Progress card."}}, "type": "object"}, "EnterpriseTopazSidekickCardMetadata": {"description": "Card metadata.", "id": "EnterpriseTopazSidekickCardMetadata", "properties": {"cardCategory": {"description": "Declares a preference for how this card should be packed in MSCR. All cards in a response must correspond to a single category. As a result, cards may be dropped from the response if this field is set. Any card that does not match the category of the card with the highest priority in the response will be dropped.", "enum": ["DEFAULT", "ANSWER", "KNOWLEDGE", "HOMEPAGE"], "enumDescriptions": ["Let MSCR decide how this card should be packed. Most cards should choose this type. This type should largely be considered equivalent to ANSWER. However, this is not guaranteed to be the case as the request to MSCR may override the static configuration.", "This card should be rendered as an answer card.", "This card should be rendered as a knowledge card (a non-organic result).", "This card should be rendered in the Homepage."], "type": "string"}, "cardId": {"description": "An ID to identify the card and match actions to it. Be thoughtful of new card IDs since actions will be associated to that ID. E.g., if two card IDs collide, the system will think that the actions have been applied to the same card. Similarly, if EAS can return multiple cards of the same type (e.g., Meetings), ensure that the card_id identifies a given instance of the card so that, e.g., dismissals only affect the dismissed card as opposed to affecting all meeting cards.", "type": "string"}, "chronology": {"description": "Chronology.", "enum": ["UNKNOWN", "PAST", "RECENTLY_PAST", "PRESENT", "NEAR_FUTURE", "FUTURE"], "enumDescriptions": ["Unknown chronology (default).", "Past.", "Recently past.", "Present.", "Near future.", "Future."], "type": "string"}, "debugInfo": {"description": "Debug info (only reported if request's debug_level > 0).", "type": "string"}, "nlpMetadata": {"$ref": "EnterpriseTopazSidekickNlpMetadata", "description": "Information about the NLP done to get the card."}, "rankingParams": {"$ref": "EnterpriseTopazSidekickRankingParams", "description": "Ranking params."}, "renderMode": {"description": "Render mode.", "enum": ["UNKNOWN_RENDER", "COLLAPSED", "EXPANDED"], "enumDescriptions": ["Unknown mode (default).", "Collapsed.", "Expanded."], "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickCommonDebugInfo": {"description": "Container of debugging information in all object levels. Extend as needed.", "id": "EnterpriseTopazSidekickCommonDebugInfo", "properties": {"message": {"description": "Debug message.", "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickCommonDocument": {"description": "Representation of a document. NEXT_TAG: 15", "id": "EnterpriseTopazSidekickCommonDocument", "properties": {"accessType": {"description": "Access type, i.e., whether the user has access to the document or not.", "enum": ["UNKNOWN_ACCESS", "ALLOWED", "NOT_ALLOWED"], "enumDescriptions": ["Unknown access type.", "Access allowed.", "Access not allowed."], "type": "string"}, "debugInfo": {"$ref": "EnterpriseTopazSidekickCommonDebugInfo", "description": "Information for debugging."}, "documentId": {"description": "Document id.", "type": "string"}, "driveDocumentMetadata": {"$ref": "EnterpriseTopazSidekickCommonDocumentDriveDocumentMetadata", "description": "Drive document metadata."}, "genericUrl": {"description": "Generic Drive-based url in the format of drive.google.com/open to be used for deeplink", "type": "string"}, "justification": {"$ref": "EnterpriseTopazSidekickCommonDocumentJustification", "description": "Justification on why the document is selected."}, "mimeType": {"description": "MIME type", "type": "string"}, "provenance": {"deprecated": true, "description": "Document provenance.", "enum": ["UNKNOWN_PROVENANCE", "CALENDAR_DESCRIPTION", "CALENDAR_ATTACHMENT", "MINED", "CALENDAR_ASSIST_ATTACHMENT"], "enumDescriptions": ["Unknown provenance.", "Calendar event description.", "Calendar event attachment.", "Mined (extracted by some offline/online analysis).", "Attachment created by enterprise assist."], "type": "string"}, "reason": {"deprecated": true, "description": "Justification of why this document is being returned.", "enum": ["UNKNOWN", "TRENDING_IN_COLLABORATORS", "TRENDING_IN_DOMAIN", "FREQUENTLY_VIEWED", "FREQUENTLY_EDITED", "NEW_UPDATES", "NEW_COMMENTS", "EVENT_DESCRIPTION", "EVENT_ATTACHMENT", "EVENT_METADATA_ATTACHMENT", "MINED_DOCUMENT", "NEW_MENTIONS", "NEW_SHARES"], "enumDescriptions": ["Unknown justification.", "Popular documents within collaborators.", "Popular documents within the domain.", "Documents being reviewed frequently by the current user .", "Documents being edited frequently by the current user .", "Documents updated since user's last visit.", "Documents that receive comments since user's last visit.", "Documents in the calendar event description.", "Documents in the calendar event attachments section.", "Documents attached in calendar event metadata instead of the attachment section. Event metadata is not visible to the final user. Enterprise assist uses this metadata to store auto-generated documents such as meeting notes.", "Documents mined, and so, probably related to the request context. For example, this category includes documents related to a meeting.", "Documents that contains mentions of the user.", "Documents that are shared with the user."], "type": "string"}, "snippet": {"description": "A sampling of the text from the document.", "type": "string"}, "thumbnailUrl": {"description": "Thumbnail URL.", "type": "string"}, "title": {"description": "Title of the document.", "type": "string"}, "type": {"description": "Type of the document.", "enum": ["UNKNOWN", "DOCUMENT", "PRESENTATION", "SPREADSHEET", "PDF", "IMAGE", "BINARY_BLOB", "FUSION_TABLE", "FOLDER", "DRAWING", "VIDEO", "FORM", "LINK_URL", "LINK_GO", "LINK_GOO_GL", "LINK_BIT_LY", "LINK_GMAIL", "LINK_MAILTO", "VIDEO_YOUTUBE", "VIDEO_LIVE", "GROUPS", "NEWS", "SITES", "HANGOUT", "AUDIO", "MS_WORD", "MS_POWERPOINT", "MS_EXCEL", "MS_OUTLOOK"], "enumDescriptions": ["If the type is unknown or not represented in this enum.", "Drive document types Writely, Word, etc.", "Presently, PowerPoint, etc.", "Trix, Excel, etc.", "File types for Gdrive objects are below. PDF.", "Image.", "Fall-back for unknown Gdrive types.", "Fusion table.", "Folder.", "Drawing.", "Video.", "Form.", "Link formats uncategorized URL links", "meaningful links that should be renderred specifically", "Link to goo.gl.", "Link to bit_ly.", "Link to Gmail.", "Mailto link.", "Videos Youtube videos.", "Live streams (e.g., liveplayer.googleplex.com)", "Other types. Google Groups.", "Google News.", "Google Sites.", "Google Hangout.", "Audio files.", "Microsoft-specific file types.", "", "", ""], "type": "string"}, "url": {"description": "Absolute URL of the document.", "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickCommonDocumentDriveDocumentMetadata": {"description": "Meta data for drive documents.", "id": "EnterpriseTopazSidekickCommonDocumentDriveDocumentMetadata", "properties": {"documentId": {"deprecated": true, "description": "The drive document cosmo id. Client could use the id to build a URL to open a document. Please use Document.document_id.", "type": "string"}, "isPrivate": {"description": "Additional field to identify whether a document is private since scope set to LIMITED can mean both that the doc is private or that it's shared with others. is_private indicates whether the doc is not shared with anyone except for the owner.", "type": "boolean"}, "lastCommentTimeMs": {"description": "Timestamp of the most recent comment added to the document in milliseconds since epoch.", "format": "uint64", "type": "string"}, "lastEditTimeMs": {"description": "Timestamp of the most recent edit from the current user in milliseconds since epoch.", "format": "uint64", "type": "string"}, "lastModificationTimeMillis": {"description": "Last modification time of the document (independent of the user that modified it).", "format": "int64", "type": "string"}, "lastUpdatedTimeMs": {"description": "Timestamp of the last updated time of the document in milliseconds since epoch.", "format": "uint64", "type": "string"}, "lastViewTimeMs": {"description": "Timestamp of the most recent view from the current user in milliseconds since epoch.", "format": "uint64", "type": "string"}, "owner": {"$ref": "EnterpriseTopazSidekickCommonPerson", "description": "The owner of the document."}, "scope": {"description": "ACL scope of the document which identifies the sharing status of the doc (e.g., limited, shared with link, team drive, ...).", "enum": ["UNKNOWN_DOCUMENT_SCOPE", "LIMITED", "DASHER_DOMAIN_WITH_LINK", "DASHER_DOMAIN", "PUBLIC_WITH_LINK", "PUBLIC", "TEAM_DRIVE"], "enumDescriptions": ["", "", "", "", "", "", ""], "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickCommonDocumentJustification": {"description": "Justification of why we are reporting the document.", "id": "EnterpriseTopazSidekickCommonDocumentJustification", "properties": {"justification": {"description": "A locale aware message that explains why this document was selected.", "type": "string"}, "reason": {"description": "Reason on why the document is selected. Populate for trending documents.", "enum": ["UNKNOWN", "TRENDING_IN_COLLABORATORS", "TRENDING_IN_DOMAIN", "FREQUENTLY_VIEWED", "FREQUENTLY_EDITED", "NEW_UPDATES", "NEW_COMMENTS", "EVENT_DESCRIPTION", "EVENT_ATTACHMENT", "EVENT_METADATA_ATTACHMENT", "MINED_DOCUMENT", "NEW_MENTIONS", "NEW_SHARES"], "enumDescriptions": ["Unknown justification.", "Popular documents within collaborators.", "Popular documents within the domain.", "Documents being reviewed frequently by the current user .", "Documents being edited frequently by the current user .", "Documents updated since user's last visit.", "Documents that receive comments since user's last visit.", "Documents in the calendar event description.", "Documents in the calendar event attachments section.", "Documents attached in calendar event metadata instead of the attachment section. Event metadata is not visible to the final user. Enterprise assist uses this metadata to store auto-generated documents such as meeting notes.", "Documents mined, and so, probably related to the request context. For example, this category includes documents related to a meeting.", "Documents that contains mentions of the user.", "Documents that are shared with the user."], "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickCommonPerson": {"description": "Common representation of a person.", "id": "EnterpriseTopazSidekickCommonPerson", "properties": {"birthday": {"$ref": "EnterpriseTopazSidekickCommonPersonBirthday", "description": "The birthday."}, "cellPhone": {"description": "Cell phone number.", "type": "string"}, "department": {"description": "The department the person works in (e.g. Engineering).", "type": "string"}, "deskLocation": {"description": "Desk location (e.g. US-MTV-PR55-5-5B1I).", "type": "string"}, "deskPhone": {"description": "Work desk phone number.", "type": "string"}, "displayName": {"description": "The full name.", "type": "string"}, "email": {"description": "Email.", "type": "string"}, "familyName": {"description": "The last name.", "type": "string"}, "fullAddress": {"description": "The fully formatted address (e.g. 1255 Pear Avenue, Mountain View 94043, United States).", "type": "string"}, "gaiaId": {"deprecated": true, "description": "This field is deprecated. The obfuscated_id should be used instead.", "format": "int64", "type": "string"}, "givenName": {"description": "The first name.", "type": "string"}, "jobTitle": {"description": "The person's job title (e.g. Software Engineer).", "type": "string"}, "manager": {"$ref": "EnterpriseTopazSidekickCommonPerson", "description": "The manager."}, "obfuscatedId": {"description": "The obfuscated GAIA ID.", "type": "string"}, "photoUrl": {"description": "The URL for the Focus profile picture.", "type": "string"}, "streetAddress": {"description": "The street address (e.g. 1255 Pear Avenue).", "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickCommonPersonBirthday": {"id": "EnterpriseTopazSidekickCommonPersonBirthday", "properties": {"value": {"description": "Unstructured birthday.", "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickConflictingEventsCardProto": {"description": "Conflicting meetings card proto.", "id": "EnterpriseTopazSidekickConflictingEventsCardProto", "properties": {"conflictingEvent": {"description": "All the events that conflict with main_event.", "items": {"$ref": "EnterpriseTopazSidekickAgendaEntry"}, "type": "array"}, "mainEvent": {"$ref": "EnterpriseTopazSidekickAgendaEntry", "description": "The event identified as being the most important."}}, "type": "object"}, "EnterpriseTopazSidekickDocumentGroup": {"description": "Represents a mapping between a document type and its corresponding documents. Use for Work in Progress card in v1 homepage.", "id": "EnterpriseTopazSidekickDocumentGroup", "properties": {"groupType": {"deprecated": true, "description": "Document group type", "enum": ["UNKNOWN_TYPE", "ALL"], "enumDescriptions": ["Unknown type.", "A mix of all the document types."], "type": "string"}, "personalizedDocument": {"description": "The list of corresponding documents.", "items": {"$ref": "EnterpriseTopazSidekickCommonDocument"}, "type": "array"}}, "type": "object"}, "EnterpriseTopazSidekickDocumentPerCategoryList": {"id": "EnterpriseTopazSidekickDocumentPerCategoryList", "properties": {"documents": {"items": {"$ref": "EnterpriseTopazSidekickDocumentPerCategoryListDocumentPerCategoryListEntry"}, "type": "array"}, "helpMessage": {"description": "Localized message explaining how the documents were derived (e.g. from the last 30 days activity). This field is optional.", "type": "string"}, "listType": {"enum": ["UNKNOWN_LIST_TYPE", "MENTIONS", "SHARES", "NEEDS_ATTENTION", "VIEWS", "EDITS"], "enumDescriptions": ["", "All documents in the list correspond to one of the mention categories.", "All documents in the list correspond to one of the share categories.", "A mixture of document categories that correspond to documents that need the users attention (e.g. documents that have been explicitly shared with the user but have not been viewed and documents where the user was mentioned but has not replied).", "All documents in the list correspond to one of the view categories.", "All documents in the list correspond to one of the edit categories."], "type": "string"}, "listTypeDescription": {"description": "Description of the types of documents present in the list.", "type": "string"}, "responseMessage": {"description": "Response message in case no documents are present in the card.", "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickDocumentPerCategoryListDocumentPerCategoryListEntry": {"id": "EnterpriseTopazSidekickDocumentPerCategoryListDocumentPerCategoryListEntry", "properties": {"category": {"enum": ["UNKNOWN_DOCUMENT", "ACTIONABLE", "VIEWED", "REPLIED", "MENTION_VIEWED", "MENTION_REPLIED", "MENTION_NOT_VIEWED", "SHARED_AND_VIEWED", "SHARED_NOT_VIEWED", "EDITED"], "enumDeprecated": [false, true, false, true, false, false, false, false, false, false], "enumDescriptions": ["", "", "", "", "Mention categories. The mention has been viewed by the user, but the user has not replied.", "The user has replied to the mention.", "The mention has not been viewed by the user.", "Share categories. Consists of documents that have been explicitly shared with the user. Document has been viewed.", "Document has not been viewed.", "Document has been edited."], "type": "string"}, "document": {"$ref": "EnterpriseTopazSidekickCommonDocument"}, "rationale": {"description": "Reason this document was selected.", "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickFindMeetingTimeCardProto": {"description": "Response to find meeting time among a set of people.", "id": "EnterpriseTopazSidekickFindMeetingTimeCardProto", "properties": {"commonAvailableTimeSlots": {"description": "Slots when all attendees have availability.", "items": {"$ref": "EnterpriseTopazSidekickTimeSlot"}, "type": "array"}, "invitees": {"description": "Invitees to the event.", "items": {"$ref": "EnterpriseTopazSidekickPerson"}, "type": "array"}, "requester": {"$ref": "EnterpriseTopazSidekickPerson", "description": "Requester."}, "scheduledMeeting": {"$ref": "EnterpriseTopazSidekickScheduledMeeting", "description": "Details about the scheduled meeting, if one exists."}, "skippedInvitees": {"description": "Invitees that have been skipped in the computation, most likely because they are groups.", "items": {"$ref": "EnterpriseTopazSidekickPerson"}, "type": "array"}, "timeBoundaries": {"$ref": "EnterpriseTopazSidekickTimeSlot", "description": "Min and max timestamp used to find a common available timeslot."}, "timezoneId": {"description": "Timezone ID.", "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickGap": {"id": "EnterpriseTopazSidekickGap", "properties": {"displayRemainingTime": {"description": "Localized time string in the format: 1 hour 15 minutes", "type": "string"}, "endTime": {"description": "Localized time string in the format:(Locale CZ) 8:30 odp.", "type": "string"}, "endTimeMs": {"format": "uint64", "type": "string"}, "remainingTime": {"format": "google-duration", "type": "string"}, "startTime": {"description": "Localized time string in the format:(Locale CZ) 8:30 odp.", "type": "string"}, "startTimeMs": {"format": "uint64", "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickGenericAnswerCard": {"id": "EnterpriseTopazSidekickGenericAnswerCard", "properties": {"answer": {"description": "The answer.", "type": "string"}, "title": {"description": "Title or header of the card.", "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickGetAndKeepAheadCardProto": {"description": "Get and keep ahead card", "id": "EnterpriseTopazSidekickGetAndKeepAheadCardProto", "properties": {"declinedEvents": {"$ref": "EnterpriseTopazSidekickGetAndKeepAheadCardProtoDeclinedEvents"}, "mentionedDocuments": {"$ref": "EnterpriseTopazSidekickDocumentPerCategoryList"}, "sharedDocuments": {"$ref": "EnterpriseTopazSidekickDocumentPerCategoryList"}}, "type": "object"}, "EnterpriseTopazSidekickGetAndKeepAheadCardProtoDeclinedEvents": {"description": "A list of events where all guests declined.", "id": "EnterpriseTopazSidekickGetAndKeepAheadCardProtoDeclinedEvents", "properties": {"events": {"items": {"$ref": "EnterpriseTopazSidekickAgendaEntry"}, "type": "array"}}, "type": "object"}, "EnterpriseTopazSidekickMeetingNotesCardError": {"description": "Errors in the creation of meeting notes.", "id": "EnterpriseTopazSidekickMeetingNotesCardError", "properties": {"description": {"description": "The description of the reason why create-meeting-notes failed.", "type": "string"}, "event": {"$ref": "EnterpriseTopazSidekickAgendaEntry", "description": "The event to request meeting notes creation"}, "reason": {"description": "The reason why create-meeting-notes failed.", "enum": ["NONE", "NOT_OWNER", "UNKNOWN"], "enumDescriptions": ["No reason (default value).", "The user is not an owner.", "Unknown reason."], "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickMeetingNotesCardProto": {"description": "Information about the meeting notes created.", "id": "EnterpriseTopazSidekickMeetingNotesCardProto", "properties": {"event": {"$ref": "EnterpriseTopazSidekickAgendaEntry", "description": "The event to request meeting notes creation."}, "fileId": {"description": "Google Drive ID (a.k.a. resource ID) of the file.", "type": "string"}, "title": {"description": "Title we want to show for meeting notes in the answer card", "type": "string"}, "url": {"description": "New URL.", "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickMeetingNotesCardRequest": {"description": "Meeting notes card request.", "id": "EnterpriseTopazSidekickMeetingNotesCardRequest", "properties": {"canCreateFor": {"description": "Who are the meeting notes created for.", "items": {"enum": ["UNKNOWN", "MYSELF", "ALL_ATTENDEES"], "enumDescriptions": ["Unknown (default).", "For the requester.", "For all the meeting attendees."], "type": "string"}, "type": "array"}, "error": {"$ref": "EnterpriseTopazSidekickMeetingNotesCardError", "description": "The error and reason if known error occured."}, "event": {"$ref": "EnterpriseTopazSidekickAgendaEntry", "description": "The event to request meeting notes creation"}}, "type": "object"}, "EnterpriseTopazSidekickNlpMetadata": {"description": "<PERSON><PERSON><PERSON> about the NLP interpretation of the query.", "id": "EnterpriseTopazSidekickNlpMetadata", "properties": {"confidence": {"description": "Confidence of the interpretation that generated this card.", "format": "float", "type": "number"}}, "type": "object"}, "EnterpriseTopazSidekickPeopleAnswerDisambiguationInfo": {"description": "Metadata for disambiguation.", "id": "EnterpriseTopazSidekickPeopleAnswerDisambiguationInfo", "properties": {"disambiguation": {"description": "A list of people that also matched the query. This list is not complete.", "items": {"$ref": "EnterpriseTopazSidekickPeopleAnswerDisambiguationInfoDisambiguationPerson"}, "type": "array"}, "name": {"description": "The name that was extracted from the query. This may be in the form of the given name, last name, full name, LDAP, or email address. This name can be considered suitable for displaying to the user and can largely be considered to be normalized (e.g. \"<PERSON>'s\" -> \"<PERSON>\").", "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickPeopleAnswerDisambiguationInfoDisambiguationPerson": {"description": "A person that also matches the query, but was not selected due to a lower affinity with the requesting user.", "id": "EnterpriseTopazSidekickPeopleAnswerDisambiguationInfoDisambiguationPerson", "properties": {"person": {"$ref": "EnterpriseTopazSidekickCommonPerson", "description": "The profile of this person."}, "query": {"description": "The query that can be used to produce an answer card with the same attribute, but for this person.", "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickPeopleAnswerPeopleAnswerCardHeader": {"description": "Recommended header to display for the card.", "id": "EnterpriseTopazSidekickPeopleAnswerPeopleAnswerCardHeader", "properties": {"title": {"description": "The suggested title to display. This defaults to the user's query.", "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickPeopleAnswerPersonAnswerCard": {"description": "An answer card for a single person.", "id": "EnterpriseTopazSidekickPeopleAnswerPersonAnswerCard", "properties": {"answer": {"deprecated": true, "description": "List of answers.", "items": {"$ref": "SafeHtmlProto"}, "type": "array"}, "answerText": {"$ref": "EnterpriseTopazSidekickAnswerAnswerList", "description": "List of answers."}, "disambiguationInfo": {"$ref": "EnterpriseTopazSidekickPeopleAnswerDisambiguationInfo", "description": "Disambiguation information."}, "header": {"$ref": "EnterpriseTopazSidekickPeopleAnswerPeopleAnswerCardHeader", "description": "The header to display for the card."}, "responseStatus": {"description": "The response status.", "enum": ["UNKNOWN", "SUCCESS", "MISSING_PERSON", "MISSING_DATA"], "enumDescriptions": ["Unknown.", "Success.", "No such person was found in the user's domain.", "A person was found to match the query, but an answer could not be obtained."], "type": "string"}, "statusMessage": {"description": "Localized user friendly message to display to the user in the case of missing data or an error.", "type": "string"}, "subject": {"$ref": "EnterpriseTopazSidekickCommonPerson", "description": "The profile of the person that was the subject of the query."}}, "type": "object"}, "EnterpriseTopazSidekickPeopleAnswerRelatedPeopleAnswerCard": {"description": "An answer card for a list of people that are related to the subject of the query.", "id": "EnterpriseTopazSidekickPeopleAnswerRelatedPeopleAnswerCard", "properties": {"disambiguationInfo": {"$ref": "EnterpriseTopazSidekickPeopleAnswerDisambiguationInfo", "description": "Disambiguation information."}, "header": {"$ref": "EnterpriseTopazSidekickPeopleAnswerPeopleAnswerCardHeader", "description": "The header to display for the card."}, "relatedPeople": {"description": "A list of people that are related to the query subject.", "items": {"$ref": "EnterpriseTopazSidekickCommonPerson"}, "type": "array"}, "relationType": {"description": "Defines the type of relation the list of people have with the subject of the card.", "enum": ["UNKNOWN", "DIRECT_REPORTS", "MANAGER", "PEERS"], "enumDescriptions": ["Unknown.", "Direct reports.", "The manager.", "The teammates/peers of the subject."], "type": "string"}, "responseStatus": {"description": "The response status.", "enum": ["UNKNOWN", "SUCCESS", "MISSING_PERSON", "MISSING_DATA"], "enumDescriptions": ["Unknown.", "Success.", "No such person was found in the user's domain.", "A person was found to match the query, but an answer could not be obtained."], "type": "string"}, "statusMessage": {"description": "Localized user friendly message to display to the user in the case of missing data or an error.", "type": "string"}, "subject": {"$ref": "EnterpriseTopazSidekickCommonPerson", "description": "The profile of the person that was the subject of the query."}}, "type": "object"}, "EnterpriseTopazSidekickPeopleDisambiguationCard": {"id": "EnterpriseTopazSidekickPeopleDisambiguationCard", "properties": {"person": {"description": "Candidate persons for the query.", "items": {"$ref": "EnterpriseTopazSidekickCommonPerson"}, "type": "array"}}, "type": "object"}, "EnterpriseTopazSidekickPerson": {"description": "Person.", "id": "EnterpriseTopazSidekickPerson", "properties": {"affinityLevel": {"description": "The level of affinity this person has with the requesting user.", "enum": ["UNKNOWN", "LOW", "MEDIUM", "HIGH"], "enumDescriptions": ["", "", "", ""], "type": "string"}, "attendingStatus": {"description": "Attendance status of the person when included in a meeting event.", "enum": ["AWAITING", "YES", "NO", "MAYBE"], "enumDescriptions": ["Awaiting for the user to set the status.", "Attending.", "Not attending.", "Tentatively attending."], "type": "string"}, "email": {"description": "Email.", "type": "string"}, "gaiaId": {"deprecated": true, "description": "Gaia id.", "format": "int64", "type": "string"}, "isGroup": {"description": "Whether the invitee is a group.", "type": "boolean"}, "name": {"description": "Name.", "type": "string"}, "obfuscatedGaiaId": {"description": "Obfuscated Gaia id.", "type": "string"}, "photoUrl": {"description": "Absolute URL to the profile photo of the person.", "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickPersonProfileCard": {"id": "EnterpriseTopazSidekickPersonProfileCard", "properties": {"relatedPeople": {"items": {"$ref": "EnterpriseTopazSidekickPersonProfileCardRelatedPeople"}, "type": "array"}, "subject": {"$ref": "EnterpriseTopazSidekickCommonPerson", "description": "The subject of the card."}}, "type": "object"}, "EnterpriseTopazSidekickPersonProfileCardRelatedPeople": {"id": "EnterpriseTopazSidekickPersonProfileCardRelatedPeople", "properties": {"relatedPerson": {"description": "Related people.", "items": {"$ref": "EnterpriseTopazSidekickCommonPerson"}, "type": "array"}, "relation": {"description": "Relation type.", "enum": ["UNKNOWN", "MANAGER", "DIRECT_REPORT"], "enumDescriptions": ["", "", ""], "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickPersonalizedDocsCardProto": {"description": "Personalized docs card proto.", "id": "EnterpriseTopazSidekickPersonalizedDocsCardProto", "properties": {"documentGroup": {"description": "Document group.", "items": {"$ref": "EnterpriseTopazSidekickDocumentGroup"}, "type": "array"}}, "type": "object"}, "EnterpriseTopazSidekickRankingParams": {"description": "Ranking params.", "id": "EnterpriseTopazSidekickRankingParams", "properties": {"endTimeMs": {"description": "The end-time that this object will expect to occur. If the type is marked as FIXED, then this end-time will persist after bidding. If the type is marked as FLEXIBLE, this field is NOT expected to be filled and will be filled in after it has won a bid. Expected to be set when type is set to FIXED.", "format": "uint64", "type": "string"}, "priority": {"description": "The priority to determine between objects that have the same start_time_ms The lower-value of priority == ranked higher. Max-priority = 0. Expected to be set for all types.", "enum": ["UNKNOWN", "CRITICAL", "IMPORTANT", "HIGH", "NORMAL", "BEST_EFFORT"], "enumDescriptions": ["Unknown (default).", "Critical.", "Important.", "High.", "Normal.", "Best effort."], "type": "string"}, "score": {"description": "The score of the card to be used to break priority-ties", "format": "float", "type": "number"}, "spanMs": {"description": "The span that this card will take in the stream Expected to be set when type is set to FLEXIBLE.", "format": "uint64", "type": "string"}, "startTimeMs": {"description": "The start-time that this object will bid-for If the type is marked as FIXED, then this start-time will persist after bidding. If the type is marked as FLEXIBLE, then it will occur at the given time or sometime after the requested time. Expected to be set for all types.", "format": "uint64", "type": "string"}, "type": {"description": "The packing type of this object.", "enum": ["FIXED", "FLEXIBLE"], "enumDescriptions": ["Fixed, i.e., the card is time sensitive.", "Flexible, i.e., the card is not time sensitive."], "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickRecentDocumentsCardProto": {"id": "EnterpriseTopazSidekickRecentDocumentsCardProto", "properties": {"document": {"items": {"$ref": "EnterpriseTopazSidekickCommonDocument"}, "type": "array"}}, "type": "object"}, "EnterpriseTopazSidekickScheduledMeeting": {"description": "Details about scheduled meetings.", "id": "EnterpriseTopazSidekickScheduledMeeting", "properties": {"meetingLocation": {"description": "The meeting location.", "type": "string"}, "meetingTime": {"$ref": "EnterpriseTopazSidekickTimeSlot", "description": "The meeting time slot."}, "meetingTitle": {"description": "The meeting title.", "type": "string"}}, "type": "object"}, "EnterpriseTopazSidekickShareMeetingDocsCardProto": {"description": "Share meeting docs card proto.", "id": "EnterpriseTopazSidekickShareMeetingDocsCardProto", "properties": {"document": {"description": "Documents to share for the given meeting.", "items": {"$ref": "EnterpriseTopazSidekickCommonDocument"}, "type": "array"}, "event": {"$ref": "EnterpriseTopazSidekickAgendaEntry", "description": "Event."}}, "type": "object"}, "EnterpriseTopazSidekickTimeSlot": {"description": "Slot of time.", "id": "EnterpriseTopazSidekickTimeSlot", "properties": {"endTimeDay": {"description": "Day end time at the user's timezone.", "type": "string"}, "endTimeHourAndMinute": {"description": "Hour and minute of the end time at the user's timezone.", "type": "string"}, "endTimeInMillis": {"description": "End time in milliseconds.", "format": "int64", "type": "string"}, "startTimeDay": {"description": "Day start time at user's timezone.", "type": "string"}, "startTimeHourAndMinute": {"description": "Hour and minute of the start time at the user's timezone.", "type": "string"}, "startTimeInMillis": {"description": "Start time in milliseconds.", "format": "int64", "type": "string"}}, "type": "object"}, "EnumOperatorOptions": {"description": "Used to provide a search operator for enum properties. This is optional. Search operators let users restrict the query to specific fields relevant to the type of item being searched. For example, if you provide no operator for a *priority* enum property with possible values *p0* and *p1*, a query that contains the term *p0* returns items that have *p0* as the value of the *priority* property, as well as any items that contain the string *p0* in other fields. If you provide an operator name for the enum, such as *priority*, then search users can use that operator to refine results to only items that have *p0* as this property's value, with the query *priority:p0*.", "id": "EnumOperatorOptions", "properties": {"operatorName": {"description": "Indicates the operator name required in the query in order to isolate the enum property. For example, if operator<PERSON>ame is *priority* and the property's name is *priorityVal*, then queries like *priority:<value>* show results only where the value of the property named *priorityVal* matches *<value>*. By contrast, a search that uses the same *<value>* without an operator returns all items where *<value>* matches the value of any String properties or text within the content field for the item. The operator name can only contain lowercase letters (a-z). The maximum length is 32 characters.", "type": "string"}}, "type": "object"}, "EnumPropertyOptions": {"description": "The options for enum properties, which allow you to define a restricted set of strings to match user queries, set rankings for those string values, and define an operator name to be paired with those strings so that users can narrow results to only items with a specific value. For example, for items in a request tracking system with priority information, you could define *p0* as an allowable enum value and tie this enum to the operator name *priority* so that search users could add *priority:p0* to their query to restrict the set of results to only those items indexed with the value *p0*.", "id": "EnumPropertyOptions", "properties": {"operatorOptions": {"$ref": "EnumOperatorOptions", "description": "If set, describes how the enum should be used as a search operator."}, "orderedRanking": {"description": "Used to specify the ordered ranking for the enumeration that determines how the integer values provided in the possible EnumValuePairs are used to rank results. If specified, integer values must be provided for all possible EnumValuePair values given for this property. Can only be used if isRepeatable is false.", "enum": ["NO_ORDER", "ASCENDING", "DESCENDING"], "enumDescriptions": ["There is no ranking order for the property. Results aren't adjusted by this property's value.", "This property is ranked in ascending order. Lower values indicate lower ranking.", "This property is ranked in descending order. Lower values indicate higher ranking."], "type": "string"}, "possibleValues": {"description": "The list of possible values for the enumeration property. All EnumValuePairs must provide a string value. If you specify an integer value for one EnumValuePair, then all possible EnumValuePairs must provide an integer value. Both the string value and integer value must be unique over all possible values. Once set, possible values cannot be removed or modified. If you supply an ordered ranking and think you might insert additional enum values in the future, leave gaps in the initial integer values to allow adding a value in between previously registered values. The maximum number of elements is 100.", "items": {"$ref": "EnumValuePair"}, "type": "array"}}, "type": "object"}, "EnumValuePair": {"description": "The enumeration value pair defines two things: a required string value and an optional integer value. The string value defines the necessary query term required to retrieve that item, such as *p0* for a priority item. The integer value determines the ranking of that string value relative to other enumerated values for the same property. For example, you might associate *p0* with *0* and define another enum pair such as *p1* and *1*. You must use the integer value in combination with ordered ranking to set the ranking of a given value relative to other enumerated values for the same property name. Here, a ranking order of DESCENDING for *priority* properties results in a ranking boost for items indexed with a value of *p0* compared to items indexed with a value of *p1*. Without a specified ranking order, the integer value has no effect on item ranking.", "id": "EnumValuePair", "properties": {"integerValue": {"description": "The integer value of the EnumValuePair which must be non-negative. Optional.", "format": "int32", "type": "integer"}, "stringValue": {"description": "The string value of the EnumValuePair. The maximum length is 32 characters.", "type": "string"}}, "type": "object"}, "EnumValues": {"description": "List of enum values.", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"values": {"description": "The maximum allowable length for string values is 32 characters.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ErrorInfo": {"description": "Error information about the response.", "id": "ErrorInfo", "properties": {"errorMessages": {"items": {"$ref": "ErrorMessage"}, "type": "array"}}, "type": "object"}, "ErrorMessage": {"description": "Error message per source response.", "id": "ErrorMessage", "properties": {"errorMessage": {"type": "string"}, "source": {"$ref": "Source"}}, "type": "object"}, "FacetBucket": {"description": "A bucket in a facet is the basic unit of operation. A bucket can comprise either a single value OR a contiguous range of values, depending on the type of the field bucketed. FacetBucket is currently used only for returning the response object.", "id": "FacetBucket", "properties": {"count": {"description": "Number of results that match the bucket value. Counts are only returned for searches when count accuracy is ensured. Cloud Search does not guarantee facet counts for any query and facet counts might be present only intermittently, even for identical queries. Do not build dependencies on facet count existence; instead use facet ount percentages which are always returned.", "format": "int32", "type": "integer"}, "filter": {"$ref": "Filter", "description": "Filter to be passed in the search request if the corresponding bucket is selected."}, "percentage": {"description": "Percent of results that match the bucket value. The returned value is between (0-100], and is rounded down to an integer if fractional. If the value is not explicitly returned, it represents a percentage value that rounds to 0. Percentages are returned for all searches, but are an estimate. Because percentages are always returned, you should render percentages instead of counts.", "format": "int32", "type": "integer"}, "value": {"$ref": "Value"}}, "type": "object"}, "FacetOptions": {"description": "Specifies operators to return facet results for. There will be one FacetResult for every source_name/object_type/operator_name combination.", "id": "FacetOptions", "properties": {"integerFacetingOptions": {"$ref": "IntegerFacetingOptions", "description": "If set, describes integer faceting options for the given integer property. The corresponding integer property in the schema should be marked isFacetable. The number of buckets returned would be minimum of this and num_facet_buckets."}, "numFacetBuckets": {"description": "Maximum number of facet buckets that should be returned for this facet. Defaults to 10. Maximum value is 100.", "format": "int32", "type": "integer"}, "objectType": {"description": "If object_type is set, only those objects of that type will be used to compute facets. If empty, then all objects will be used to compute facets.", "type": "string"}, "operatorName": {"description": "The name of the operator chosen for faceting. @see cloudsearch.SchemaPropertyOptions", "type": "string"}, "sourceName": {"description": "Source name to facet on. Format: datasources/{source_id} If empty, all data sources will be used.", "type": "string"}}, "type": "object"}, "FacetResult": {"description": "Source specific facet response", "id": "FacetResult", "properties": {"buckets": {"description": "FacetBuckets for values in response containing at least a single result with the corresponding filter.", "items": {"$ref": "FacetBucket"}, "type": "array"}, "objectType": {"description": "Object type for which facet results are returned. Can be empty.", "type": "string"}, "operatorName": {"description": "The name of the operator chosen for faceting. @see cloudsearch.SchemaPropertyOptions", "type": "string"}, "sourceName": {"description": "Source name for which facet results are returned. Will not be empty.", "type": "string"}}, "type": "object"}, "FieldViolation": {"id": "FieldViolation", "properties": {"description": {"description": "The description of the error.", "type": "string"}, "field": {"description": "Path of field with violation.", "type": "string"}}, "type": "object"}, "Filter": {"description": "A generic way of expressing filters in a query, which supports two approaches: **1. Setting a ValueFilter.** The name must match an operator_name defined in the schema for your data source. **2. Setting a CompositeFilter.** The filters are evaluated using the logical operator. The top-level operators can only be either an AND or a NOT. AND can appear only at the top-most level. OR can appear only under a top-level AND.", "id": "Filter", "properties": {"compositeFilter": {"$ref": "CompositeFilter"}, "valueFilter": {"$ref": "ValueFilter"}}, "type": "object"}, "FilterOptions": {"description": "Filter options to be applied on query.", "id": "FilterOptions", "properties": {"filter": {"$ref": "Filter", "description": "Generic filter to restrict the search, such as `lang:en`, `site:xyz`."}, "objectType": {"description": "If object_type is set, only objects of that type are returned. This should correspond to the name of the object that was registered within the definition of schema. The maximum length is 256 characters.", "type": "string"}}, "type": "object"}, "FreshnessOptions": {"description": "Indicates which freshness property to use when adjusting search ranking for an item. Fresher, more recent dates indicate higher quality. Use the freshness option property that best works with your data. For fileshare documents, last modified time is most relevant. For calendar event data, the time when the event occurs is a more relevant freshness indicator. In this way, calendar events that occur closer to the time of the search query are considered higher quality and ranked accordingly.", "id": "FreshnessOptions", "properties": {"freshnessDuration": {"description": "The duration after which an object should be considered stale. The default value is 180 days (in seconds).", "format": "google-duration", "type": "string"}, "freshnessProperty": {"description": "This property indicates the freshness level of the object in the index. If set, this property must be a top-level property within the property definitions and it must be a timestamp type or date type. Otherwise, the Indexing API uses updateTime as the freshness indicator. The maximum length is 256 characters. When a property is used to calculate freshness, the value defaults to 2 years from the current time.", "type": "string"}}, "type": "object"}, "GSuitePrincipal": {"id": "GSuitePrincipal", "properties": {"gsuiteDomain": {"description": "This principal represents all users of the Google Workspace domain of the customer.", "type": "boolean"}, "gsuiteGroupEmail": {"description": "This principal references a Google Workspace group name.", "type": "string"}, "gsuiteUserEmail": {"description": "This principal references a Google Workspace user account.", "type": "string"}}, "type": "object"}, "GetCustomerIndexStatsResponse": {"id": "GetCustomerIndexStatsResponse", "properties": {"averageIndexedItemCount": {"description": "Average item count for the given date range for which billing is done.", "format": "int64", "type": "string"}, "stats": {"description": "Summary of indexed item counts, one for each day in the requested range.", "items": {"$ref": "CustomerIndexStats"}, "type": "array"}}, "type": "object"}, "GetCustomerQueryStatsResponse": {"id": "GetCustomerQueryStatsResponse", "properties": {"stats": {"items": {"$ref": "CustomerQueryStats"}, "type": "array"}, "totalQueryCount": {"description": "Total successful query count (status code 200) for the given date range.", "format": "int64", "type": "string"}}, "type": "object"}, "GetCustomerSearchApplicationStatsResponse": {"description": "Response format for search application stats for a customer.", "id": "GetCustomerSearchApplicationStatsResponse", "properties": {"averageSearchApplicationCount": {"description": "Average search application count for the given date range.", "format": "int64", "type": "string"}, "stats": {"description": "Search application stats by date.", "items": {"$ref": "CustomerSearchApplicationStats"}, "type": "array"}}, "type": "object"}, "GetCustomerSessionStatsResponse": {"id": "GetCustomerSessionStatsResponse", "properties": {"stats": {"items": {"$ref": "CustomerSessionStats"}, "type": "array"}}, "type": "object"}, "GetCustomerUserStatsResponse": {"id": "GetCustomerUserStatsResponse", "properties": {"stats": {"items": {"$ref": "CustomerUserStats"}, "type": "array"}}, "type": "object"}, "GetDataSourceIndexStatsResponse": {"id": "GetDataSourceIndexStatsResponse", "properties": {"averageIndexedItemCount": {"description": "Average item count for the given date range for which billing is done.", "format": "int64", "type": "string"}, "stats": {"description": "Summary of indexed item counts, one for each day in the requested range.", "items": {"$ref": "DataSourceIndexStats"}, "type": "array"}}, "type": "object"}, "GetSearchApplicationQueryStatsResponse": {"description": "Response format for getting query stats for a search application between given dates.", "id": "GetSearchApplicationQueryStatsResponse", "properties": {"stats": {"description": "Query stats per date for a search application.", "items": {"$ref": "SearchApplicationQueryStats"}, "type": "array"}, "totalQueryCount": {"description": "Total successful query count (status code 200) for the given date range.", "format": "int64", "type": "string"}}, "type": "object"}, "GetSearchApplicationSessionStatsResponse": {"id": "GetSearchApplicationSessionStatsResponse", "properties": {"stats": {"items": {"$ref": "SearchApplicationSessionStats"}, "type": "array"}}, "type": "object"}, "GetSearchApplicationUserStatsResponse": {"id": "GetSearchApplicationUserStatsResponse", "properties": {"stats": {"items": {"$ref": "SearchApplicationUserStats"}, "type": "array"}}, "type": "object"}, "HtmlOperatorOptions": {"description": "Used to provide a search operator for html properties. This is optional. Search operators let users restrict the query to specific fields relevant to the type of item being searched.", "id": "HtmlOperatorOptions", "properties": {"operatorName": {"description": "Indicates the operator name required in the query in order to isolate the html property. For example, if operator<PERSON>ame is *subject* and the property's name is *subjectLine*, then queries like *subject:<value>* show results only where the value of the property named *subjectLine* matches *<value>*. By contrast, a search that uses the same *<value>* without an operator return all items where *<value>* matches the value of any html properties or text within the content field for the item. The operator name can only contain lowercase letters (a-z). The maximum length is 32 characters.", "type": "string"}}, "type": "object"}, "HtmlPropertyOptions": {"description": "The options for html properties.", "id": "HtmlPropertyOptions", "properties": {"operatorOptions": {"$ref": "HtmlOperatorOptions", "description": "If set, describes how the property should be used as a search operator."}, "retrievalImportance": {"$ref": "RetrievalImportance", "description": "Indicates the search quality importance of the tokens within the field when used for retrieval. Can only be set to DEFAULT or NONE."}}, "type": "object"}, "HtmlValues": {"description": "List of html values.", "id": "HtmlValues", "properties": {"values": {"description": "The maximum allowable length for html values is 2048 characters.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "IndexItemOptions": {"id": "IndexItemOptions", "properties": {"allowUnknownGsuitePrincipals": {"description": "Specifies if the index request should allow Google Workspace principals that do not exist or are deleted.", "type": "boolean"}}, "type": "object"}, "IndexItemRequest": {"id": "IndexItemRequest", "properties": {"connectorName": {"description": "The name of connector making this call. Format: datasources/{source_id}/connectors/{ID}", "type": "string"}, "debugOptions": {"$ref": "DebugOptions", "description": "Common debug options."}, "indexItemOptions": {"$ref": "IndexItemOptions"}, "item": {"$ref": "<PERSON><PERSON>", "description": "The name of the item. Format: datasources/{source_id}/items/{item_id}"}, "mode": {"description": "Required. The RequestMode for this request.", "enum": ["UNSPECIFIED", "SYNCHRONOUS", "ASYNCHRONOUS"], "enumDescriptions": ["The priority is not specified in the update request. Leaving priority unspecified results in an update failure.", "For real-time updates.", "For changes that are executed after the response is sent back to the caller."], "type": "string"}}, "type": "object"}, "InitializeCustomerRequest": {"description": "Request message for `InitializeCustomer` method.", "id": "InitializeCustomerRequest", "properties": {}, "type": "object"}, "IntegerFacetingOptions": {"description": "Used to specify integer faceting options.", "id": "IntegerFacetingOptions", "properties": {"integerBuckets": {"description": "Buckets for given integer values should be in strictly ascending order. For example, if values supplied are (1,5,10,100), the following facet buckets will be formed {<1, [1,5), [5-10), [10-100), >=100}.", "items": {"format": "int64", "type": "string"}, "type": "array"}}, "type": "object"}, "IntegerOperatorOptions": {"description": "Used to provide a search operator for integer properties. This is optional. Search operators let users restrict the query to specific fields relevant to the type of item being searched.", "id": "IntegerOperatorOptions", "properties": {"greaterThanOperatorName": {"description": "Indicates the operator name required in the query in order to isolate the integer property using the greater-than operator. For example, if greaterThanOperatorName is *priorityabove* and the property's name is *priorityVal*, then queries like *priorityabove:<value>* show results only where the value of the property named *priorityVal* is greater than *<value>*. The operator name can only contain lowercase letters (a-z). The maximum length is 32 characters.", "type": "string"}, "lessThanOperatorName": {"description": "Indicates the operator name required in the query in order to isolate the integer property using the less-than operator. For example, if lessThanOperatorName is *prioritybelow* and the property's name is *priorityVal*, then queries like *prioritybelow:<value>* show results only where the value of the property named *priorityVal* is less than *<value>*. The operator name can only contain lowercase letters (a-z). The maximum length is 32 characters.", "type": "string"}, "operatorName": {"description": "Indicates the operator name required in the query in order to isolate the integer property. For example, if operator<PERSON><PERSON> is *priority* and the property's name is *priorityVal*, then queries like *priority:<value>* show results only where the value of the property named *priorityVal* matches *<value>*. By contrast, a search that uses the same *<value>* without an operator returns all items where *<value>* matches the value of any String properties or text within the content field for the item. The operator name can only contain lowercase letters (a-z). The maximum length is 32 characters.", "type": "string"}}, "type": "object"}, "IntegerPropertyOptions": {"description": "The options for integer properties.", "id": "IntegerPropertyOptions", "properties": {"integerFacetingOptions": {"$ref": "IntegerFacetingOptions", "description": "If set, describes integer faceting options for the given integer property. The corresponding integer property should be marked isFacetable."}, "maximumValue": {"description": "The maximum value of the property. The minimum and maximum values for the property are used to rank results according to the ordered ranking. Indexing requests with values greater than the maximum are accepted and ranked with the same weight as items indexed with the maximum value.", "format": "int64", "type": "string"}, "minimumValue": {"description": "The minimum value of the property. The minimum and maximum values for the property are used to rank results according to the ordered ranking. Indexing requests with values less than the minimum are accepted and ranked with the same weight as items indexed with the minimum value.", "format": "int64", "type": "string"}, "operatorOptions": {"$ref": "IntegerOperatorOptions", "description": "If set, describes how the integer should be used as a search operator."}, "orderedRanking": {"description": "Used to specify the ordered ranking for the integer. Can only be used if isRepeatable is false.", "enum": ["NO_ORDER", "ASCENDING", "DESCENDING"], "enumDescriptions": ["There is no ranking order for the property. Results are not adjusted by this property's value.", "This property is ranked in ascending order. Lower values indicate lower ranking.", "This property is ranked in descending order. Lower values indicate higher ranking."], "type": "string"}}, "type": "object"}, "IntegerValues": {"description": "List of integer values.", "id": "IntegerValues", "properties": {"values": {"items": {"format": "int64", "type": "string"}, "type": "array"}}, "type": "object"}, "Interaction": {"description": "Represents an interaction between a user and an item.", "id": "Interaction", "properties": {"interactionTime": {"description": "The time when the user acted on the item. If multiple actions of the same type exist for a single user, only the most recent action is recorded.", "format": "google-datetime", "type": "string"}, "principal": {"$ref": "Principal", "description": "The user that acted on the item."}, "type": {"enum": ["UNSPECIFIED", "VIEW", "EDIT"], "enumDescriptions": ["Invalid value.", "This interaction indicates the user viewed the item.", "This interaction indicates the user edited the item."], "type": "string"}}, "type": "object"}, "Item": {"description": "Represents a single object that is an item in the search index, such as a file, folder, or a database record.", "id": "<PERSON><PERSON>", "properties": {"acl": {"$ref": "ItemAcl", "description": "Access control list for this item."}, "content": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Item content to be indexed and made text searchable."}, "itemType": {"description": "The type for this item.", "enum": ["UNSPECIFIED", "CONTENT_ITEM", "CONTAINER_ITEM", "VIRTUAL_CONTAINER_ITEM"], "enumDescriptions": ["", "An item that is indexed for the only purpose of serving information. These items cannot be referred in containerName or inheritAclFrom fields.", "An item that gets indexed and whose purpose is to supply other items with ACLs and/or contain other items.", "An item that does not get indexed, but otherwise has the same purpose as CONTAINER_ITEM."], "type": "string"}, "metadata": {"$ref": "ItemMetadata", "description": "The metadata information."}, "name": {"description": "The name of the Item. Format: datasources/{source_id}/items/{item_id} This is a required field. The maximum length is 1536 characters.", "type": "string"}, "payload": {"description": "Additional state connector can store for this item. The maximum length is 10000 bytes.", "format": "byte", "type": "string"}, "queue": {"description": "Queue this item belongs to. The maximum length is 100 characters.", "type": "string"}, "status": {"$ref": "ItemStatus", "description": "Status of the item. Output only field."}, "structuredData": {"$ref": "ItemStructuredData", "description": "The structured data for the item that should conform to a registered object definition in the schema for the data source."}, "version": {"description": "Required. The indexing system stores the version from the datasource as a byte string and compares the Item version in the index to the version of the queued Item using lexical ordering. Cloud Search Indexing won't index or delete any queued item with a version value that is less than or equal to the version of the currently indexed item. The maximum length for this field is 1024 bytes. For information on how item version affects the deletion process, refer to [Handle revisions after manual deletes](https://developers.google.com/cloud-search/docs/guides/operations).", "format": "byte", "type": "string"}}, "type": "object"}, "ItemAcl": {"description": "Access control list information for the item. For more information see [Map ACLs](https://developers.google.com/cloud-search/docs/guides/acls).", "id": "ItemAcl", "properties": {"aclInheritanceType": {"description": "Sets the type of access rules to apply when an item inherits its ACL from a parent. This should always be set in tandem with the inheritAclFrom field. Also, when the inheritAclFrom field is set, this field should be set to a valid AclInheritanceType.", "enum": ["NOT_APPLICABLE", "CHILD_OVERRIDE", "PARENT_OVERRIDE", "BOTH_PERMIT"], "enumDescriptions": ["The default value when this item does not inherit an ACL. Use NOT_APPLICABLE when inheritAclFrom is empty. An item without ACL inheritance can still have ACLs supplied by its own readers and deniedReaders fields.", "During an authorization conflict, the ACL of the child item determines its read access.", "During an authorization conflict, the ACL of the parent item specified in the inheritAclFrom field determines read access.", "Access is granted only if this item and the parent item specified in the inheritAclFrom field both permit read access."], "type": "string"}, "deniedReaders": {"description": "List of principals who are explicitly denied access to the item in search results. While principals are denied access by default, use denied readers to handle exceptions and override the list allowed readers. The maximum number of elements is 100.", "items": {"$ref": "Principal"}, "type": "array"}, "inheritAclFrom": {"description": "The name of the item to inherit the Access Permission List (ACL) from. Note: ACL inheritance *only* provides access permissions to child items and does not define structural relationships, nor does it provide convenient ways to delete large groups of items. Deleting an ACL parent from the index only alters the access permissions of child items that reference the parent in the inheritAclFrom field. The item is still in the index, but may not visible in search results. By contrast, deletion of a container item also deletes all items that reference the container via the containerName field. The maximum length for this field is 1536 characters.", "type": "string"}, "owners": {"description": "Optional. List of owners for the item. This field has no bearing on document access permissions. It does, however, offer a slight ranking boosts items where the querying user is an owner. The maximum number of elements is 5.", "items": {"$ref": "Principal"}, "type": "array"}, "readers": {"description": "List of principals who are allowed to see the item in search results. Optional if inheriting permissions from another item or if the item is not intended to be visible, such as virtual containers. The maximum number of elements is 1000.", "items": {"$ref": "Principal"}, "type": "array"}}, "type": "object"}, "ItemContent": {"description": "Content of an item to be indexed and surfaced by Cloud Search. Only UTF-8 encoded strings are allowed as inlineContent. If the content is uploaded and not binary, it must be UTF-8 encoded.", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"contentDataRef": {"$ref": "UploadItemRef", "description": "Upload reference ID of a previously uploaded content via write method."}, "contentFormat": {"enum": ["UNSPECIFIED", "HTML", "TEXT", "RAW"], "enumDescriptions": ["Invalid value.", "contentFormat is HTML.", "contentFormat is free text.", "contentFormat is raw bytes."], "type": "string"}, "hash": {"description": "Hashing info calculated and provided by the API client for content. Can be used with the items.push method to calculate modified state. The maximum length is 2048 characters.", "type": "string"}, "inlineContent": {"description": "Content that is supplied inlined within the update method. The maximum length is 102400 bytes (100 KiB).", "format": "byte", "type": "string"}}, "type": "object"}, "ItemCountByStatus": {"id": "ItemCountByStatus", "properties": {"count": {"description": "Number of items matching the status code.", "format": "int64", "type": "string"}, "indexedItemsCount": {"description": "Number of items matching the status code for which billing is done. This excludes virtual container items from the total count. This count would not be applicable for items with ERROR or NEW_ITEM status code.", "format": "int64", "type": "string"}, "statusCode": {"description": "Status of the items.", "enum": ["CODE_UNSPECIFIED", "ERROR", "MODIFIED", "NEW_ITEM", "ACCEPTED"], "enumDescriptions": ["Input-only value. Used with Items.list to list all items in the queue, regardless of status.", "Error encountered by Cloud Search while processing this item. Details of the error are in repositoryError.", "Item has been modified in the repository, and is out of date with the version previously accepted into Cloud Search.", "Item is known to exist in the repository, but is not yet accepted by Cloud Search. An item can be in this state when Items.push has been called for an item of this name that did not exist previously.", "API has accepted the up-to-date data of this item."], "type": "string"}}, "type": "object"}, "ItemMetadata": {"description": "Available metadata fields for the item.", "id": "ItemMetadata", "properties": {"containerName": {"description": "The name of the container for this item. Deletion of the container item leads to automatic deletion of this item. Note: ACLs are not inherited from a container item. To provide ACL inheritance for an item, use the inheritAclFrom field. The maximum length is 1536 characters.", "type": "string"}, "contentLanguage": {"description": "The BCP-47 language code for the item, such as \"en-US\" or \"sr-Latn\". For more information, see http://www.unicode.org/reports/tr35/#Unicode_locale_identifier. The maximum length is 32 characters.", "type": "string"}, "contextAttributes": {"description": "A set of named attributes associated with the item. This can be used for influencing the ranking of the item based on the context in the request. The maximum number of elements is 10.", "items": {"$ref": "ContextAttribute"}, "type": "array"}, "createTime": {"description": "The time when the item was created in the source repository.", "format": "google-datetime", "type": "string"}, "hash": {"description": "Hashing value provided by the API caller. This can be used with the items.push method to calculate modified state. The maximum length is 2048 characters.", "type": "string"}, "interactions": {"description": "A list of interactions for the item. Interactions are used to improve Search quality, but are not exposed to end users. The maximum number of elements is 1000.", "items": {"$ref": "Interaction"}, "type": "array"}, "keywords": {"description": "Additional keywords or phrases that should match the item. Used internally for user generated content. The maximum number of elements is 100. The maximum length is 8192 characters.", "items": {"type": "string"}, "type": "array"}, "mimeType": {"description": "The original mime-type of ItemContent.content in the source repository. The maximum length is 256 characters.", "type": "string"}, "objectType": {"description": "The type of the item. This should correspond to the name of an object definition in the schema registered for the data source. For example, if the schema for the data source contains an object definition with name 'document', then item indexing requests for objects of that type should set objectType to 'document'. The maximum length is 256 characters.", "type": "string"}, "searchQualityMetadata": {"$ref": "SearchQualityMetadata", "description": "Additional search quality metadata of the item"}, "sourceRepositoryUrl": {"description": "Link to the source repository serving the data. Seach results apply this link to the title. Whitespace or special characters may cause Cloud Seach result links to trigger a redirect notice; to avoid this, encode the URL. The maximum length is 2048 characters.", "type": "string"}, "title": {"description": "The title of the item. If given, this will be the displayed title of the Search result. The maximum length is 2048 characters.", "type": "string"}, "updateTime": {"description": "The time when the item was last modified in the source repository.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "ItemStatus": {"description": "This contains item's status and any errors.", "id": "ItemStatus", "properties": {"code": {"description": "Status code.", "enum": ["CODE_UNSPECIFIED", "ERROR", "MODIFIED", "NEW_ITEM", "ACCEPTED"], "enumDescriptions": ["Input-only value. Used with Items.list to list all items in the queue, regardless of status.", "Error encountered by Cloud Search while processing this item. Details of the error are in repositoryError.", "Item has been modified in the repository, and is out of date with the version previously accepted into Cloud Search.", "Item is known to exist in the repository, but is not yet accepted by Cloud Search. An item can be in this state when Items.push has been called for an item of this name that did not exist previously.", "API has accepted the up-to-date data of this item."], "type": "string"}, "processingErrors": {"description": "Error details in case the item is in ERROR state.", "items": {"$ref": "ProcessingError"}, "type": "array"}, "repositoryErrors": {"description": "Repository error reported by connector.", "items": {"$ref": "RepositoryError"}, "type": "array"}}, "type": "object"}, "ItemStructuredData": {"description": "Available structured data fields for the item.", "id": "ItemStructuredData", "properties": {"hash": {"description": "Hashing value provided by the API caller. This can be used with the items.push method to calculate modified state. The maximum length is 2048 characters.", "type": "string"}, "object": {"$ref": "StructuredDataObject", "description": "The structured data object that should conform to a registered object definition in the schema for the data source."}}, "type": "object"}, "ListDataSourceResponse": {"id": "ListDataSourceResponse", "properties": {"nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "sources": {"items": {"$ref": "DataSource"}, "type": "array"}}, "type": "object"}, "ListItemNamesForUnmappedIdentityResponse": {"id": "ListItemNamesForUnmappedIdentityResponse", "properties": {"itemNames": {"items": {"type": "string"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}}, "type": "object"}, "ListItemsResponse": {"id": "ListItemsResponse", "properties": {"items": {"items": {"$ref": "<PERSON><PERSON>"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListQuerySourcesResponse": {"description": "List sources response.", "id": "ListQuerySourcesResponse", "properties": {"nextPageToken": {"type": "string"}, "sources": {"items": {"$ref": "QuerySource"}, "type": "array"}}, "type": "object"}, "ListSearchApplicationsResponse": {"id": "ListSearchApplicationsResponse", "properties": {"nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "searchApplications": {"items": {"$ref": "SearchApplication"}, "type": "array"}}, "type": "object"}, "ListUnmappedIdentitiesResponse": {"id": "ListUnmappedIdentitiesResponse", "properties": {"nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "unmappedIdentities": {"items": {"$ref": "UnmappedIdentity"}, "type": "array"}}, "type": "object"}, "MapInfo": {"description": "Geo information used for rendering a map that shows the user's work location.", "id": "MapInfo", "properties": {"lat": {"description": "Latitude in degrees", "format": "double", "type": "number"}, "locationUrl": {"$ref": "SafeUrlProto", "description": "URL to a view of a map centered on the user's work location in Campus Maps (for google.com) or Google Maps (external)."}, "long": {"description": "Longitude in degrees", "format": "double", "type": "number"}, "mapTile": {"description": "MapTiles for the area around a user's work location", "items": {"$ref": "MapTile"}, "type": "array"}, "zoom": {"description": "The zoom level of the map. A constant zoom value of 18 is used for now to match the zoom of the map shown on a Moma Teams Profile page", "format": "int32", "type": "integer"}}, "type": "object"}, "MapTile": {"description": "Information used to render a map tile image in the proper location on a map.", "id": "MapTile", "properties": {"imageUrl": {"$ref": "SafeUrlProto", "description": "URL to an image file containing an office layout of the user's location for their organization, if one is available. For google.com, this image is from Corp Campus Maps."}, "tileX": {"description": "Map tile x coordinate", "format": "double", "type": "number"}, "tileY": {"description": "Map tile y coordinate", "format": "double", "type": "number"}}, "type": "object"}, "MatchRange": {"description": "Matched range of a snippet [start, end).", "id": "MatchRange", "properties": {"end": {"description": "End of the match in the snippet.", "format": "int32", "type": "integer"}, "start": {"description": "Starting position of the match in the snippet.", "format": "int32", "type": "integer"}}, "type": "object"}, "Media": {"description": "Media resource.", "id": "Media", "properties": {"resourceName": {"description": "Name of the media resource.", "type": "string"}}, "type": "object"}, "Metadata": {"description": "<PERSON><PERSON><PERSON> of a matched search result.", "id": "<PERSON><PERSON><PERSON>", "properties": {"createTime": {"description": "The creation time for this document or object in the search result.", "format": "google-datetime", "type": "string"}, "displayOptions": {"$ref": "ResultDisplayMetadata", "description": "Options that specify how to display a structured data search result."}, "fields": {"description": "Indexed fields in structured data, returned as a generic named property.", "items": {"$ref": "NamedProperty"}, "type": "array"}, "mimeType": {"description": "Mime type of the search result.", "type": "string"}, "objectType": {"description": "Object type of the search result.", "type": "string"}, "owner": {"$ref": "Person", "description": "Owner (usually creator) of the document or object of the search result."}, "source": {"$ref": "Source", "description": "The named source for the result, such as Gmail."}, "thumbnailUrl": {"description": "The thumbnail URL of the result.", "type": "string"}, "updateTime": {"description": "The last modified date for the object in the search result. If not set in the item, the value returned here is empty. When `updateTime` is used for calculating freshness and is not set, this value defaults to 2 years from the current time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "Metaline": {"description": "A metaline is a list of properties that are displayed along with the search result to provide context.", "id": "Metaline", "properties": {"properties": {"description": "The list of displayed properties for the metaline. The maximum number of properties is 5.", "items": {"$ref": "DisplayedProperty"}, "type": "array"}}, "type": "object"}, "Name": {"description": "A person's name.", "id": "Name", "properties": {"displayName": {"description": "The read-only display name formatted according to the locale specified by the viewer's account or the `Accept-Language` HTTP header.", "type": "string"}}, "type": "object"}, "NamedProperty": {"description": "A typed name-value pair for structured data. The type of the value should be the same as the registered type for the `name` property in the object definition of `objectType`.", "id": "NamedProperty", "properties": {"booleanValue": {"type": "boolean"}, "dateValues": {"$ref": "DateV<PERSON>ues"}, "doubleValues": {"$ref": "DoubleV<PERSON>ues"}, "enumValues": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "htmlValues": {"$ref": "HtmlValues"}, "integerValues": {"$ref": "IntegerValues"}, "name": {"description": "The name of the property. This name should correspond to the name of the property that was registered for object definition in the schema. The maximum allowable length for this property is 256 characters.", "type": "string"}, "objectValues": {"$ref": "ObjectValues"}, "textValues": {"$ref": "TextValues"}, "timestampValues": {"$ref": "TimestampValues"}}, "type": "object"}, "ObjectDefinition": {"description": "The definition for an object within a data source.", "id": "ObjectDefinition", "properties": {"name": {"description": "The name for the object, which then defines its type. Item indexing requests should set the objectType field equal to this value. For example, if *name* is *Document*, then indexing requests for items of type Document should set objectType equal to *Document*. Each object definition must be uniquely named within a schema. The name must start with a letter and can only contain letters (A-Z, a-z) or numbers (0-9). The maximum length is 256 characters.", "type": "string"}, "options": {"$ref": "ObjectOptions", "description": "The optional object-specific options."}, "propertyDefinitions": {"description": "The property definitions for the object. The maximum number of elements is 1000.", "items": {"$ref": "PropertyDefinition"}, "type": "array"}}, "type": "object"}, "ObjectDisplayOptions": {"description": "The display options for an object.", "id": "ObjectDisplayOptions", "properties": {"metalines": {"description": "Defines the properties that are displayed in the metalines of the search results. The property values are displayed in the order given here. If a property holds multiple values, all of the values are displayed before the next properties. For this reason, it is a good practice to specify singular properties before repeated properties in this list. All of the properties must set is_returnable to true. The maximum number of metalines is 3.", "items": {"$ref": "Metaline"}, "type": "array"}, "objectDisplayLabel": {"description": "The user friendly label to display in the search result to indicate the type of the item. This is OPTIONAL; if not provided, an object label isn't displayed on the context line of the search results. The maximum length is 64 characters.", "type": "string"}}, "type": "object"}, "ObjectOptions": {"description": "The options for an object.", "id": "ObjectOptions", "properties": {"displayOptions": {"$ref": "ObjectDisplayOptions", "description": "The options that determine how the object is displayed in the Cloud Search results page."}, "freshnessOptions": {"$ref": "FreshnessOptions", "description": "The freshness options for an object."}, "suggestionFilteringOperators": {"description": "Operators that can be used to filter suggestions. For Suggest API, only operators mentioned here will be honored in the FilterOptions. Only TEXT and ENUM operators are supported. NOTE: \"objecttype\", \"type\" and \"mimetype\" are already supported. This property is to configure schema specific operators. Even though this is an array, only one operator can be specified. This is an array for future extensibility. Operators mapping to multiple properties within the same object are not supported. If the operator spans across different object types, this option has to be set once for each object definition.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ObjectPropertyOptions": {"description": "The options for object properties.", "id": "ObjectPropertyOptions", "properties": {"subobjectProperties": {"description": "The properties of the sub-object. These properties represent a nested object. For example, if this property represents a postal address, the subobjectProperties might be named *street*, *city*, and *state*. The maximum number of elements is 1000.", "items": {"$ref": "PropertyDefinition"}, "type": "array"}}, "type": "object"}, "ObjectValues": {"description": "List of object values.", "id": "ObjectValues", "properties": {"values": {"items": {"$ref": "StructuredDataObject"}, "type": "array"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "PeoplePromotionCard": {"id": "PeoplePromotionCard", "properties": {"people": {"items": {"$ref": "PersonCore"}, "type": "array"}}, "type": "object"}, "PeopleSuggestion": {"description": "This field contains information about the person being suggested.", "id": "PeopleSuggestion", "properties": {"person": {"$ref": "Person", "description": "Suggested person. All fields of the person object might not be populated."}}, "type": "object"}, "Person": {"description": "Object to represent a person.", "id": "Person", "properties": {"emailAddresses": {"description": "The person's email addresses", "items": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type": "array"}, "name": {"description": "The resource name of the person to provide information about. See [`People.get`](https://developers.google.com/people/api/rest/v1/people/get) from the Google People API.", "type": "string"}, "obfuscatedId": {"description": "Obfuscated ID of a person.", "type": "string"}, "personNames": {"description": "The person's name", "items": {"$ref": "Name"}, "type": "array"}, "phoneNumbers": {"description": "The person's phone numbers", "items": {"$ref": "PhoneNumber"}, "type": "array"}, "photos": {"description": "A person's read-only photo. A picture shown next to the person's name to help others recognize the person in search results.", "items": {"$ref": "Photo"}, "type": "array"}}, "type": "object"}, "PersonCore": {"description": "Information for rendering a person. NEXT ID: 37", "id": "PersonCore", "properties": {"addressMeAs": {"description": "Instructions for how to address this person (e.g. custom pronouns). For google.com this is a set of pronouns from a defined list of options.", "type": "string"}, "adminTo": {"description": "People the profile owner is an admin to. Note that not all fields of these PersonCores will be set, in particular, relationships will be empty.", "items": {"$ref": "PersonCore"}, "type": "array"}, "admins": {"description": "The profile owner's admins in no particular order. Note that not all fields of these PersonCores will be set, in particular, relationships will be empty.", "items": {"$ref": "PersonCore"}, "type": "array"}, "availabilityStatus": {"enum": ["UNKNOWN", "OUT_OF_OFFICE", "OUTSIDE_WORKING_HOURS", "AVAILABLE"], "enumDescriptions": ["", "", "", ""], "type": "string"}, "birthday": {"$ref": "Date", "description": "Person birthday."}, "calendarUrl": {"$ref": "SafeUrlProto", "description": "The URL to open the profile owner's primary calendar."}, "chatUrl": {"$ref": "SafeUrlProto", "description": "The URL to start a chat conversation with the profile owner. For google.com this is a Hangouts URL."}, "costCenter": {"description": "Person's cost center as a string, e.g. \"926: Googler Apps\".", "type": "string"}, "department": {"description": "The person's Organization department, e.g. \"People Operations\". For google.com this is usually called \"area\".", "type": "string"}, "directReports": {"description": "A subset of the profile owner's direct reports. The number of entities here may be less than total_direct_reports_count, because typically ProfileResponse does not include all the person's reports, if there are too many to retrieve efficiently. Note that not all fields of these PersonCores will be set, in particular, relationships will be empty.", "items": {"$ref": "PersonCore"}, "type": "array"}, "dottedLineManagers": {"description": "The profile owner's direct dotted line managers in no particular order. Note that not all fields of these PersonCores will be set, in particular, relationships will be empty.", "items": {"$ref": "PersonCore"}, "type": "array"}, "dottedLineReports": {"description": "A subset of the profile owner's dotted-line reports. The number of entities here may be less than total_dlr_count. Note that not all fields of these PersonCores will be set, in particular, relationships will be empty.", "items": {"$ref": "PersonCore"}, "type": "array"}, "emails": {"description": "E-mail addresses of the person. The primary or preferred email should be first.", "items": {"type": "string"}, "type": "array"}, "employeeId": {"description": "Person's employee number (external ID of type \"organization\") For google.com this is the badge number (e.g. 2 for <PERSON>).", "type": "string"}, "fingerprint": {"description": "A fingerprint used by PAPI to reliably determine if a resource has changed Externally it is used as part of the etag.", "type": "string"}, "ftePermille": {"description": "Full-time equivalent (in ‰) (e.g. 800 for a person who's working 80%).", "format": "int64", "type": "string"}, "geoLocation": {"$ref": "MapInfo"}, "gmailUrl": {"type": "string"}, "jobTitle": {"description": "Profile owner's job title (e.g. \"Software Engineer\"). For google.com this is the Workday preferred job title.", "type": "string"}, "keywordTypes": {"description": "List of keys to use from the map 'keywords'.", "items": {"type": "string"}, "type": "array"}, "keywords": {"additionalProperties": {"type": "string"}, "description": "Custom keywords the domain admin has added.", "type": "object"}, "links": {"description": "Custom links the profile owner has added.", "items": {"$ref": "EnterpriseTopazFrontendTeamsLink"}, "type": "array"}, "location": {"description": "Detailed desk location within the company. For google.com this is the desk location code (e.g. \"DE-MUC-ARP-6T2-6T2C0C\") if the person has a desk.", "type": "string"}, "managers": {"description": "The profile owner's management chain from top to bottom, where managers[0] is the CEO, manager[N-2] is the person's manager's manager and managers[N-1] is the person's direct manager. Note that not all fields of these PersonCores will be set, in particular, relationships will be empty.", "items": {"$ref": "PersonCore"}, "type": "array"}, "mission": {"description": "Custom mission statement the profile owner has added.", "type": "string"}, "name": {"description": "Human-readable Unicode display name.", "type": "string"}, "officeLocation": {"description": "Office/building identifier within the company. For google.com this is the office code (e.g. \"DE-MUC-ARP\").", "type": "string"}, "personId": {"description": "The person's obfuscated Gaia ID.", "type": "string"}, "phoneNumbers": {"items": {"$ref": "EnterpriseTopazFrontendTeamsPersonCorePhoneNumber"}, "type": "array"}, "photoUrl": {"$ref": "SafeUrlProto", "description": "Person photo."}, "postalAddress": {"description": "Postal address of office/building.", "type": "string"}, "totalDirectReportsCount": {"description": "Total count of the profile owner's direct reports.", "format": "int32", "type": "integer"}, "totalDlrCount": {"description": "Total count of the profile owner's dotted-line reports.", "format": "int32", "type": "integer"}, "totalFteCount": {"description": "The sum of all profile owner's reports and their own full-time-equivalents in ‰ (e.g. 1800 if one report is working 80% and profile owner 100%).", "format": "int64", "type": "string"}, "username": {"description": "External ID of type \"login_id\" for the profile. For google.com this is the username/LDAP.", "type": "string"}, "waldoComeBackTime": {"format": "google-datetime", "type": "string"}}, "type": "object"}, "PhoneNumber": {"description": "A person's Phone Number", "id": "PhoneNumber", "properties": {"phoneNumber": {"description": "The phone number of the person.", "type": "string"}, "type": {"enum": ["OTHER", "MOBILE", "OFFICE"], "enumDescriptions": ["", "", ""], "type": "string"}}, "type": "object"}, "Photo": {"description": "A person's photo.", "id": "Photo", "properties": {"url": {"description": "The URL of the photo.", "type": "string"}}, "type": "object"}, "PollItemsRequest": {"id": "PollItemsRequest", "properties": {"connectorName": {"description": "The name of connector making this call. Format: datasources/{source_id}/connectors/{ID}", "type": "string"}, "debugOptions": {"$ref": "DebugOptions", "description": "Common debug options."}, "limit": {"description": "Maximum number of items to return. The maximum value is 100 and the default value is 20.", "format": "int32", "type": "integer"}, "queue": {"description": "Queue name to fetch items from. If unspecified, PollItems will fetch from 'default' queue. The maximum length is 100 characters.", "type": "string"}, "statusCodes": {"description": "Limit the items polled to the ones with these statuses.", "items": {"enum": ["CODE_UNSPECIFIED", "ERROR", "MODIFIED", "NEW_ITEM", "ACCEPTED"], "enumDescriptions": ["Input-only value. Used with Items.list to list all items in the queue, regardless of status.", "Error encountered by Cloud Search while processing this item. Details of the error are in repositoryError.", "Item has been modified in the repository, and is out of date with the version previously accepted into Cloud Search.", "Item is known to exist in the repository, but is not yet accepted by Cloud Search. An item can be in this state when Items.push has been called for an item of this name that did not exist previously.", "API has accepted the up-to-date data of this item."], "type": "string"}, "type": "array"}}, "type": "object"}, "PollItemsResponse": {"id": "PollItemsResponse", "properties": {"items": {"description": "Set of items from the queue available for connector to process. These items have the following subset of fields populated: version metadata.hash structured_data.hash content.hash payload status queue", "items": {"$ref": "<PERSON><PERSON>"}, "type": "array"}}, "type": "object"}, "Principal": {"description": "Reference to a user, group, or domain.", "id": "Principal", "properties": {"groupResourceName": {"description": "This principal is a group identified using an external identity. The name field must specify the group resource name with this format: identitysources/{source_id}/groups/{ID}", "type": "string"}, "gsuitePrincipal": {"$ref": "GSuitePrincipal", "description": "This principal is a Google Workspace user, group or domain."}, "userResourceName": {"description": "This principal is a user identified using an external identity. The name field must specify the user resource name with this format: identitysources/{source_id}/users/{ID}", "type": "string"}}, "type": "object"}, "ProcessingError": {"id": "ProcessingError", "properties": {"code": {"description": "Error code indicating the nature of the error.", "enum": ["PROCESSING_ERROR_CODE_UNSPECIFIED", "MALFORMED_REQUEST", "UNSUPPORTED_CONTENT_FORMAT", "INDIRECT_BROKEN_ACL", "ACL_CYCLE"], "enumDescriptions": ["Input only value. Use this value in Items.", "Item's ACL, metadata, or content is malformed or in invalid state. FieldViolations contains more details on where the problem is.", "Countent format is unsupported.", "Items with incomplete ACL information due to inheriting other items with broken ACL or having groups with unmapped descendants.", "ACL inheritance graph formed a cycle."], "type": "string"}, "errorMessage": {"description": "The description of the error.", "type": "string"}, "fieldViolations": {"description": "In case the item fields are invalid, this field contains the details about the validation errors.", "items": {"$ref": "FieldViolation"}, "type": "array"}}, "type": "object"}, "PropertyDefinition": {"description": "The definition of a property within an object.", "id": "PropertyDefinition", "properties": {"booleanPropertyOptions": {"$ref": "BooleanPropertyOptions"}, "datePropertyOptions": {"$ref": "DatePropertyOptions"}, "displayOptions": {"$ref": "PropertyDisplayOptions", "description": "The options that determine how the property is displayed in the Cloud Search results page if it's specified to be displayed in the object's display options."}, "doublePropertyOptions": {"$ref": "DoublePropertyOptions"}, "enumPropertyOptions": {"$ref": "EnumPropertyOptions"}, "htmlPropertyOptions": {"$ref": "HtmlPropertyOptions"}, "integerPropertyOptions": {"$ref": "IntegerPropertyOptions"}, "isFacetable": {"description": "Indicates that the property can be used for generating facets. Cannot be true for properties whose type is object. IsReturnable must be true to set this option. Only supported for boolean, enum, integer, and text properties.", "type": "boolean"}, "isRepeatable": {"description": "Indicates that multiple values are allowed for the property. For example, a document only has one description but can have multiple comments. Cannot be true for properties whose type is a boolean. If set to false, properties that contain more than one value cause the indexing request for that item to be rejected.", "type": "boolean"}, "isReturnable": {"description": "Indicates that the property identifies data that should be returned in search results via the Query API. If set to *true*, indicates that Query API users can use matching property fields in results. However, storing fields requires more space allocation and uses more bandwidth for search queries, which impacts performance over large datasets. Set to *true* here only if the field is needed for search results. Cannot be true for properties whose type is an object.", "type": "boolean"}, "isSortable": {"description": "Indicates that the property can be used for sorting. Cannot be true for properties that are repeatable. Cannot be true for properties whose type is object. IsReturnable must be true to set this option. Only supported for boolean, date, double, integer, and timestamp properties.", "type": "boolean"}, "isSuggestable": {"description": "Indicates that the property can be used for generating query suggestions.", "type": "boolean"}, "isWildcardSearchable": {"description": "Indicates that users can perform wildcard search for this property. Only supported for Text properties. IsReturnable must be true to set this option. In a given datasource maximum of 5 properties can be marked as is_wildcard_searchable. For more details, see [Define object properties](https://developers.google.com/cloud-search/docs/guides/schema-guide#properties)", "type": "boolean"}, "name": {"description": "The name of the property. Item indexing requests sent to the Indexing API should set the property name equal to this value. For example, if name is *subject_line*, then indexing requests for document items with subject fields should set the name for that field equal to *subject_line*. Use the name as the identifier for the object property. Once registered as a property for an object, you cannot re-use this name for another property within that object. The name must start with a letter and can only contain letters (A-Z, a-z) or numbers (0-9). The maximum length is 256 characters.", "type": "string"}, "objectPropertyOptions": {"$ref": "ObjectPropertyOptions"}, "textPropertyOptions": {"$ref": "TextPropertyOptions"}, "timestampPropertyOptions": {"$ref": "TimestampPropertyOptions"}}, "type": "object"}, "PropertyDisplayOptions": {"description": "The display options for a property.", "id": "PropertyDisplayOptions", "properties": {"displayLabel": {"description": "The user friendly label for the property that is used if the property is specified to be displayed in ObjectDisplayOptions. If provided, the display label is shown in front of the property values when the property is part of the object display options. For example, if the property value is '1', the value by itself may not be useful context for the user. If the display name given was 'priority', then the user sees 'priority : 1' in the search results which provides clear context to search users. This is OPTIONAL; if not given, only the property values are displayed. The maximum length is 64 characters.", "type": "string"}}, "type": "object"}, "PushItem": {"description": "Represents an item to be pushed to the indexing queue.", "id": "PushItem", "properties": {"contentHash": {"description": "Content hash of the item according to the repository. If specified, this is used to determine how to modify this item's status. Setting this field and the type field results in argument error. The maximum length is 2048 characters.", "type": "string"}, "metadataHash": {"description": "The metadata hash of the item according to the repository. If specified, this is used to determine how to modify this item's status. Setting this field and the type field results in argument error. The maximum length is 2048 characters.", "type": "string"}, "payload": {"description": "Provides additional document state information for the connector, such as an alternate repository ID and other metadata. The maximum length is 8192 bytes.", "format": "byte", "type": "string"}, "queue": {"description": "Queue to which this item belongs. The `default` queue is chosen if this field is not specified. The maximum length is 512 characters.", "type": "string"}, "repositoryError": {"$ref": "RepositoryError", "description": "Populate this field to store Connector or repository error details. This information is displayed in the Admin Console. This field may only be populated when the Type is REPOSITORY_ERROR."}, "structuredDataHash": {"description": "Structured data hash of the item according to the repository. If specified, this is used to determine how to modify this item's status. Setting this field and the type field results in argument error. The maximum length is 2048 characters.", "type": "string"}, "type": {"description": "The type of the push operation that defines the push behavior.", "enum": ["UNSPECIFIED", "MODIFIED", "NOT_MODIFIED", "REPOSITORY_ERROR", "REQUEUE"], "enumDescriptions": ["Default UNSPECIFIED. Specifies that the push operation should not modify ItemStatus", "Indicates that the repository document has been modified or updated since the previous update call. This changes status to MODIFIED state for an existing item. If this is called on a non existing item, the status is changed to NEW_ITEM.", "Item in the repository has not been modified since the last update call. This push operation will set status to ACCEPTED state.", "Connector is facing a repository error regarding this item. Change status to REPOSITORY_ERROR state. Item is unreserved and rescheduled at a future time determined by exponential backoff.", "Call push with REQUEUE only for items that have been reserved. This action unreserves the item and resets its available time to the wall clock time."], "type": "string"}}, "type": "object"}, "PushItemRequest": {"id": "PushItemRequest", "properties": {"connectorName": {"description": "The name of connector making this call. Format: datasources/{source_id}/connectors/{ID}", "type": "string"}, "debugOptions": {"$ref": "DebugOptions", "description": "Common debug options."}, "item": {"$ref": "PushItem", "description": "Item to push onto the queue."}}, "type": "object"}, "QueryActivity": {"description": "Details about a user's query activity.", "id": "QueryActivity", "properties": {"query": {"description": "User input query to be logged/removed.", "type": "string"}}, "type": "object"}, "QueryCountByStatus": {"id": "QueryCountByStatus", "properties": {"count": {"format": "int64", "type": "string"}, "statusCode": {"description": "This represents the http status code.", "format": "int32", "type": "integer"}}, "type": "object"}, "QueryInterpretation": {"id": "QueryInterpretation", "properties": {"interpretationType": {"enum": ["NONE", "BLEND", "REPLACE"], "enumDescriptions": ["Neither the natural language interpretation, nor a broader version of the query is used to fetch the search results.", "The results from original query are blended with other results. The reason for blending these other results with the results from original query is populated in the 'Reason' field below.", "The results from original query are replaced. The reason for replacing the results from original query is populated in the 'Reason' field below."], "type": "string"}, "interpretedQuery": {"description": "The interpretation of the query used in search. For example, queries with natural language intent like \"email from john\" will be interpreted as \"from:john source:mail\". This field will not be filled when the reason is NOT_ENOUGH_RESULTS_FOUND_FOR_USER_QUERY.", "type": "string"}, "reason": {"description": "The reason for interpretation of the query. This field will not be UNSPECIFIED if the interpretation type is not NONE.", "enum": ["UNSPECIFIED", "QUERY_HAS_NATURAL_LANGUAGE_INTENT", "NOT_ENOUGH_RESULTS_FOUND_FOR_USER_QUERY"], "enumDescriptions": ["", "Natural language interpretation of the query is used to fetch the search results.", "Query and document terms similarity is used to selectively broaden the query to retrieve additional search results since enough results were not found for the user query. Interpreted query will be empty for this case."], "type": "string"}}, "type": "object"}, "QueryInterpretationConfig": {"description": "Default options to interpret user query.", "id": "QueryInterpretationConfig", "properties": {"forceDisableSupplementalResults": {"description": "Set this flag to disable supplemental results retrieval, setting a flag here will not retrieve supplemental results for queries associated with a given search application. If this flag is set to True, it will take precedence over the option set at Query level. For the default value of False, query level flag will set the correct interpretation for supplemental results.", "type": "boolean"}, "forceVerbatimMode": {"description": "Enable this flag to turn off all internal optimizations like natural language (NL) interpretation of queries, supplemental results retrieval, and usage of synonyms including custom ones. If this flag is set to True, it will take precedence over the option set at Query level. For the default value of False, query level flag will set the correct interpretation for verbatim mode.", "type": "boolean"}}, "type": "object"}, "QueryInterpretationOptions": {"description": "Options to interpret user query.", "id": "QueryInterpretationOptions", "properties": {"disableNlInterpretation": {"description": "Flag to disable natural language (NL) interpretation of queries. Default is false, Set to true to disable natural language interpretation. NL interpretation only applies to predefined datasources.", "type": "boolean"}, "disableSupplementalResults": {"description": "Use this flag to disable supplemental results for a query. Supplemental results setting chosen at SearchApplication level will take precedence if set to True.", "type": "boolean"}, "enableVerbatimMode": {"description": "Enable this flag to turn off all internal optimizations like natural language (NL) interpretation of queries, supplemental result retrieval, and usage of synonyms including custom ones. Nl interpretation will be disabled if either one of the two flags is true.", "type": "boolean"}}, "type": "object"}, "QueryItem": {"description": "Information relevant only to a query entry.", "id": "QueryItem", "properties": {"isSynthetic": {"description": "True if the text was generated by means other than a previous user search.", "type": "boolean"}}, "type": "object"}, "QueryOperator": {"description": "The definition of a operator that can be used in a Search/Suggest request.", "id": "QueryOperator", "properties": {"displayName": {"description": "Display name of the operator", "type": "string"}, "enumValues": {"description": "Potential list of values for the opeatror field. This field is only filled when we can safely enumerate all the possible values of this operator.", "items": {"type": "string"}, "type": "array"}, "greaterThanOperatorName": {"description": "Indicates the operator name that can be used to isolate the property using the greater-than operator.", "type": "string"}, "isFacetable": {"description": "Can this operator be used to get facets.", "type": "boolean"}, "isRepeatable": {"description": "Indicates if multiple values can be set for this property.", "type": "boolean"}, "isReturnable": {"description": "Will the property associated with this facet be returned as part of search results.", "type": "boolean"}, "isSortable": {"description": "Can this operator be used to sort results.", "type": "boolean"}, "isSuggestable": {"description": "Can get suggestions for this field.", "type": "boolean"}, "lessThanOperatorName": {"description": "Indicates the operator name that can be used to isolate the property using the less-than operator.", "type": "string"}, "objectType": {"description": "The name of the object corresponding to the operator. This field is only filled for schema-specific operators, and is unset for common operators.", "type": "string"}, "operatorName": {"description": "The name of the operator.", "type": "string"}, "type": {"description": "The type of the operator.", "enum": ["UNKNOWN", "INTEGER", "DOUBLE", "TIMESTAMP", "BOOLEAN", "ENUM", "DATE", "TEXT", "HTML"], "enumDescriptions": ["Invalid value.", "", "", "", "", "", "", "", ""], "type": "string"}}, "type": "object"}, "QuerySource": {"description": "List of sources that the user can search using the query API.", "id": "QuerySource", "properties": {"displayName": {"description": "Display name of the data source.", "type": "string"}, "operators": {"description": "List of all operators applicable for this source.", "items": {"$ref": "QueryOperator"}, "type": "array"}, "shortName": {"description": "A short name or alias for the source. This value can be used with the 'source' operator.", "type": "string"}, "source": {"$ref": "Source", "description": "The name of the source"}}, "type": "object"}, "QuerySuggestion": {"description": "This field does not contain anything as of now and is just used as an indicator that the suggest result was a phrase completion.", "id": "QuerySuggestion", "properties": {}, "type": "object"}, "RemoveActivityRequest": {"description": "Remove Logged Activity Request.", "id": "RemoveActivityRequest", "properties": {"requestOptions": {"$ref": "RequestOptions", "description": "Request options, such as the search application and clientId."}, "userActivity": {"$ref": "UserActivity", "description": "User Activity containing the data to be deleted."}}, "type": "object"}, "RemoveActivityResponse": {"description": "Remove Logged Activity Response. will return an empty response for now. Will be revisited in later phases.", "id": "RemoveActivityResponse", "properties": {}, "type": "object"}, "RepositoryError": {"description": "Errors when the connector is communicating to the source repository.", "id": "RepositoryError", "properties": {"errorMessage": {"description": "Message that describes the error. The maximum allowable length of the message is 8192 characters.", "type": "string"}, "httpStatusCode": {"description": "Error codes. Matches the definition of HTTP status codes.", "format": "int32", "type": "integer"}, "type": {"description": "The type of error.", "enum": ["UNKNOWN", "NETWORK_ERROR", "DNS_ERROR", "CONNECTION_ERROR", "AUTHENTICATION_ERROR", "AUTHORIZATION_ERROR", "SERVER_ERROR", "QUOTA_EXCEEDED", "SERVICE_UNAVAILABLE", "CLIENT_ERROR"], "enumDescriptions": ["Unknown error.", "Unknown or unreachable host.", "DNS problem, such as the DNS server is not responding.", "Cannot connect to the repository server.", "Failed authentication due to incorrect credentials.", "Service account is not authorized for the repository.", "Repository server error.", "<PERSON><PERSON><PERSON> exceeded.", "Server temporarily unavailable.", "Client-related error, such as an invalid request from the connector to the repository server."], "type": "string"}}, "type": "object"}, "RequestOptions": {"description": "Shared request options for all RPC methods.", "id": "RequestOptions", "properties": {"debugOptions": {"$ref": "DebugOptions", "description": "Debug options of the request"}, "languageCode": {"description": "The BCP-47 language code, such as \"en-US\" or \"sr-Latn\". For more information, see http://www.unicode.org/reports/tr35/#Unicode_locale_identifier. For translations. Set this field using the language set in browser or for the page. In the event that the user's language preference is known, set this field to the known user language. When specified, the documents in search results are biased towards the specified language. The Suggest API uses this field as a hint to make better third-party autocomplete predictions.", "type": "string"}, "searchApplicationId": {"description": "The ID generated when you create a search application using the [admin console](https://support.google.com/a/answer/9043922).", "type": "string"}, "timeZone": {"description": "Current user's time zone id, such as \"America/Los_Angeles\" or \"Australia/Sydney\". These IDs are defined by [Unicode Common Locale Data Repository (CLDR)](http://cldr.unicode.org/) project, and currently available in the file [timezone.xml](http://unicode.org/repos/cldr/trunk/common/bcp47/timezone.xml). This field is used to correctly interpret date and time queries. If this field is not specified, the default time zone (UTC) is used.", "type": "string"}}, "type": "object"}, "ResetSearchApplicationRequest": {"id": "ResetSearchApplicationRequest", "properties": {"debugOptions": {"$ref": "DebugOptions", "description": "Common debug options."}}, "type": "object"}, "ResponseDebugInfo": {"description": "Debugging information about the response.", "id": "ResponseDebugInfo", "properties": {"formattedDebugInfo": {"description": "General debug info formatted for display.", "type": "string"}}, "type": "object"}, "RestrictItem": {"description": "Information relevant only to a restrict entry. NextId: 12", "id": "RestrictItem", "properties": {"driveFollowUpRestrict": {"$ref": "DriveFollowUpRestrict"}, "driveLocationRestrict": {"$ref": "DriveLocationRestrict"}, "driveMimeTypeRestrict": {"$ref": "DriveMimeTypeRestrict", "description": "Drive Types."}, "driveTimeSpanRestrict": {"$ref": "DriveTimeSpanRestrict"}, "searchOperator": {"description": "The search restrict (e.g. \"after:2017-09-11 before:2017-09-12\").", "type": "string"}}, "type": "object"}, "ResultCounts": {"description": "Result count information", "id": "ResultCounts", "properties": {"sourceResultCounts": {"description": "Result count information for each source with results.", "items": {"$ref": "SourceResultCount"}, "type": "array"}}, "type": "object"}, "ResultDebugInfo": {"description": "Debugging information about the result.", "id": "ResultDebugInfo", "properties": {"formattedDebugInfo": {"description": "General debug info formatted for display.", "type": "string"}}, "type": "object"}, "ResultDisplayField": {"description": "Display Fields for Search Results", "id": "ResultDisplayField", "properties": {"label": {"description": "The display label for the property.", "type": "string"}, "operatorName": {"description": "The operator name of the property.", "type": "string"}, "property": {"$ref": "NamedProperty", "description": "The name value pair for the property."}}, "type": "object"}, "ResultDisplayLine": {"description": "The collection of fields that make up a displayed line", "id": "ResultDisplayLine", "properties": {"fields": {"items": {"$ref": "ResultDisplayField"}, "type": "array"}}, "type": "object"}, "ResultDisplayMetadata": {"id": "ResultDisplayMetadata", "properties": {"metalines": {"description": "The metalines content to be displayed with the result.", "items": {"$ref": "ResultDisplayLine"}, "type": "array"}, "objectTypeLabel": {"description": "The display label for the object.", "type": "string"}}, "type": "object"}, "RetrievalImportance": {"id": "RetrievalImportance", "properties": {"importance": {"description": "Indicates the ranking importance given to property when it is matched during retrieval. Once set, the token importance of a property cannot be changed.", "enum": ["DEFAULT", "HIGHEST", "HIGH", "LOW", "NONE"], "enumDescriptions": ["Treat the match like a body text match.", "Treat the match like a match against title of the item.", "Treat the match with higher importance than body text.", "Treat the match with lower importance than body text.", "Do not match against this field during retrieval. The property can still be used for operator matching, faceting, and suggest if desired."], "type": "string"}}, "type": "object"}, "SafeHtmlProto": {"description": "IMPORTANT: It is unsafe to accept this message from an untrusted source, since it's trivial for an attacker to forge serialized messages that don't fulfill the type's safety contract -- for example, it could contain attacker controlled script. A system which receives a SafeHtmlProto implicitly trusts the producer of the SafeHtmlProto. So, it's generally safe to return this message in RPC responses, but generally unsafe to accept it in RPC requests.", "id": "SafeHtmlProto", "properties": {"privateDoNotAccessOrElseSafeHtmlWrappedValue": {"description": "IMPORTANT: Never set or read this field, even from tests, it is private. See documentation at the top of .proto file for programming language packages with which to create or read this message.", "type": "string"}}, "type": "object"}, "SafeUrlProto": {"description": "Message containing a string that is safe to use in URL contexts in DOM APIs and HTML documents, where the URL context does not refer to a resource that loads code.", "id": "SafeUrlProto", "properties": {"privateDoNotAccessOrElseSafeUrlWrappedValue": {"description": "IMPORTANT: Never set or read this field, even from tests, it is private. See documentation at the top of .proto file for programming language packages with which to create or read this message.", "type": "string"}}, "type": "object"}, "Schema": {"description": "The schema definition for a data source.", "id": "<PERSON><PERSON><PERSON>", "properties": {"objectDefinitions": {"description": "The list of top-level objects for the data source. The maximum number of elements is 10.", "items": {"$ref": "ObjectDefinition"}, "type": "array"}, "operationIds": {"description": "IDs of the Long Running Operations (LROs) currently running for this schema. After modifying the schema, wait for operations to complete before indexing additional content.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ScoringConfig": {"description": "Scoring configurations for a source while processing a Search or Suggest request.", "id": "ScoringConfig", "properties": {"disableFreshness": {"description": "Whether to use freshness as a ranking signal. By default, freshness is used as a ranking signal. Note that this setting is not available in the Admin UI.", "type": "boolean"}, "disablePersonalization": {"description": "Whether to personalize the results. By default, personal signals will be used to boost results.", "type": "boolean"}}, "type": "object"}, "SearchApplication": {"description": "SearchApplication", "id": "SearchApplication", "properties": {"dataSourceRestrictions": {"description": "Retrictions applied to the configurations. The maximum number of elements is 10.", "items": {"$ref": "DataSourceRestriction"}, "type": "array"}, "defaultFacetOptions": {"description": "The default fields for returning facet results. The sources specified here also have been included in data_source_restrictions above.", "items": {"$ref": "FacetOptions"}, "type": "array"}, "defaultSortOptions": {"$ref": "SortOptions", "description": "The default options for sorting the search results"}, "displayName": {"description": "Display name of the Search Application. The maximum length is 300 characters.", "type": "string"}, "enableAuditLog": {"description": "Indicates whether audit logging is on/off for requests made for the search application in query APIs.", "type": "boolean"}, "name": {"description": "The name of the Search Application. Format: searchapplications/{application_id}.", "type": "string"}, "operationIds": {"description": "Output only. IDs of the Long Running Operations (LROs) currently running for this schema. Output only field.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "queryInterpretationConfig": {"$ref": "QueryInterpretationConfig", "description": "The default options for query interpretation"}, "returnResultThumbnailUrls": {"description": "With each result we should return the URI for its thumbnail (when applicable)", "type": "boolean"}, "scoringConfig": {"$ref": "ScoringConfig", "description": "Configuration for ranking results."}, "sourceConfig": {"description": "Configuration for a sources specified in data_source_restrictions.", "items": {"$ref": "SourceConfig"}, "type": "array"}}, "type": "object"}, "SearchApplicationQueryStats": {"description": "Search application level query stats per date", "id": "SearchApplicationQueryStats", "properties": {"date": {"$ref": "Date", "description": "The date for which query stats were calculated. Stats calculated on the next day close to midnight are returned."}, "queryCountByStatus": {"items": {"$ref": "QueryCountByStatus"}, "type": "array"}}, "type": "object"}, "SearchApplicationSessionStats": {"id": "SearchApplicationSessionStats", "properties": {"date": {"$ref": "Date", "description": "The date for which session stats were calculated. Stats are calculated on the following day, close to midnight PST, and then returned."}, "searchSessionsCount": {"description": "The count of search sessions on the day", "format": "int64", "type": "string"}}, "type": "object"}, "SearchApplicationUserStats": {"id": "SearchApplicationUserStats", "properties": {"date": {"$ref": "Date", "description": "The date for which session stats were calculated. Stats calculated on the next day close to midnight are returned."}, "oneDayActiveUsersCount": {"description": "The count of unique active users in the past one day", "format": "int64", "type": "string"}, "sevenDaysActiveUsersCount": {"description": "The count of unique active users in the past seven days", "format": "int64", "type": "string"}, "thirtyDaysActiveUsersCount": {"description": "The count of unique active users in the past thirty days", "format": "int64", "type": "string"}}, "type": "object"}, "SearchItemsByViewUrlRequest": {"id": "SearchItemsByViewUrlRequest", "properties": {"debugOptions": {"$ref": "DebugOptions", "description": "Common debug options."}, "pageToken": {"description": "The next_page_token value returned from a previous request, if any.", "type": "string"}, "viewUrl": {"description": "Specify the full view URL to find the corresponding item. The maximum length is 2048 characters.", "type": "string"}}, "type": "object"}, "SearchItemsByViewUrlResponse": {"id": "SearchItemsByViewUrlResponse", "properties": {"items": {"items": {"$ref": "<PERSON><PERSON>"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}}, "type": "object"}, "SearchQualityMetadata": {"description": "Additional search quality metadata of the item.", "id": "SearchQualityMetadata", "properties": {"quality": {"description": "An indication of the quality of the item, used to influence search quality. Value should be between 0.0 (lowest quality) and 1.0 (highest quality). The default value is 0.0.", "format": "double", "type": "number"}}, "type": "object"}, "SearchRequest": {"description": "The search API request. NEXT ID: 17", "id": "SearchRequest", "properties": {"contextAttributes": {"description": "Context attributes for the request which will be used to adjust ranking of search results. The maximum number of elements is 10.", "items": {"$ref": "ContextAttribute"}, "type": "array"}, "dataSourceRestrictions": {"description": "The sources to use for querying. If not specified, all data sources from the current search application are used.", "items": {"$ref": "DataSourceRestriction"}, "type": "array"}, "facetOptions": {"items": {"$ref": "FacetOptions"}, "type": "array"}, "pageSize": {"description": "Maximum number of search results to return in one page. Valid values are between 1 and 100, inclusive. Default value is 10. Minimum value is 50 when results beyond 2000 are requested.", "format": "int32", "type": "integer"}, "query": {"description": "The raw query string. See supported search operators in the [Narrow your search with operators](https://support.google.com/cloudsearch/answer/6172299)", "type": "string"}, "queryInterpretationOptions": {"$ref": "QueryInterpretationOptions", "description": "Options to interpret the user query."}, "requestOptions": {"$ref": "RequestOptions", "description": "Request options, such as the search application and user timezone."}, "sortOptions": {"$ref": "SortOptions", "description": "The options for sorting the search results"}, "start": {"description": "Starting index of the results.", "format": "int32", "type": "integer"}}, "type": "object"}, "SearchResponse": {"description": "The search API response. NEXT ID: 17", "id": "SearchResponse", "properties": {"debugInfo": {"$ref": "ResponseDebugInfo", "description": "Debugging information about the response."}, "errorInfo": {"$ref": "ErrorInfo", "description": "Error information about the response."}, "facetResults": {"description": "Repeated facet results.", "items": {"$ref": "FacetResult"}, "type": "array"}, "hasMoreResults": {"description": "Whether there are more search results matching the query.", "type": "boolean"}, "queryInterpretation": {"$ref": "QueryInterpretation", "description": "Query interpretation result for user query. Empty if query interpretation is disabled."}, "resultCountEstimate": {"description": "The estimated result count for this query.", "format": "int64", "type": "string"}, "resultCountExact": {"description": "The exact result count for this query.", "format": "int64", "type": "string"}, "resultCounts": {"$ref": "ResultCounts", "description": "Expanded result count information."}, "results": {"description": "Results from a search query.", "items": {"$ref": "SearchResult"}, "type": "array"}, "spellResults": {"description": "Suggested spelling for the query.", "items": {"$ref": "SpellResult"}, "type": "array"}, "structuredResults": {"description": "Structured results for the user query. These results are not counted against the page_size.", "items": {"$ref": "StructuredResult"}, "type": "array"}}, "type": "object"}, "SearchResult": {"description": "Results containing indexed information for a document.", "id": "SearchResult", "properties": {"clusteredResults": {"description": "If source is clustered, provide list of clustered results. There will only be one level of clustered results. If current source is not enabled for clustering, this field will be empty.", "items": {"$ref": "SearchResult"}, "type": "array"}, "debugInfo": {"$ref": "ResultDebugInfo", "description": "Debugging information about this search result."}, "metadata": {"$ref": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> of the search result."}, "snippet": {"$ref": "Snippet", "description": "The concatenation of all snippets (summaries) available for this result."}, "title": {"description": "Title of the search result.", "type": "string"}, "url": {"description": "The URL of the search result. The URL contains a Google redirect to the actual item. This URL is signed and shouldn't be changed.", "type": "string"}}, "type": "object"}, "Snippet": {"description": "Snippet of the search result, which summarizes the content of the resulting page.", "id": "Snippet", "properties": {"matchRanges": {"description": "The matched ranges in the snippet.", "items": {"$ref": "MatchRange"}, "type": "array"}, "snippet": {"description": "The snippet of the document. May contain escaped HTML character that should be unescaped prior to rendering.", "type": "string"}}, "type": "object"}, "SortOptions": {"id": "SortOptions", "properties": {"operatorName": {"description": "The name of the operator corresponding to the field to sort on. The corresponding property must be marked as sortable.", "type": "string"}, "sortOrder": {"description": "Ascending is the default sort order", "enum": ["ASCENDING", "DESCENDING"], "enumDescriptions": ["", ""], "type": "string"}}, "type": "object"}, "Source": {"description": "Defines sources for the suggest/search APIs.", "id": "Source", "properties": {"name": {"description": "Source name for content indexed by the Indexing API.", "type": "string"}, "predefinedSource": {"description": "Predefined content source for Google Apps.", "enum": ["NONE", "QUERY_HISTORY", "PERSON", "GOOGLE_DRIVE", "GOOGLE_GMAIL", "GOOGLE_SITES", "GOOGLE_GROUPS", "GOOGLE_CALENDAR", "GOOGLE_KEEP"], "enumDescriptions": ["", "Suggests queries issued by the user in the past. Only valid when used with the suggest API. Ignored when used in the query API.", "Suggests people in the organization. Only valid when used with the suggest API. Results in an error when used in the query API.", "", "", "", "", "", ""], "type": "string"}}, "type": "object"}, "SourceConfig": {"description": "Configurations for a source while processing a Search or Suggest request.", "id": "SourceConfig", "properties": {"crowdingConfig": {"$ref": "SourceCrowdingConfig", "description": "The crowding configuration for the source."}, "scoringConfig": {"$ref": "SourceScoringConfig", "description": "The scoring configuration for the source."}, "source": {"$ref": "Source", "description": "The source for which this configuration is to be used."}}, "type": "object"}, "SourceCrowdingConfig": {"description": "Set search results crowding limits. Crowding is a situation in which multiple results from the same source or host \"crowd out\" other results, diminishing the quality of search for users. To foster better search quality and source diversity in search results, you can set a condition to reduce repetitive results by source.", "id": "SourceCrowdingConfig", "properties": {"numResults": {"description": "Maximum number of results allowed from a datasource in a result page as long as results from other sources are not exhausted. Value specified must not be negative. A default value is used if this value is equal to 0. To disable crowding, set the value greater than 100.", "format": "int32", "type": "integer"}, "numSuggestions": {"description": "Maximum number of suggestions allowed from a source. No limits will be set on results if this value is less than or equal to 0.", "format": "int32", "type": "integer"}}, "type": "object"}, "SourceResultCount": {"description": "Per source result count information.", "id": "SourceResultCount", "properties": {"hasMoreResults": {"description": "Whether there are more search results for this source.", "type": "boolean"}, "resultCountEstimate": {"description": "The estimated result count for this source.", "format": "int64", "type": "string"}, "resultCountExact": {"description": "The exact result count for this source.", "format": "int64", "type": "string"}, "source": {"$ref": "Source", "description": "The source the result count information is associated with."}}, "type": "object"}, "SourceScoringConfig": {"description": "Set the scoring configuration. This allows modifying the ranking of results for a source.", "id": "SourceScoringConfig", "properties": {"sourceImportance": {"description": "Importance of the source.", "enum": ["DEFAULT", "LOW", "HIGH"], "enumDescriptions": ["", "", ""], "type": "string"}}, "type": "object"}, "SpellResult": {"id": "SpellResult", "properties": {"suggestedQuery": {"description": "The suggested spelling of the query.", "type": "string"}, "suggestedQueryHtml": {"$ref": "SafeHtmlProto", "description": "The sanitized HTML representing the spell corrected query that can be used in the UI. This usually has language-specific tags to mark up parts of the query that are spell checked."}, "suggestionType": {"description": "Suggestion triggered for the current query.", "enum": ["SUGGESTION_TYPE_UNSPECIFIED", "NON_EMPTY_RESULTS_SPELL_SUGGESTION", "ZERO_RESULTS_FULL_PAGE_REPLACEMENT"], "enumDescriptions": ["Default spell check type", "Spell suggestion without any results changed. The results are still shown for the original query (which has non zero / results) with a suggestion for spelling that would have results.", "Spell suggestion triggered when original query has no results. When the original query has no results, and spell suggestion has results we trigger results for the spell corrected query."], "type": "string"}}, "type": "object"}, "StartUploadItemRequest": {"description": "Start upload file request.", "id": "StartUploadItemRequest", "properties": {"connectorName": {"description": "The name of connector making this call. Format: datasources/{source_id}/connectors/{ID}", "type": "string"}, "debugOptions": {"$ref": "DebugOptions", "description": "Common debug options."}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "StructuredDataObject": {"description": "A structured data object consisting of named properties.", "id": "StructuredDataObject", "properties": {"properties": {"description": "The properties for the object. The maximum number of elements is 1000.", "items": {"$ref": "NamedProperty"}, "type": "array"}}, "type": "object"}, "StructuredResult": {"description": "Structured results that are returned as part of search request.", "id": "StructuredResult", "properties": {"person": {"$ref": "Person", "description": "Representation of a person"}}, "type": "object"}, "SuggestRequest": {"description": "Request of suggest API.", "id": "SuggestRequest", "properties": {"dataSourceRestrictions": {"description": "The sources to use for suggestions. If not specified, the data sources are taken from the current search application. NOTE: Suggestions are only supported for the following sources: * Third-party data sources * PredefinedSource.PERSON * PredefinedSource.GOOGLE_DRIVE", "items": {"$ref": "DataSourceRestriction"}, "type": "array"}, "query": {"description": "Partial query for which autocomplete suggestions will be shown. For example, if the query is \"sea\", then the server might return \"season\", \"search\", \"seagull\" and so on.", "type": "string"}, "requestOptions": {"$ref": "RequestOptions", "description": "Request options, such as the search application and user timezone."}}, "type": "object"}, "SuggestResponse": {"description": "Response of the suggest API.", "id": "SuggestResponse", "properties": {"suggestResults": {"description": "List of suggestions.", "items": {"$ref": "SuggestResult"}, "type": "array"}}, "type": "object"}, "SuggestResult": {"description": "One suggestion result.", "id": "SuggestResult", "properties": {"peopleSuggestion": {"$ref": "PeopleSuggestion", "description": "This is present when the suggestion indicates a person. It contains more information about the person - like their email ID, name etc."}, "querySuggestion": {"$ref": "QuerySuggestion", "description": "This field will be present if the suggested query is a word/phrase completion."}, "source": {"$ref": "Source", "description": "The source of the suggestion."}, "suggestedQuery": {"description": "The suggested query that will be used for search, when the user clicks on the suggestion", "type": "string"}}, "type": "object"}, "TextOperatorOptions": {"description": "Used to provide a search operator for text properties. This is optional. Search operators let users restrict the query to specific fields relevant to the type of item being searched.", "id": "TextOperatorOptions", "properties": {"exactMatchWithOperator": {"description": "If true, the text value is tokenized as one atomic value in operator searches and facet matches. For example, if the operator name is \"genre\" and the value is \"science-fiction\" the query restrictions \"genre:science\" and \"genre:fiction\" doesn't match the item; \"genre:science-fiction\" does. Text value matching is case-sensitive and does not remove special characters. If false, the text is tokenized. For example, if the value is \"science-fiction\" the queries \"genre:science\" and \"genre:fiction\" matches the item.", "type": "boolean"}, "operatorName": {"description": "Indicates the operator name required in the query in order to isolate the text property. For example, if operator<PERSON><PERSON> is *subject* and the property's name is *subjectLine*, then queries like *subject:<value>* show results only where the value of the property named *subjectLine* matches *<value>*. By contrast, a search that uses the same *<value>* without an operator returns all items where *<value>* matches the value of any text properties or text within the content field for the item. The operator name can only contain lowercase letters (a-z). The maximum length is 32 characters.", "type": "string"}}, "type": "object"}, "TextPropertyOptions": {"description": "The options for text properties.", "id": "TextPropertyOptions", "properties": {"operatorOptions": {"$ref": "TextOperatorOptions", "description": "If set, describes how the property should be used as a search operator."}, "retrievalImportance": {"$ref": "RetrievalImportance", "description": "Indicates the search quality importance of the tokens within the field when used for retrieval."}}, "type": "object"}, "TextValues": {"description": "List of text values.", "id": "TextValues", "properties": {"values": {"description": "The maximum allowable length for text values is 2048 characters.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ThirdPartyGenericCard": {"id": "ThirdPartyGenericCard", "properties": {"cardId": {"description": "Unique identifier for the card.", "type": "string"}, "category": {"description": "Category that the card belongs to.", "type": "string"}, "content": {"$ref": "Content", "description": "[Required] Card content."}, "context": {"$ref": "Context", "description": "[Required] Context where the card should be triggered."}, "isDismissible": {"description": "Whether the card can be dismissed.", "type": "boolean"}, "priority": {"description": "Priority of the card, where 0 is the highest priority.", "format": "int32", "type": "integer"}}, "type": "object"}, "TimestampOperatorOptions": {"description": "Used to provide a search operator for timestamp properties. This is optional. Search operators let users restrict the query to specific fields relevant to the type of item being searched.", "id": "TimestampOperatorOptions", "properties": {"greaterThanOperatorName": {"description": "Indicates the operator name required in the query in order to isolate the timestamp property using the greater-than operator. For example, if greaterThanOperatorName is *closedafter* and the property's name is *closeDate*, then queries like *closedafter:<value>* show results only where the value of the property named *closeDate* is later than *<value>*. The operator name can only contain lowercase letters (a-z). The maximum length is 32 characters.", "type": "string"}, "lessThanOperatorName": {"description": "Indicates the operator name required in the query in order to isolate the timestamp property using the less-than operator. For example, if lessThanOperatorName is *closedbefore* and the property's name is *closeDate*, then queries like *closedbefore:<value>* show results only where the value of the property named *closeDate* is earlier than *<value>*. The operator name can only contain lowercase letters (a-z). The maximum length is 32 characters.", "type": "string"}, "operatorName": {"description": "Indicates the operator name required in the query in order to isolate the timestamp property. For example, if operatorName is *closedon* and the property's name is *closeDate*, then queries like *closedon:<value>* show results only where the value of the property named *closeDate* matches *<value>*. By contrast, a search that uses the same *<value>* without an operator returns all items where *<value>* matches the value of any String properties or text within the content field for the item. The operator name can only contain lowercase letters (a-z). The maximum length is 32 characters.", "type": "string"}}, "type": "object"}, "TimestampPropertyOptions": {"description": "The options for timestamp properties.", "id": "TimestampPropertyOptions", "properties": {"operatorOptions": {"$ref": "TimestampOperatorOptions", "description": "If set, describes how the timestamp should be used as a search operator."}}, "type": "object"}, "TimestampValues": {"description": "List of timestamp values.", "id": "TimestampValues", "properties": {"values": {"items": {"format": "google-datetime", "type": "string"}, "type": "array"}}, "type": "object"}, "UnmappedIdentity": {"id": "UnmappedIdentity", "properties": {"externalIdentity": {"$ref": "Principal", "description": "The resource name for an external user."}, "resolutionStatusCode": {"description": "The resolution status for the external identity.", "enum": ["CODE_UNSPECIFIED", "NOT_FOUND", "IDENTITY_SOURCE_NOT_FOUND", "IDENTITY_SOURCE_MISCONFIGURED", "TOO_MANY_MAPPINGS_FOUND", "INTERNAL_ERROR"], "enumDescriptions": ["Input-only value. Used to list all unmapped identities regardless of status.", "The unmapped identity was not found in IDaaS, and needs to be provided by the user.", "The identity source associated with the identity was either not found or deleted.", "IDaaS does not understand the identity source, probably because the schema was modified in a non compatible way.", "The number of users associated with the external identity is too large.", "Internal error."], "type": "string"}}, "type": "object"}, "UnreserveItemsRequest": {"id": "UnreserveItemsRequest", "properties": {"connectorName": {"description": "The name of connector making this call. Format: datasources/{source_id}/connectors/{ID}", "type": "string"}, "debugOptions": {"$ref": "DebugOptions", "description": "Common debug options."}, "queue": {"description": "The name of a queue to unreserve items from.", "type": "string"}}, "type": "object"}, "UpdateDataSourceRequest": {"id": "UpdateDataSourceRequest", "properties": {"debugOptions": {"$ref": "DebugOptions", "description": "Common debug options."}, "source": {"$ref": "DataSource"}, "updateMask": {"description": "Only applies to [`settings.datasources.patch`](https://developers.google.com/cloud-search/docs/reference/rest/v1/settings.datasources/patch). Update mask to control which fields to update. Example field paths: `name`, `displayName`. * If `update_mask` is non-empty, then only the fields specified in the `update_mask` are updated. * If you specify a field in the `update_mask`, but don't specify its value in the source, that field is cleared. * If the `update_mask` is not present or empty or has the value `*`, then all fields are updated.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "UpdateSchemaRequest": {"id": "UpdateSchemaRequest", "properties": {"debugOptions": {"$ref": "DebugOptions", "description": "Common debug options."}, "schema": {"$ref": "<PERSON><PERSON><PERSON>", "description": "The new schema for the source."}, "validateOnly": {"description": "If true, the schema will be checked for validity, but will not be registered with the data source, even if valid.", "type": "boolean"}}, "type": "object"}, "UploadItemRef": {"description": "Represents an upload session reference. This reference is created via upload method. This reference is valid for 30 days after its creation. Updating of item content may refer to this uploaded content via contentDataRef.", "id": "UploadItemRef", "properties": {"name": {"description": "The name of the content reference. The maximum length is 2048 characters.", "type": "string"}}, "type": "object"}, "UserActivity": {"description": "User's single or bulk query activity. This can be a logging query or deletion query.", "id": "UserActivity", "properties": {"queryActivity": {"$ref": "QueryActivity", "description": "Contains data which needs to be logged/removed."}}, "type": "object"}, "VPCSettings": {"id": "VPCSettings", "properties": {"project": {"description": "The resource name of the GCP Project to be used for VPC SC policy check. VPC security settings on this project will be honored for Cloud Search APIs after project_name has been updated through CustomerService. Format: projects/{project_id}", "type": "string"}}, "type": "object"}, "Value": {"description": "Definition of a single value with generic type.", "id": "Value", "properties": {"booleanValue": {"type": "boolean"}, "dateValue": {"$ref": "Date"}, "doubleValue": {"format": "double", "type": "number"}, "integerValue": {"format": "int64", "type": "string"}, "stringValue": {"type": "string"}, "timestampValue": {"format": "google-datetime", "type": "string"}}, "type": "object"}, "ValueFilter": {"id": "ValueFilter", "properties": {"operatorName": {"description": "The `operator_name` applied to the query, such as *price_greater_than*. The filter can work against both types of filters defined in the schema for your data source: 1. `operator_name`, where the query filters results by the property that matches the value. 2. `greater_than_operator_name` or `less_than_operator_name` in your schema. The query filters the results for the property values that are greater than or less than the supplied value in the query.", "type": "string"}, "value": {"$ref": "Value", "description": "The value to be compared with."}}, "type": "object"}}, "servicePath": "", "title": "Cloud Search API", "version": "v1", "version_module": true}
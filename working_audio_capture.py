"""
Working Audio Capture System
Using Intel Smart Microphone Array (Device 2) that successfully captures audio
"""

import time
import numpy as np
import threading
from datetime import datetime

class WorkingAudioCapture:
    def __init__(self):
        self.audio_device = 2  # Intel Smart Microphone Array
        self.is_recording = False
        self.audio_buffer = []
        self.audio_thread = None
        
    def capture_single_audio(self, duration=3):
        """Capture a single audio sample"""
        try:
            import sounddevice as sd
            
            print(f"🎵 Capturing {duration} seconds of audio...")
            
            audio_data = sd.rec(int(duration * 44100), 
                               samplerate=44100, 
                               channels=2, 
                               device=self.audio_device,
                               dtype='float32')
            sd.wait()
            
            # Analyze the audio
            volume_rms = np.sqrt(np.mean(audio_data**2))
            volume_max = np.max(np.abs(audio_data))
            
            # Categorize audio level
            if volume_rms > 0.01:
                level = "🔊 High volume audio detected"
            elif volume_rms > 0.005:
                level = "🔉 Moderate volume audio detected"
            elif volume_rms > 0.001:
                level = "🔈 Low volume audio detected"
            else:
                level = "🔇 Very quiet/no audio"
            
            return {
                'timestamp': datetime.now().strftime("%H:%M:%S"),
                'volume_rms': volume_rms,
                'volume_max': volume_max,
                'level': level,
                'raw_data': audio_data
            }
            
        except Exception as e:
            return {
                'timestamp': datetime.now().strftime("%H:%M:%S"),
                'error': f"Audio capture failed: {e}",
                'level': "❌ Audio capture error"
            }
    
    def start_continuous_monitoring(self):
        """Start continuous audio monitoring in background"""
        if self.is_recording:
            print("⚠️ Audio monitoring already running")
            return
        
        self.is_recording = True
        self.audio_buffer = []
        self.audio_thread = threading.Thread(target=self._continuous_monitor)
        self.audio_thread.daemon = True
        self.audio_thread.start()
        print("🎵 Started continuous audio monitoring...")
    
    def stop_continuous_monitoring(self):
        """Stop continuous audio monitoring"""
        self.is_recording = False
        if self.audio_thread:
            self.audio_thread.join(timeout=2)
        print("🛑 Stopped continuous audio monitoring")
    
    def _continuous_monitor(self):
        """Background thread for continuous audio monitoring"""
        try:
            import sounddevice as sd
            
            print("🎵 Background audio monitoring active...")
            
            while self.is_recording:
                try:
                    # Capture 2-second chunks
                    audio_data = sd.rec(int(2 * 44100), 
                                       samplerate=44100, 
                                       channels=2, 
                                       device=self.audio_device,
                                       dtype='float32')
                    sd.wait()
                    
                    # Analyze
                    volume_rms = np.sqrt(np.mean(audio_data**2))
                    
                    # Categorize
                    if volume_rms > 0.01:
                        level = "🔊 High volume"
                    elif volume_rms > 0.005:
                        level = "🔉 Moderate volume"
                    elif volume_rms > 0.001:
                        level = "🔈 Low volume"
                    else:
                        level = "🔇 Quiet"
                    
                    # Add to buffer (keep last 10)
                    self.audio_buffer.append({
                        'timestamp': datetime.now().strftime("%H:%M:%S"),
                        'volume': volume_rms,
                        'level': level
                    })
                    
                    # Keep buffer size manageable
                    if len(self.audio_buffer) > 10:
                        self.audio_buffer.pop(0)
                    
                except Exception as e:
                    print(f"⚠️ Audio monitoring error: {e}")
                    time.sleep(1)
                    
        except Exception as e:
            print(f"❌ Continuous monitoring failed: {e}")
    
    def get_recent_audio_summary(self):
        """Get summary of recent audio activity"""
        if not self.audio_buffer:
            return "🔇 No recent audio data"
        
        # Get last 3 audio samples
        recent = self.audio_buffer[-3:]
        summary = []
        
        for audio in recent:
            summary.append(f"{audio['level']} ({audio['timestamp']})")
        
        return " | ".join(summary)
    
    def test_audio_capture(self):
        """Test the audio capture system"""
        print("🎯 TESTING WORKING AUDIO CAPTURE SYSTEM")
        print("=" * 50)
        
        # Test single capture
        print("1. Testing single audio capture...")
        result = self.capture_single_audio(3)
        
        if 'error' in result:
            print(f"❌ {result['error']}")
            return False
        else:
            print(f"✅ {result['level']}")
            print(f"   RMS Volume: {result['volume_rms']:.6f}")
            print(f"   Max Volume: {result['volume_max']:.6f}")
        
        # Test continuous monitoring
        print("\n2. Testing continuous monitoring (10 seconds)...")
        self.start_continuous_monitoring()
        
        for i in range(10, 0, -1):
            print(f"   Monitoring... {i} seconds remaining")
            time.sleep(1)
        
        self.stop_continuous_monitoring()
        
        # Show results
        print(f"\n📊 Recent Audio Summary:")
        print(f"   {self.get_recent_audio_summary()}")
        
        return True

def main():
    print("🎯 WORKING AUDIO CAPTURE SYSTEM")
    print("=" * 60)
    print("✅ Using Intel Smart Microphone Array (Device 2)")
    print("🔊 Make sure you have audio playing for best results")
    print("=" * 60)
    
    audio_system = WorkingAudioCapture()
    
    while True:
        print("\nChoose test:")
        print("1. Single Audio Capture (3 seconds)")
        print("2. Continuous Monitoring Test (10 seconds)")
        print("3. Full System Test")
        print("4. Exit")
        
        choice = input("Enter choice (1-4): ").strip()
        
        if choice == '1':
            result = audio_system.capture_single_audio(3)
            if 'error' in result:
                print(f"❌ {result['error']}")
            else:
                print(f"✅ {result['level']}")
                print(f"   Volume: {result['volume_rms']:.6f}")
        
        elif choice == '2':
            print("🎵 Starting 10-second continuous monitoring...")
            audio_system.start_continuous_monitoring()
            time.sleep(10)
            audio_system.stop_continuous_monitoring()
            print(f"📊 Summary: {audio_system.get_recent_audio_summary()}")
        
        elif choice == '3':
            audio_system.test_audio_capture()
        
        elif choice == '4':
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice")

if __name__ == "__main__":
    main()

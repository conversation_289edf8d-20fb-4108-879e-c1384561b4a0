"""
Windows WASAPI System Audio Capture Test
Using the best method for Windows system audio capture
"""

import time
import numpy as np

def test_wasapi_loopback():
    """Test WASAPI loopback - the gold standard for Windows system audio"""
    print("🎵 Testing WASAPI Loopback (Best Method for Windows)")
    try:
        import sounddevice as sd
        
        # Find WASAPI Stereo Mix device
        devices = sd.query_devices()
        wasapi_stereo_mix = None
        
        for i, device in enumerate(devices):
            device_name = str(device['name'])
            api_name = str(device.get('hostapi', ''))
            
            if ('Stereo Mix' in device_name and 
                'WASAPI' in str(sd.query_hostapis()[device['hostapi']]['name']) and
                device['max_input_channels'] > 0):
                wasapi_stereo_mix = i
                print(f"✅ Found WASAPI Stereo Mix: Device {i} - {device_name}")
                break
        
        if wasapi_stereo_mix is None:
            print("❌ WASAPI Stereo Mix not found")
            return False
        
        # Test recording with different settings
        print("🎵 Recording 5 seconds with WASAPI...")
        duration = 5
        sample_rate = 44100
        
        # Record with specific device
        audio_data = sd.rec(int(duration * sample_rate), 
                           samplerate=sample_rate, 
                           channels=2, 
                           device=wasapi_stereo_mix,
                           dtype='float32')
        
        print("⏳ Recording... (make sure audio is playing!)")
        sd.wait()
        
        # Analyze the audio
        volume_rms = np.sqrt(np.mean(audio_data**2))
        volume_max = np.max(np.abs(audio_data))
        volume_avg = np.mean(np.abs(audio_data))
        
        print(f"📊 Audio Analysis:")
        print(f"   RMS Volume: {volume_rms:.6f}")
        print(f"   Max Volume: {volume_max:.6f}")
        print(f"   Avg Volume: {volume_avg:.6f}")
        
        # Check if we got actual audio
        if volume_rms > 0.001:
            print("🎉 SUCCESS: System audio captured!")
            
            # Analyze frequency content
            fft = np.fft.fft(audio_data[:, 0])  # Use first channel
            freqs = np.fft.fftfreq(len(fft), 1/sample_rate)
            magnitude = np.abs(fft)
            
            # Find dominant frequency
            dominant_freq_idx = np.argmax(magnitude[:len(magnitude)//2])
            dominant_freq = freqs[dominant_freq_idx]
            
            print(f"🎼 Dominant frequency: {dominant_freq:.1f} Hz")
            
            return True
        else:
            print("❌ No significant audio detected")
            return False
            
    except Exception as e:
        print(f"❌ WASAPI test failed: {e}")
        return False

def test_alternative_wasapi():
    """Test alternative WASAPI devices"""
    print("\n🎵 Testing Alternative WASAPI Devices")
    try:
        import sounddevice as sd
        
        devices = sd.query_devices()
        hostapis = sd.query_hostapis()
        
        # Find all WASAPI input devices
        wasapi_devices = []
        for i, device in enumerate(devices):
            hostapi = hostapis[device['hostapi']]
            if 'WASAPI' in hostapi['name'] and device['max_input_channels'] > 0:
                wasapi_devices.append((i, device))
        
        print(f"Found {len(wasapi_devices)} WASAPI input devices:")
        for i, (device_idx, device) in enumerate(wasapi_devices):
            print(f"  {i+1}. Device {device_idx}: {device['name']}")
        
        # Test each WASAPI device
        for device_idx, device in wasapi_devices:
            if 'Stereo Mix' in device['name'] or 'Loopback' in device['name']:
                print(f"\n🎵 Testing {device['name']}...")
                
                try:
                    # Quick 2-second test
                    audio_data = sd.rec(int(2 * 44100), 
                                       samplerate=44100, 
                                       channels=min(2, device['max_input_channels']), 
                                       device=device_idx,
                                       dtype='float32')
                    sd.wait()
                    
                    volume = np.sqrt(np.mean(audio_data**2))
                    print(f"   Volume: {volume:.6f}")
                    
                    if volume > 0.001:
                        print(f"✅ SUCCESS with {device['name']}!")
                        return device_idx
                        
                except Exception as e:
                    print(f"   ❌ Failed: {e}")
        
        return None
        
    except Exception as e:
        print(f"❌ Alternative WASAPI test failed: {e}")
        return None

def test_pyaudio_wasapi():
    """Test PyAudio with WASAPI"""
    print("\n🎵 Testing PyAudio with WASAPI")
    try:
        import pyaudio
        
        p = pyaudio.PyAudio()
        
        # Look for WASAPI devices
        wasapi_devices = []
        for i in range(p.get_device_count()):
            try:
                info = p.get_device_info_by_index(i)
                if 'WASAPI' in str(info.get('name', '')) and info['maxInputChannels'] > 0:
                    wasapi_devices.append((i, info))
            except:
                continue
        
        print(f"Found {len(wasapi_devices)} PyAudio WASAPI devices")
        
        # Try Stereo Mix specifically
        for device_idx, info in wasapi_devices:
            if 'Stereo Mix' in info['name']:
                print(f"🎵 Testing PyAudio with {info['name']}...")
                
                try:
                    CHUNK = 1024
                    FORMAT = pyaudio.paFloat32
                    CHANNELS = 2
                    RATE = 44100
                    RECORD_SECONDS = 3
                    
                    stream = p.open(format=FORMAT,
                                   channels=CHANNELS,
                                   rate=RATE,
                                   input=True,
                                   input_device_index=device_idx,
                                   frames_per_buffer=CHUNK)
                    
                    frames = []
                    for _ in range(0, int(RATE / CHUNK * RECORD_SECONDS)):
                        data = stream.read(CHUNK, exception_on_overflow=False)
                        frames.append(data)
                    
                    stream.stop_stream()
                    stream.close()
                    
                    # Analyze
                    audio_data = np.frombuffer(b''.join(frames), dtype=np.float32)
                    volume = np.sqrt(np.mean(audio_data**2))
                    print(f"   Volume: {volume:.6f}")
                    
                    if volume > 0.001:
                        print("✅ SUCCESS with PyAudio WASAPI!")
                        p.terminate()
                        return True
                        
                except Exception as e:
                    print(f"   ❌ Failed: {e}")
        
        p.terminate()
        return False
        
    except Exception as e:
        print(f"❌ PyAudio WASAPI test failed: {e}")
        return False

def main():
    print("🎯 WINDOWS WASAPI SYSTEM AUDIO TEST")
    print("=" * 60)
    print("🔊 CRITICAL: Make sure you have LOUD audio playing!")
    print("   - Play YouTube video at HIGH volume")
    print("   - Play music loudly")
    print("   - Make sure speakers/headphones are working")
    print("   - Test that you can hear the audio clearly")
    print("=" * 60)
    
    input("Press Enter when you have LOUD audio playing...")
    
    success = False
    
    # Test methods in order of reliability
    if test_wasapi_loopback():
        success = True
    elif test_alternative_wasapi():
        success = True
    elif test_pyaudio_wasapi():
        success = True
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SUCCESS! We found a working method to capture system audio!")
        print("✅ Ready to integrate with your screen monitoring system")
    else:
        print("❌ TROUBLESHOOTING NEEDED:")
        print("1. Right-click speaker icon → Open Sound settings")
        print("2. Go to Sound Control Panel → Recording tab")
        print("3. Right-click empty space → Show Disabled Devices")
        print("4. Enable 'Stereo Mix' if it appears")
        print("5. Set Stereo Mix as default recording device")
        print("6. Make sure audio is playing LOUDLY")
        print("7. Try running as Administrator")

if __name__ == "__main__":
    main()

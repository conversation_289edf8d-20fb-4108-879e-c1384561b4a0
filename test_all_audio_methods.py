"""
Comprehensive Audio Capture Test
Try all available methods to capture system audio on Windows
"""

import time
import numpy as np
import sys

def test_method_1_default_input():
    """Test using default input device"""
    print("🎵 Method 1: Default Input Device")
    try:
        import sounddevice as sd
        
        # Use default input device
        print("   Using default input device...")
        duration = 3
        sample_rate = 44100
        
        audio_data = sd.rec(int(duration * sample_rate), 
                           samplerate=sample_rate, 
                           channels=2,
                           dtype='float32')
        sd.wait()
        
        volume = np.sqrt(np.mean(audio_data**2))
        print(f"   Volume: {volume:.6f}")
        
        if volume > 0.001:
            print("   ✅ SUCCESS: Audio detected!")
            return True
        else:
            print("   ❌ No significant audio")
            return False
            
    except Exception as e:
        print(f"   ❌ Failed: {e}")
        return False

def test_method_2_all_input_devices():
    """Test all available input devices"""
    print("\n🎵 Method 2: Test All Input Devices")
    try:
        import sounddevice as sd
        
        devices = sd.query_devices()
        input_devices = [(i, dev) for i, dev in enumerate(devices) if dev['max_input_channels'] > 0]
        
        print(f"   Found {len(input_devices)} input devices")
        
        for device_idx, device in input_devices:
            device_name = device['name'][:30]  # Truncate long names
            print(f"   Testing Device {device_idx}: {device_name}...")
            
            try:
                # Quick 1-second test
                audio_data = sd.rec(44100, 
                                   samplerate=44100, 
                                   channels=min(2, device['max_input_channels']), 
                                   device=device_idx,
                                   dtype='float32')
                sd.wait()
                
                volume = np.sqrt(np.mean(audio_data**2))
                print(f"      Volume: {volume:.6f}")
                
                if volume > 0.001:
                    print(f"   ✅ SUCCESS with {device['name']}!")
                    return device_idx
                    
            except Exception as e:
                print(f"      ❌ Failed: {str(e)[:30]}...")
        
        return None
        
    except Exception as e:
        print(f"   ❌ Method failed: {e}")
        return None

def test_method_3_pycaw():
    """Test using PyCaw for Windows audio"""
    print("\n🎵 Method 3: PyCaw Windows Audio")
    try:
        from pycaw.pycaw import AudioUtilities, AudioSession
        import sounddevice as sd
        
        # Get all audio sessions
        sessions = AudioUtilities.GetAllSessions()
        print(f"   Found {len(sessions)} audio sessions")
        
        for session in sessions:
            if session.Process and session.Process.name():
                print(f"   Audio from: {session.Process.name()}")
        
        # Try to capture using default device but with PyCaw info
        print("   Attempting capture with PyCaw context...")
        
        audio_data = sd.rec(int(3 * 44100), 
                           samplerate=44100, 
                           channels=2,
                           dtype='float32')
        sd.wait()
        
        volume = np.sqrt(np.mean(audio_data**2))
        print(f"   Volume: {volume:.6f}")
        
        if volume > 0.001:
            print("   ✅ SUCCESS with PyCaw context!")
            return True
        else:
            print("   ❌ No audio detected")
            return False
            
    except Exception as e:
        print(f"   ❌ PyCaw failed: {e}")
        return False

def test_method_4_pyaudio_all_devices():
    """Test PyAudio with all devices"""
    print("\n🎵 Method 4: PyAudio All Devices")
    try:
        import pyaudio
        
        p = pyaudio.PyAudio()
        
        print(f"   Found {p.get_device_count()} PyAudio devices")
        
        for i in range(p.get_device_count()):
            try:
                info = p.get_device_info_by_index(i)
                if info['maxInputChannels'] > 0:
                    device_name = info['name'][:30]
                    print(f"   Testing Device {i}: {device_name}...")
                    
                    try:
                        CHUNK = 1024
                        FORMAT = pyaudio.paFloat32
                        CHANNELS = min(2, info['maxInputChannels'])
                        RATE = int(info['defaultSampleRate'])
                        RECORD_SECONDS = 2
                        
                        stream = p.open(format=FORMAT,
                                       channels=CHANNELS,
                                       rate=RATE,
                                       input=True,
                                       input_device_index=i,
                                       frames_per_buffer=CHUNK)
                        
                        frames = []
                        for _ in range(0, int(RATE / CHUNK * RECORD_SECONDS)):
                            data = stream.read(CHUNK, exception_on_overflow=False)
                            frames.append(data)
                        
                        stream.stop_stream()
                        stream.close()
                        
                        audio_data = np.frombuffer(b''.join(frames), dtype=np.float32)
                        volume = np.sqrt(np.mean(audio_data**2))
                        print(f"      Volume: {volume:.6f}")
                        
                        if volume > 0.001:
                            print(f"   ✅ SUCCESS with {info['name']}!")
                            p.terminate()
                            return i
                            
                    except Exception as e:
                        print(f"      ❌ Failed: {str(e)[:30]}...")
                        
            except:
                continue
        
        p.terminate()
        return None
        
    except Exception as e:
        print(f"   ❌ PyAudio method failed: {e}")
        return None

def test_method_5_microphone_as_system():
    """Test if microphone is picking up system audio (speakers bleeding into mic)"""
    print("\n🎵 Method 5: Microphone Picking Up System Audio")
    try:
        import sounddevice as sd
        
        # Find microphone devices
        devices = sd.query_devices()
        mic_devices = []
        
        for i, device in enumerate(devices):
            device_name = str(device['name']).lower()
            if ('microphone' in device_name or 'mic' in device_name) and device['max_input_channels'] > 0:
                mic_devices.append((i, device))
        
        print(f"   Found {len(mic_devices)} microphone devices")
        
        for device_idx, device in mic_devices:
            print(f"   Testing {device['name'][:30]}...")
            
            try:
                audio_data = sd.rec(int(3 * 44100), 
                                   samplerate=44100, 
                                   channels=min(2, device['max_input_channels']), 
                                   device=device_idx,
                                   dtype='float32')
                sd.wait()
                
                volume = np.sqrt(np.mean(audio_data**2))
                print(f"      Volume: {volume:.6f}")
                
                if volume > 0.01:  # Higher threshold for mic
                    print(f"   ✅ SUCCESS: Microphone picking up system audio!")
                    return device_idx
                    
            except Exception as e:
                print(f"      ❌ Failed: {str(e)[:30]}...")
        
        return None
        
    except Exception as e:
        print(f"   ❌ Microphone method failed: {e}")
        return None

def main():
    print("🎯 COMPREHENSIVE AUDIO CAPTURE TEST")
    print("=" * 60)
    print("🔊 CRITICAL: Make sure you have VERY LOUD audio playing!")
    print("   - YouTube video at MAXIMUM volume")
    print("   - Music playing at full volume")
    print("   - Any system sound that's clearly audible")
    print("   - If using headphones, try switching to speakers")
    print("=" * 60)
    
    input("Press Enter when you have LOUD audio playing...")
    
    methods = [
        test_method_1_default_input,
        test_method_2_all_input_devices,
        test_method_3_pycaw,
        test_method_4_pyaudio_all_devices,
        test_method_5_microphone_as_system
    ]
    
    successful_methods = []
    
    for method in methods:
        try:
            result = method()
            if result:
                successful_methods.append((method.__name__, result))
        except Exception as e:
            print(f"❌ {method.__name__} crashed: {e}")
        
        print("-" * 40)
    
    print("\n🎯 FINAL RESULTS:")
    print("=" * 60)
    
    if successful_methods:
        print("✅ SUCCESSFUL METHODS:")
        for method_name, result in successful_methods:
            print(f"   • {method_name}: {result}")
        
        print("\n🎉 WE FOUND WORKING AUDIO CAPTURE!")
        print("✅ Ready to create the integrated system!")
        
    else:
        print("❌ NO METHODS WORKED")
        print("\n🔧 FINAL TROUBLESHOOTING:")
        print("1. Check Windows Privacy Settings:")
        print("   Settings → Privacy → Microphone → Allow apps to access")
        print("2. Try running as Administrator")
        print("3. Check if any antivirus is blocking audio access")
        print("4. Try with different audio sources (VLC, Windows Media Player)")
        print("5. Restart computer and try again")
        print("6. Update audio drivers")

if __name__ == "__main__":
    main()
